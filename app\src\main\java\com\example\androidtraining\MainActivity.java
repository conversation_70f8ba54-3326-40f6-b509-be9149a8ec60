package com.example.androidtraining;

import android.Manifest;
import android.app.Activity;
import android.app.AlarmManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.widget.Button;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.example.androidtraining.app.AppLifecycleHandler;
import com.example.androidtraining.course.CourseActivity;
import com.example.androidtraining.utils.Logger;
import com.google.android.material.snackbar.Snackbar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MainActivity extends BaseActivity {
    private static final String TAG = MainActivity.class.getSimpleName();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_main;
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate");

//        EdgeToEdge.enable(this);
//        setContentView(R.layout.activity_main);
//        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
//            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
//            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
//            return insets;
//        });

        Button btnStartCourse = findViewById(R.id.btn_start_course);
        btnStartCourse.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, CourseActivity.class);
            startActivity(intent);
        });

        // request storage permission
        // API >= 30: MANAGE_EXTERNAL_STORAGE & "All files access" -
        requestStoragePermission();
        requestScheduleExactAlarmPermission();
        requestAppPermissions();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Logger.d(TAG, "onResume: isAppRunning=" + AppLifecycleHandler.isAppRunning(this));
    }

    private void requestStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R /*API 30*/) {
            Logger.d(TAG, "requestStoragePermission, API >= 30: isExternalStorageManager() - " + Environment.isExternalStorageManager());
            if (!Environment.isExternalStorageManager()) {
                Snackbar.make(findViewById(android.R.id.content), "Permission needed!", Snackbar.LENGTH_INDEFINITE)
                        .setAction("Settings", v -> {
                            try {
                                Uri uri = Uri.parse("package:" + BuildConfig.APPLICATION_ID);
                                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION, uri);
                                startActivityForResult(intent, 200);
                            } catch (Exception ex) {
                                Intent intent = new Intent();
                                intent.setAction(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                                startActivityForResult(intent, 200);
                            }
                        })
                        .show();
            }
        } else {
            boolean hasAllStoragePermissions = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                    && ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
            Logger.d(TAG, "requestStoragePermission, API < 30: hasAllStoragePermissions=" + hasAllStoragePermissions);
            if (!hasAllStoragePermissions) {
                String[] permissions = {Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE};
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    requestPermissions(permissions, 100);
                } else {
                    ActivityCompat.requestPermissions(this, permissions, 100);
                }
            }
        }
    }

    private void requestScheduleExactAlarmPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            AlarmManager alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
            Logger.d(TAG, "requestScheduleExactAlarmPermission: canScheduleExactAlarms=" + alarmManager.canScheduleExactAlarms());
//            if (ContextCompat.checkSelfPermission(this, Manifest.permission.SCHEDULE_EXACT_ALARM) != PackageManager.PERMISSION_GRANTED) {
            if (!alarmManager.canScheduleExactAlarms()) {
                Intent intent = new Intent();
                intent.setAction(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM);
                intent.setData(Uri.parse("package:" + getPackageName()));
                startActivityForResult(intent, 201);
            }
        }
    }

    private void requestAppPermissions() {
        List<String> allAppPermissions = new ArrayList<>();
        allAppPermissions.add(Manifest.permission.SET_ALARM);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            allAppPermissions.add(Manifest.permission.USE_EXACT_ALARM);
        }
        boolean hasAllPermissions = true;
        List<String> deniedPermissions = new ArrayList<>();
        for (String permission : allAppPermissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                Logger.d(TAG, "requestAppPermissions, permission is not granted: " + permission);
                hasAllPermissions = false;
                deniedPermissions.add(permission);
            }
        }
        if (!hasAllPermissions) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                requestPermissions(deniedPermissions.toArray(new String[0]), 101);
            } else {
                ActivityCompat.requestPermissions(this, deniedPermissions.toArray(new String[0]), 101);
            }
        } else {
            Logger.d(TAG, "requestAppPermissions, allAppPermissions are granted");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 100 || requestCode == 101) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Logger.d(TAG, "onRequestPermissionsResult: Permission is granted, " + Arrays.toString(permissions));
            } else {
                Logger.d(TAG, "onRequestPermissionsResult: Permission is denied, " +  Arrays.toString(permissions));
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Logger.d(TAG, "onActivityResult: requestCode = " + requestCode + ", resultCode = " + resultCode + ", intent:" + data);
        if (resultCode == Activity.RESULT_OK) {

        }
    }
}