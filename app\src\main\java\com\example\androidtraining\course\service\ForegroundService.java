package com.example.androidtraining.course.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;

import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;

public class ForegroundService extends Service {
    private static final String TAG = ForegroundService.class.getSimpleName();

    public static final String ACTION_SEND_COUNT = "com.example.androidtraining.SERVICE_SEND_COUNT";
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.d(TAG, "onCreate count = " + mCount + ", isRunning=" + mIsRunning);
        int NOTIFICATION_ID = 10;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForeground(NOTIFICATION_ID, createNotificationWithChanel());
        } else {
            startForeground(NOTIFICATION_ID, new Notification());
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private Notification createNotificationWithChanel() {
        String CHANEL_ID = "ServiceChanel";
        NotificationManager manager = getSystemService(NotificationManager.class);
        NotificationChannel channel = new NotificationChannel(CHANEL_ID, "Service", NotificationManager.IMPORTANCE_LOW);
        manager.createNotificationChannel(channel);
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher_round)
                .setContentTitle("Foreground service 1")
                .setContentText("Foreground service is running for >= O os")
                .setPriority(NotificationManager.IMPORTANCE_HIGH)
                .setCategory(Notification.CATEGORY_SERVICE);
        return builder.build();
    }

    private int mCount = 0;
    private Thread mThread;
    private boolean mIsRunning = false;

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.d(TAG, "onStartCommand: intent = " + intent + " thread=" + Thread.currentThread().getName() + ", isRunning=" + mIsRunning);
        String mess = mIsRunning ? "ForegroundService is running" : "ForegroundService start running";
        Toast.makeText(this, mess, Toast.LENGTH_LONG).show();
        if (!mIsRunning) {
            mThread = new Thread(() -> {
                while (true) {
                    mCount += 1;
                    mIsRunning = true;
                    Logger.d(TAG, "count = " + mCount + " thread=" + Thread.currentThread().getName());
                    Intent intent1 = new Intent(ACTION_SEND_COUNT);
                    intent1.putExtra("count", mCount);
                    sendBroadcast(intent1);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Logger.d(TAG, "thread is interrupted: break - " + e);
                        mIsRunning = false;
                        break;
                    }
                    if (mCount > 300) {
                        mIsRunning = false;
                        break;
                    }
                }
            });
            mThread.start();
        }
        return START_STICKY;
//        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        Logger.d(TAG, "onDestroy count = " + mCount);
        Toast.makeText(this, "Service is killed", Toast.LENGTH_LONG).show();
        if (mThread != null && mThread.isAlive()) {
            try {
                mThread.interrupt();
                Logger.d(TAG, "onDestroy interrupt thread");
            } catch (Exception e) {
                Logger.d(TAG, "onDestroy interrupt ex = " + e);
            }
        }
        super.onDestroy();
    }
}
