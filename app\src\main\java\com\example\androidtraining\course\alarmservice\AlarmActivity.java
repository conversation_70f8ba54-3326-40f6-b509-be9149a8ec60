package com.example.androidtraining.course.alarmservice;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class AlarmActivity extends BaseActivity {
    private static final String TAG = AlarmActivity.class.getSimpleName();

    private AlarmManager alarmManager;
    private Map<Integer, PendingIntent> mapPendingIntents;
    private int alarmId;
    /*
    * Từ Android M thì Google đã có sự thay đổi với AlarmManager để hạn chế việc tiêu thụ pin quá mức.
    * Alarm sẽ không còn được ưu tiên bật chính xác vào thời điểm đã đặt, mà tùy thuộc vào tình hình tài nguyên máy thời điểm đó mà thời gian bật alarm sẽ có sai lệch chút ít.
    * Có một vấn đề nhức đầu phổ biến của các nhà phát triển ứng dụng, đó là AlarmManager không hề lưu lại các lịch đã đặt sau khi thiết bị khởi động lại.
    *
    * Từ Android M, Google có thêm khái niệm Doze và App Standby. Hai chế độ này sẽ hủy các alarm được lên lịch trước đó khi thiết bị không được cắm sạc.
    * Khi người dùng tắt màn hình hoặc không cắm sạc, thiết bị sẽ chuyển sang chế độ Doze.
    * Trong chế độ Doze, Android sẽ hạn chế thiết bị truy cập vào mạng và dịch vụ sử dụng nhiều CPU.
    * Do đó, các alarm cũng sẽ bị hủy hoặc bị hoãn cho tới khi thiết bị sẵn sàng.
    *
    * Android sẽ định kỳ thoát và vào lại chế độ Doze, gọi là khoảng thời gian bảo trì.
    * Trong thời gian bảo trì này, tất cả các hạn chế trước đó sẽ được giải phóng. Tức là các yêu cầu truy cập mạng sẽ được phép, các alarm được kích hoạt.
    * Chế độ App Standby cũng tương tự chế độ Doze, chỉ khác là màn hình không bắt buộc phải tắt.
    *
    * 2 ways to set alarm:
    *   - exact alarms: can be scheduled for a specific time and will wake up the device
    *       + setExact(): except battery-saver mode and doze mode on.
    *       + setExactAndAllowWhileIdle(): báo chính xác ngay cả khi đang ở chế độ nghỉ - Doze mode
    *       + setAlarmClock(): priority highest
    *   - not-exact alarms: can be delayed or grouped together to improve battery life
    *       + setRepeating():
    *       + setInexactRepeating():
    *
    * 2 types:
    *   - ELAPSED_REALTIME_WAKEUP: elapsed time since boot (including time spent in sleep)
    *   - RTC_WAKEUP: real time
    *
    * <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    *   Android 12 this permission is automatically granted by the Android system but on
    *   Android 13 you need to check if the user has granted this permission. - T os, API 33
    *
    * <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> - not need request permisison
    *   Since Android 13, the USE_EXACT_ALARM is available, Note that only one of USE_EXACT_ALARM or SCHEDULE_EXACT_ALARM should be requested on a device.
    *   Only "Calendar" or "Wake Up" apps now are allowed to use the USE_EXACT_ALARM permission. - categorized as "Calendar and alarm clock app"
    *
    *   This is no longer allowed for OS 14, as the app will be rejected by play store.
    * */

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_alarm;
        super.onCreate(savedInstanceState);

        mapPendingIntents = new HashMap<>();
        alarmId = 0;

        alarmManager = (AlarmManager) getSystemService(ALARM_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            Logger.d(TAG, "onCreate: canScheduleExactAlarms=" + alarmManager.canScheduleExactAlarms());
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            alarmManager.cancelAll();
        }

        Button btnExact = findViewById(R.id.btn_exact_not_repeat);
        btnExact.setOnClickListener(v -> {
            alarmId++;
            // cancel alarm if it is exist before setting a new one
            PendingIntent alarmIntent = mapPendingIntents.get(alarmId);
//            if (alarmIntent != null) {
//                alarmManager.cancel(alarmIntent);
//                Logger.d(TAG, "btnExact onClick: cancel alarmIntent: " + alarmIntent);
//            }

            Date date = new Date();
            int hour = date.getHours();
            int minute = date.getMinutes() + 1;
            if (minute >= 60) hour += 1;
            minute = minute % 60;

            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(System.currentTimeMillis());
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            Intent intent = new Intent(this, AlarmReceiver.class);
            intent.setAction(AlarmConstants.ACTION_ALARM);
            intent.putExtra(AlarmConstants.ALARM_HOUR, hour);
            intent.putExtra(AlarmConstants.ALARM_MIN, minute);
            intent.putExtra(AlarmConstants.ALARM_ID, alarmId);
            // PendingIntent.FLAG_CANCEL_CURRENT: only one 1 alarm is created/overwritten for the same alarmId
            alarmIntent = PendingIntent.getBroadcast(this, alarmId, intent, PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_CANCEL_CURRENT);
            Logger.d(TAG, "btnExact onClick: set alarm, hour: " + hour + ", minute=" + minute + ", alarmId=" + alarmId);
//            mapPendingIntents.put(alarmId, alarmIntent);

//            alarmManager.setExact(AlarmManager.ELAPSED_REALTIME_WAKEUP, SystemClock.elapsedRealtime() + 60 * 1000, alarmIntent);
//            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), alarmIntent);
            alarmManager.setAlarmClock(new AlarmManager.AlarmClockInfo(calendar.getTimeInMillis(), null), alarmIntent);
        });

        Button btnExactRepeat = findViewById(R.id.btn_exact_repeat);
        btnExactRepeat.setOnClickListener(v -> {
            alarmId++;
            // cancel alarm if it is exist before setting a new one
            PendingIntent alarmIntent = mapPendingIntents.get(alarmId);
//            if (alarmIntent != null) {
//                alarmManager.cancel(alarmIntent);
//                Logger.d(TAG, "btnExactRepeat onClick: cancel alarmIntent: " + alarmIntent);
//            }

            Date date = new Date();
            int hour = date.getHours();
            int minute = date.getMinutes() + 2;
            if (minute >= 60) hour += 1;
            minute = minute % 60;

            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(System.currentTimeMillis());
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
//            if (System.currentTimeMillis() > calendar.getTimeInMillis()) {
//                calendar.add(Calendar.DATE, 1); // next day
//            }

            int repeatCount = 3, repeatTime = 1;
            Logger.d(TAG, "btnExactRepeat - onClick: hour: " + hour + ", minute: " + minute + ", alarmId=" + alarmId);
            Intent intent = new Intent(this, AlarmReceiver.class);
            intent.setAction(AlarmConstants.ACTION_ALARM);
            intent.putExtra(AlarmConstants.ALARM_HOUR, hour);
            intent.putExtra(AlarmConstants.ALARM_MIN, minute);
            intent.putExtra(AlarmConstants.ALARM_REPEAT_COUNT, repeatCount);
            intent.putExtra(AlarmConstants.ALARM_REPEAT_TIME, repeatTime);
            intent.putExtra(AlarmConstants.ALARM_ID, alarmId);
            alarmIntent = PendingIntent.getBroadcast(this, alarmId, intent, PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_CANCEL_CURRENT);
//            mapPendingIntents.put(alarmId, alarmIntent);

            alarmManager.setRepeating(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), AlarmManager.INTERVAL_DAY, alarmIntent);
        });
    }
}