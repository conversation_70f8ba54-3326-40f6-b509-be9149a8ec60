#Wed Jul 09 21:01:49 ICT 2025
com.example.androidtraining.app-main-6\:/layout/item_gallery_image.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_gallery_image.xml
com.example.androidtraining.app-main-6\:/drawable-anydpi/ic_next.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-anydpi-v4\\ic_next.xml
com.example.androidtraining.app-main-6\:/layout/item_list_song.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_list_song.xml
com.example.androidtraining.app-main-6\:/layout/activity_example.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_example.xml
com.example.androidtraining.app-main-6\:/layout/activity_custom.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_custom.xml
com.example.androidtraining.app-main-6\:/drawable-xxhdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-xxhdpi-v4\\ic_next.png
com.example.androidtraining.app-main-6\:/drawable/ic_favorite_filled.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_favorite_filled.xml
com.example.androidtraining.app-main-6\:/drawable-xhdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-xhdpi-v4\\ic_next.png
com.example.androidtraining.app-main-6\:/drawable/ic_refresh.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_refresh.xml
com.example.androidtraining.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.androidtraining.app-main-6\:/xml/data_extraction_rules.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.example.androidtraining.app-main-6\:/layout/activity_course.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_course.xml
com.example.androidtraining.app-main-6\:/drawable/rounded_background.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_background.xml
com.example.androidtraining.app-main-6\:/drawable/ic_sort.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_sort.xml
com.example.androidtraining.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-6\:/drawable-v24/ic_launcher_foreground.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-v24\\ic_launcher_foreground.xml
com.example.androidtraining.app-main-6\:/drawable-mdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-mdpi-v4\\ic_arrow.png
com.example.androidtraining.app-main-6\:/layout/fragment_recycle_view.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_recycle_view.xml
com.example.androidtraining.app-main-6\:/drawable/ic_favorite_border.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_favorite_border.xml
com.example.androidtraining.app-main-6\:/layout/activity_share_prefs.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_share_prefs.xml
com.example.androidtraining.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-6\:/drawable/ic_image_placeholder.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_image_placeholder.xml
com.example.androidtraining.app-main-6\:/layout/fragment_top.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_top.xml
com.example.androidtraining.app-main-6\:/drawable/ic_share.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_share.xml
com.example.androidtraining.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.androidtraining.app-main-6\:/drawable-mdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-mdpi-v4\\ic_next.png
com.example.androidtraining.app-main-6\:/layout/fragment_list_view.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_list_view.xml
com.example.androidtraining.app-main-6\:/drawable/ic_filter.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_filter.xml
com.example.androidtraining.app-main-6\:/xml/backup_rules.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.androidtraining.app-main-6\:/layout/activity_thread.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_thread.xml
com.example.androidtraining.app-main-6\:/layout/fragment_bottom.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_bottom.xml
com.example.androidtraining.app-main-6\:/drawable/ic_delete.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete.xml
com.example.androidtraining.app-main-6\:/drawable-xxhdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-xxhdpi-v4\\ic_arrow.png
com.example.androidtraining.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-6\:/drawable-xhdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-xhdpi-v4\\ic_arrow.png
com.example.androidtraining.app-main-6\:/drawable-hdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-hdpi-v4\\ic_arrow.png
com.example.androidtraining.app-main-6\:/drawable/ic_select_all.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_select_all.xml
com.example.androidtraining.app-main-6\:/mipmap-mdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-6\:/menu/gallery_menu.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\gallery_menu.xml
com.example.androidtraining.app-main-6\:/layout/activity_adapter.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_adapter.xml
com.example.androidtraining.app-main-6\:/layout/fragment_one.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_one.xml
com.example.androidtraining.app-main-6\:/layout/activity_gallery.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_gallery.xml
com.example.androidtraining.app-main-6\:/drawable-anydpi/ic_arrow.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-anydpi-v4\\ic_arrow.xml
com.example.androidtraining.app-main-6\:/drawable/ic_search.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search.xml
com.example.androidtraining.app-main-6\:/mipmap-hdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-6\:/drawable-hdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-hdpi-v4\\ic_next.png
com.example.androidtraining.app-main-6\:/layout/notification_layout.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\notification_layout.xml
com.example.androidtraining.app-main-6\:/menu/image_detail_menu.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\image_detail_menu.xml
com.example.androidtraining.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-6\:/layout/activity_broadcast.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_broadcast.xml
com.example.androidtraining.app-main-6\:/layout/fragment_two.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_two.xml
com.example.androidtraining.app-main-6\:/layout/activity_notification.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_notification.xml
com.example.androidtraining.app-main-6\:/layout/activity_view_layout.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_view_layout.xml
com.example.androidtraining.app-main-6\:/layout/item_list_user.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_list_user.xml
com.example.androidtraining.app-main-6\:/layout/activity_main.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.example.androidtraining.app-main-6\:/layout/activity_alarm.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_alarm.xml
com.example.androidtraining.app-main-6\:/layout/view_layout_include.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\view_layout_include.xml
com.example.androidtraining.app-main-6\:/drawable/ic_launcher_background.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.androidtraining.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-6\:/layout/activity_database.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_database.xml
com.example.androidtraining.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-6\:/drawable/gradient_overlay.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_overlay.xml
com.example.androidtraining.app-main-6\:/layout/activity_snooze.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_snooze.xml
com.example.androidtraining.app-main-6\:/layout/activity_service.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_service.xml
com.example.androidtraining.app-main-6\:/layout/file_row.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\file_row.xml
com.example.androidtraining.app-main-6\:/layout/activity_image_detail.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_image_detail.xml
