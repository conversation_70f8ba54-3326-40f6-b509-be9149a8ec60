package com.example.androidtraining.course.model;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

public class MsgObject implements Parcelable {
    int code;
    String msg;

    public MsgObject(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public MsgObject(Parcel in) {
        readFromParcel(in);
    }

    public MsgObject() {

    }

    public void readFromParcel(Parcel in) {
        code = in.readInt();
        msg = in.readString();
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeInt(code);
        dest.writeString(msg);
    }
    @Override
    public int describeContents() {
        return 0;
    }
    public static final Creator<MsgObject> CREATOR = new Creator<MsgObject>() {
        @Override
        public MsgObject createFromParcel(Parcel in) {
            return new MsgObject(in);
        }

        @Override
        public MsgObject[] newArray(int size) {
            return new MsgObject[size];
        }
    };

    @Override
    public String toString() {
        return "MsgObject{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }
}
