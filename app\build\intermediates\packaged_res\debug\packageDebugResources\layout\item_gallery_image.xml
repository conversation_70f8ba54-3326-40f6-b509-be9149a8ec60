<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="120dp">

        <ImageView
            android:id="@+id/image_thumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@color/light_gray"
            tools:src="@drawable/ic_image_placeholder" />

        <!-- Selection overlay -->
        <View
            android:id="@+id/selection_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/selection_overlay"
            android:visibility="gone" />

        <!-- Selection checkbox -->
        <CheckBox
            android:id="@+id/checkbox_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|end"
            android:layout_margin="8dp"
            android:visibility="gone"
            android:buttonTint="@color/white" />

        <!-- Like indicator -->
        <ImageView
            android:id="@+id/icon_like"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="top|start"
            android:layout_margin="8dp"
            android:src="@drawable/ic_favorite_filled"
            android:tint="@color/red"
            android:visibility="gone" />

        <!-- Image info overlay -->
        <LinearLayout
            android:id="@+id/info_overlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@drawable/gradient_overlay"
            android:orientation="vertical"
            android:padding="4dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/text_image_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:text="IMG_20231201_123456.jpg" />

            <TextView
                android:id="@+id/text_image_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="8sp"
                tools:text="2.5 MB • 1920x1080" />

        </LinearLayout>

        <!-- Loading indicator -->
        <ProgressBar
            android:id="@+id/progress_loading"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>

</androidx.cardview.widget.CardView>
