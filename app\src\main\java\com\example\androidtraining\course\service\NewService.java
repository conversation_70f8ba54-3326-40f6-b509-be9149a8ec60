package com.example.androidtraining.course.service;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;

import androidx.annotation.Nullable;

public class NewService extends Service {

    class LocalBinder extends Binder {
        public NewService getService() { return NewService.this; }
    }

    // activity -> service:
    //      + startService(intent);
    //      + bindService(intent, connectionService, flag);
    //      + public method: LocalBinder
    // service -> activity: callback, broadcast, IPC

    LocalBinder localBinder = new LocalBinder();
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
//        return null;
        return localBinder;
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // worker thread
        Bundle bundle = intent.getExtras(); // data
        // do st
        return START_STICKY;
//        return super.onStartCommand(intent, flags, startId);
    }

    public void doSomething() {
        // count
    }

    @Override
    public boolean onUnbind(Intent intent) {

        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
