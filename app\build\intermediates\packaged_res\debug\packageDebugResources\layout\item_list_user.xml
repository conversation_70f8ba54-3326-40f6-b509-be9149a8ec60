<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_user_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    android:paddingTop="5dp"
    android:paddingBottom="5dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_item_user_vertical_15"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.15"
        />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_item_user_vertical_70"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.70"
        />

    <TextView
        android:id="@+id/tv_user_id"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="16dp"
        android:textAlignment="viewStart"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />

    <TextView
        android:id="@+id/tv_user_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="12dp"
        android:tooltipText="Name"
        android:textAlignment="viewStart"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/guideline_item_user_vertical_15"
        />

    <TextView
        android:id="@+id/tv_user_phone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="12dp"
        android:tooltipText="Phone"
        android:textAlignment="viewStart"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/guideline_item_user_vertical_70"
        />

</androidx.constraintlayout.widget.ConstraintLayout>