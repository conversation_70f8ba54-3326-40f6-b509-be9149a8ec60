{"logs": [{"outputFile": "com.example.androidtraining.app-mergeDebugResources-47:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\368b07b4e9b7912a6e22f44deed31ddf\\transformed\\browser-1.4.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "38,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3707,3805,3902,4011", "endColumns": "97,96,108,98", "endOffsets": "3800,3897,4006,4105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\3b1f9487fa18ad75d94f820ca6c18c8a\\transformed\\core-1.16.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "31,32,33,34,35,36,37,43", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2988,3084,3186,3285,3384,3488,3591,4193", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3079,3181,3280,3379,3483,3586,3702,4289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\e7f24da73f275a8a791d04076c131d9f\\transformed\\appcompat-1.7.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,4110", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,4188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\60115c31cdc3e8b7d77a5b34ad2884ea\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2762,2874", "endColumns": "111,113", "endOffsets": "2869,2983"}}]}]}