<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.broadcastreceiver.BroadcastActivity">

    <Button
        android:id="@+id/btn_send_user_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Send User action"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_send_new_action"
        />

    <Button
        android:id="@+id/btn_send_new_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Send New action"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <Button
        android:id="@+id/btn_send_local_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Send Local action"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_send_new_action"
        app:layout_constraintBottom_toBottomOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>