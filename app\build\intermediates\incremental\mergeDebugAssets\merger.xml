<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:object-detection-common:18.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11\transforms\48dce592930da5930d183f6f3d763055\transformed\jetified-object-detection-common-18.0.0\assets"><file name="mlkit_odt_localizer/localizer_with_validation.tflite" path="C:\Users\<USER>\.gradle\caches\8.11\transforms\48dce592930da5930d183f6f3d763055\transformed\jetified-object-detection-common-18.0.0\assets\mlkit_odt_localizer\localizer_with_validation.tflite"/><file name="mlkit_odt_localizer/mobile_object_localizer_3_1_anchors.pb" path="C:\Users\<USER>\.gradle\caches\8.11\transforms\48dce592930da5930d183f6f3d763055\transformed\jetified-object-detection-common-18.0.0\assets\mlkit_odt_localizer\mobile_object_localizer_3_1_anchors.pb"/><file name="mlkit_odt_localizer/mobile_object_localizer_labelmap" path="C:\Users\<USER>\.gradle\caches\8.11\transforms\48dce592930da5930d183f6f3d763055\transformed\jetified-object-detection-common-18.0.0\assets\mlkit_odt_localizer\mobile_object_localizer_labelmap"/></source></dataSet><dataSet config="com.google.mlkit:object-detection:17.0.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\assets"><file name="mlkit_odt_default_classifier/labeler_with_validation.tflite" path="C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\assets\mlkit_odt_default_classifier\labeler_with_validation.tflite"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>