package com.example.androidtraining.course.service;

import android.app.Service;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Process;

import androidx.annotation.Nullable;

import com.example.androidtraining.IRemoteServiceInterface;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.course.model.MsgObject;

/*
    1. Create the .aidl file:
        Create folder "src/aidl", then create new package for "aidl" folder: src/aidl/package/
        Create new .aidl file in package: src/aidl/package/fileName.aidl -> then build the project to generate the fileName.java file in folder "app/build/generated/aidl_.../"
        This file defines the programming interface with method signatures.
    2. Implement the interface
        The Android SDK tools generate an interface in the Java programming language based on your .aidl file.
        This interface has an inner abstract class named Stub that extends Binder and implements methods from your AIDL interface. You must extend the Stub class and implement the methods.
    3. Expose the interface to clients
        Copy .aidl file to clients apps -> do same steps as above
        Implement a Service and override onBind() to return your implementation of the Stub class.
    */
public class BoundedServiceAIDLBasic extends Service {
    private static final String TAG = BoundedServiceAIDLBasic.class.getSimpleName();
    private final IRemoteServiceInterface.Stub mBinder = new IRemoteServiceInterface.Stub() {
        public int getPid() {
            return Process.myPid();
        }
        @Override
        public String getPackageName() {
            return BoundedServiceAIDLBasic.this.getPackageName();
        }
        public void basicTypes(int anInt, long aLong, boolean aBoolean, float aFloat, double aDouble, String aString) {
            // TODO something
        }

        @Override
        public void sendMessageBundle(Bundle bundle) {
            bundle.setClassLoader(getClass().getClassLoader());
            MsgObject msg = bundle.getParcelable("msg");
            Logger.d(TAG, "saveMessageBundle: msg=" + msg);
        }
    };
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Logger.d(TAG, "onBind");
        return mBinder;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.d(TAG, "onCreate thread=" + Thread.currentThread().getName());
    }

    @Override
    public void onRebind(Intent intent) {
        super.onRebind(intent);
        Logger.d(TAG, "onRebind thread=" + Thread.currentThread().getName());
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.d(TAG, "onStartCommand");
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public boolean onUnbind(Intent intent) {
        Logger.d(TAG, "onUnbind");
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy, thread=" + Thread.currentThread().getName());
    }
}
