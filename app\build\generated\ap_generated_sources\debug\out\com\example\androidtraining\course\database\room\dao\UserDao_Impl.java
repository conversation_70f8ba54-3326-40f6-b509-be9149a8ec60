package com.example.androidtraining.course.database.room.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.room.EntityDeleteOrUpdateAdapter;
import androidx.room.EntityInsertAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomRawQuery;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.DBUtil;
import androidx.room.util.SQLiteConnectionUtil;
import androidx.room.util.SQLiteStatementUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.SQLiteStatement;
import androidx.sqlite.db.SupportSQLiteQuery;
import com.example.androidtraining.course.database.room.entities.User;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation", "removal"})
public final class UserDao_Impl implements UserDao {
  private final RoomDatabase __db;

  private final EntityInsertAdapter<User> __insertAdapterOfUser;

  private final EntityDeleteOrUpdateAdapter<User> __deleteAdapterOfUser;

  private final EntityDeleteOrUpdateAdapter<User> __updateAdapterOfUser;

  public UserDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertAdapterOfUser = new EntityInsertAdapter<User>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `engineers` (`id`,`name`,`gen`,`single_id`) VALUES (?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final User entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindText(2, entity.getName());
        }
        statement.bindLong(3, entity.getGen());
        if (entity.getSingleId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindText(4, entity.getSingleId());
        }
      }
    };
    this.__deleteAdapterOfUser = new EntityDeleteOrUpdateAdapter<User>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `engineers` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final User entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfUser = new EntityDeleteOrUpdateAdapter<User>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `engineers` SET `id` = ?,`name` = ?,`gen` = ?,`single_id` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final User entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindText(2, entity.getName());
        }
        statement.bindLong(3, entity.getGen());
        if (entity.getSingleId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindText(4, entity.getSingleId());
        }
        statement.bindLong(5, entity.getId());
      }
    };
  }

  @Override
  public long[] insertUsers(final List<User> users) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      return __insertAdapterOfUser.insertAndReturnIdsArray(_connection, users);
    });
  }

  @Override
  public long insertUser(final User user) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      return __insertAdapterOfUser.insertAndReturnId(_connection, user);
    });
  }

  @Override
  public int deleteUser(final User user) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      int _result = 0;
      _result += __deleteAdapterOfUser.handle(_connection, user);
      return _result;
    });
  }

  @Override
  public int deleteUsers(final List<User> users) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      int _result = 0;
      _result += __deleteAdapterOfUser.handleMultiple(_connection, users);
      return _result;
    });
  }

  @Override
  public int updateUser(final User user) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      int _result = 0;
      _result += __updateAdapterOfUser.handle(_connection, user);
      return _result;
    });
  }

  @Override
  public List<User> getAllUsers() {
    final String _sql = "SELECT * FROM engineers";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfGen = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gen");
        final int _columnIndexOfSingleId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "single_id");
        final List<User> _result = new ArrayList<User>();
        while (_stmt.step()) {
          final User _item;
          _item = new User();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final int _tmpGen;
          _tmpGen = (int) (_stmt.getLong(_columnIndexOfGen));
          _item.setGen(_tmpGen);
          final String _tmpSingleId;
          if (_stmt.isNull(_columnIndexOfSingleId)) {
            _tmpSingleId = null;
          } else {
            _tmpSingleId = _stmt.getText(_columnIndexOfSingleId);
          }
          _item.setSingleId(_tmpSingleId);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<User> getListUsersByIds(final int[] uIds) {
    final StringBuilder _stringBuilder = new StringBuilder();
    _stringBuilder.append("SELECT * FROM engineers WHERE id IN (");
    final int _inputSize = uIds == null ? 1 : uIds.length;
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (uIds == null) {
          _stmt.bindNull(_argIndex);
        } else {
          for (int _item : uIds) {
            _stmt.bindLong(_argIndex, _item);
            _argIndex++;
          }
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfGen = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gen");
        final int _columnIndexOfSingleId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "single_id");
        final List<User> _result = new ArrayList<User>();
        while (_stmt.step()) {
          final User _item_1;
          _item_1 = new User();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item_1.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item_1.setName(_tmpName);
          final int _tmpGen;
          _tmpGen = (int) (_stmt.getLong(_columnIndexOfGen));
          _item_1.setGen(_tmpGen);
          final String _tmpSingleId;
          if (_stmt.isNull(_columnIndexOfSingleId)) {
            _tmpSingleId = null;
          } else {
            _tmpSingleId = _stmt.getText(_columnIndexOfSingleId);
          }
          _item_1.setSingleId(_tmpSingleId);
          _result.add(_item_1);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public User findUserByName(final String name) {
    final String _sql = "SELECT * FROM engineers WHERE name = ? LIMIT 1";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (name == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, name);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfGen = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gen");
        final int _columnIndexOfSingleId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "single_id");
        final User _result;
        if (_stmt.step()) {
          _result = new User();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _result.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _result.setName(_tmpName);
          final int _tmpGen;
          _tmpGen = (int) (_stmt.getLong(_columnIndexOfGen));
          _result.setGen(_tmpGen);
          final String _tmpSingleId;
          if (_stmt.isNull(_columnIndexOfSingleId)) {
            _tmpSingleId = null;
          } else {
            _tmpSingleId = _stmt.getText(_columnIndexOfSingleId);
          }
          _result.setSingleId(_tmpSingleId);
        } else {
          _result = null;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public Cursor selectUserById(final long id) {
    final String _sql = "SELECT * FROM engineers WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final Cursor _tmpResult = __db.query(_statement);
    return _tmpResult;
  }

  @Override
  public void updateUser(final String name, final String singleId, final String nameLike,
      final int id) {
    final String _sql = "UPDATE engineers SET name=?, single_id=? WHERE name like ? OR id=?";
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (name == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, name);
        }
        _argIndex = 2;
        if (singleId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, singleId);
        }
        _argIndex = 3;
        if (nameLike == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, nameLike);
        }
        _argIndex = 4;
        _stmt.bindLong(_argIndex, id);
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public int deleteUserById(final String uid) {
    final String _sql = "DELETE FROM engineers WHERE id=?";
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (uid == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, uid);
        }
        _stmt.step();
        return SQLiteConnectionUtil.getTotalChangedRows(_connection);
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public User updateUser(final SupportSQLiteQuery query) {
    final RoomRawQuery _rawQuery = RoomSQLiteQuery.copyFrom(query).toRoomRawQuery();
    final String _sql = _rawQuery.getSql();
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        _rawQuery.getBindingFunction().invoke(_stmt);
        final User _result;
        if (_stmt.step()) {
          _result = __entityStatementConverter_comExampleAndroidtrainingCourseDatabaseRoomEntitiesUser(_stmt);
        } else {
          _result = null;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private User __entityStatementConverter_comExampleAndroidtrainingCourseDatabaseRoomEntitiesUser(
      @NonNull final SQLiteStatement statement) {
    final User _entity;
    final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndex(statement, "id");
    final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndex(statement, "name");
    final int _columnIndexOfGen = SQLiteStatementUtil.getColumnIndex(statement, "gen");
    final int _columnIndexOfSingleId = SQLiteStatementUtil.getColumnIndex(statement, "single_id");
    _entity = new User();
    if (_columnIndexOfId != -1) {
      final int _tmpId;
      _tmpId = (int) (statement.getLong(_columnIndexOfId));
      _entity.setId(_tmpId);
    }
    if (_columnIndexOfName != -1) {
      final String _tmpName;
      if (statement.isNull(_columnIndexOfName)) {
        _tmpName = null;
      } else {
        _tmpName = statement.getText(_columnIndexOfName);
      }
      _entity.setName(_tmpName);
    }
    if (_columnIndexOfGen != -1) {
      final int _tmpGen;
      _tmpGen = (int) (statement.getLong(_columnIndexOfGen));
      _entity.setGen(_tmpGen);
    }
    if (_columnIndexOfSingleId != -1) {
      final String _tmpSingleId;
      if (statement.isNull(_columnIndexOfSingleId)) {
        _tmpSingleId = null;
      } else {
        _tmpSingleId = statement.getText(_columnIndexOfSingleId);
      }
      _entity.setSingleId(_tmpSingleId);
    }
    return _entity;
  }
}
