package com.example.androidtraining.course.multithread;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.ExifInterface;
import android.media.ThumbnailUtils;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Size;

import com.example.androidtraining.utils.Logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ImageRepository {
    private static final String TAG = ImageRepository.class.getSimpleName();
    
    private final Context context;
    private final RoomImageDao imageDao;
    private final ExecutorService executorService;
    private final File thumbnailDir;
    private final ObjectDetectionService objectDetectionService;
    
    public interface LoadCallback {
        void onProgress(int current, int total);
        void onComplete(List<RoomImage> images);
        void onError(String error);
    }
    
    public ImageRepository(Context context) {
        this.context = context;
        this.imageDao = RoomImageDatabase.getDatabase(context).roomImageDao();
        this.executorService = Executors.newFixedThreadPool(4); // Use 4 threads for parallel processing
        
        // Create thumbnail directory
        this.thumbnailDir = new File(context.getCacheDir(), "thumbnails");
        if (!thumbnailDir.exists()) {
            thumbnailDir.mkdirs();
        }

        // Initialize object detection service
        this.objectDetectionService = new ObjectDetectionService(context);
    }
    
    public void loadAllImages(LoadCallback callback) {
        executorService.execute(() -> {
            try {
                List<RoomImage> existingImages = imageDao.getAllImages();
                List<ImageInfo> deviceImages = scanDeviceImages();
                
                Logger.d(TAG, "Found " + deviceImages.size() + " images on device");
                Logger.d(TAG, "Found " + existingImages.size() + " images in database");
                
                List<RoomImage> finalImages = new ArrayList<>();
                int processed = 0;
                
                for (ImageInfo imageInfo : deviceImages) {
                    // Check if image already exists in database
                    RoomImage existingImage = findImageByPath(existingImages, imageInfo.path);
                    
                    if (existingImage != null) {
                        // Image exists, check if it needs update
                        if (existingImage.getDateModified() < imageInfo.dateModified) {
                            updateExistingImage(existingImage, imageInfo);
                            imageDao.updateImage(existingImage);
                        }
                        finalImages.add(existingImage);
                    } else {
                        // New image, create and insert
                        RoomImage newImage = createRoomImageFromInfo(imageInfo);
                        long id = imageDao.insertImage(newImage);
                        newImage.setId((int) id);
                        finalImages.add(newImage);
                    }
                    
                    processed++;
                    final int currentProgress = processed;
                    final int total = deviceImages.size();
                    
                    // Update progress every 10 images or at the end
                    if (currentProgress % 10 == 0 || currentProgress == total) {
                        callback.onProgress(currentProgress, total);
                    }
                }
                
                // Remove images from database that no longer exist on device
                removeDeletedImages(existingImages, deviceImages);

                // Start object detection for unprocessed images in background
                startObjectDetectionForUnprocessedImages();

                callback.onComplete(finalImages);
                
            } catch (Exception e) {
                Logger.e(TAG, "Error loading images: " + e.getMessage());
                callback.onError(e.getMessage());
            }
        });
    }
    
    private List<ImageInfo> scanDeviceImages() {
        List<ImageInfo> images = new ArrayList<>();
        
        ContentResolver contentResolver = context.getContentResolver();
        Uri uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
        
        String[] projection = {
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.DATA,
            MediaStore.Images.Media.SIZE,
            MediaStore.Images.Media.WIDTH,
            MediaStore.Images.Media.HEIGHT,
            MediaStore.Images.Media.DATE_TAKEN,
            MediaStore.Images.Media.DATE_MODIFIED,
            MediaStore.Images.Media.MIME_TYPE
        };
        
        String sortOrder = MediaStore.Images.Media.DATE_TAKEN + " DESC";
        
        try (Cursor cursor = contentResolver.query(uri, projection, null, null, sortOrder)) {
            if (cursor != null) {
                int idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID);
                int nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME);
                int dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
                int sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE);
                int widthColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.WIDTH);
                int heightColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.HEIGHT);
                int dateTakenColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_TAKEN);
                int dateModifiedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED);
                int mimeTypeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.MIME_TYPE);
                
                while (cursor.moveToNext()) {
                    ImageInfo imageInfo = new ImageInfo();
                    imageInfo.id = cursor.getLong(idColumn);
                    imageInfo.name = cursor.getString(nameColumn);
                    imageInfo.path = cursor.getString(dataColumn);
                    imageInfo.size = cursor.getLong(sizeColumn);
                    imageInfo.width = cursor.getInt(widthColumn);
                    imageInfo.height = cursor.getInt(heightColumn);
                    imageInfo.dateTaken = cursor.getLong(dateTakenColumn);
                    imageInfo.dateModified = cursor.getLong(dateModifiedColumn) * 1000; // Convert to milliseconds
                    imageInfo.mimeType = cursor.getString(mimeTypeColumn);
                    
                    // Verify file exists
                    File file = new File(imageInfo.path);
                    if (file.exists() && file.canRead()) {
                        images.add(imageInfo);
                    }
                }
            }
        } catch (Exception e) {
            Logger.e(TAG, "Error scanning device images: " + e.getMessage());
        }
        
        return images;
    }
    
    private RoomImage createRoomImageFromInfo(ImageInfo imageInfo) {
        RoomImage roomImage = new RoomImage(
            imageInfo.name,
            imageInfo.path,
            imageInfo.size,
            imageInfo.width,
            imageInfo.height,
            imageInfo.dateTaken,
            imageInfo.dateModified,
            imageInfo.mimeType
        );
        
        // Generate thumbnail
        String thumbnailPath = generateThumbnail(imageInfo.path);
        roomImage.setThumbnailPath(thumbnailPath);
        
        return roomImage;
    }
    
    private void updateExistingImage(RoomImage existingImage, ImageInfo imageInfo) {
        existingImage.setName(imageInfo.name);
        existingImage.setSize(imageInfo.size);
        existingImage.setWidth(imageInfo.width);
        existingImage.setHeight(imageInfo.height);
        existingImage.setDateTaken(imageInfo.dateTaken);
        existingImage.setDateModified(imageInfo.dateModified);
        existingImage.setMimeType(imageInfo.mimeType);
        
        // Regenerate thumbnail if needed
        if (existingImage.getThumbnailPath() == null || 
            !new File(existingImage.getThumbnailPath()).exists()) {
            String thumbnailPath = generateThumbnail(imageInfo.path);
            existingImage.setThumbnailPath(thumbnailPath);
        }
    }
    
    private RoomImage findImageByPath(List<RoomImage> images, String path) {
        for (RoomImage image : images) {
            if (image.getPath().equals(path)) {
                return image;
            }
        }
        return null;
    }
    
    private void removeDeletedImages(List<RoomImage> existingImages, List<ImageInfo> deviceImages) {
        List<String> devicePaths = new ArrayList<>();
        for (ImageInfo info : deviceImages) {
            devicePaths.add(info.path);
        }
        
        for (RoomImage existingImage : existingImages) {
            if (!devicePaths.contains(existingImage.getPath())) {
                // Image no longer exists on device, remove from database
                imageDao.deleteImage(existingImage);
                
                // Also delete thumbnail
                if (existingImage.getThumbnailPath() != null) {
                    File thumbnailFile = new File(existingImage.getThumbnailPath());
                    if (thumbnailFile.exists()) {
                        thumbnailFile.delete();
                    }
                }
            }
        }
    }
    
    private String generateThumbnail(String imagePath) {
        try {
            File imageFile = new File(imagePath);
            String thumbnailName = "thumb_" + imageFile.getName();
            File thumbnailFile = new File(thumbnailDir, thumbnailName);
            
            if (thumbnailFile.exists()) {
                return thumbnailFile.getAbsolutePath();
            }
            
            // Create thumbnail using ThumbnailUtils (API 29+) or BitmapFactory
            Bitmap thumbnail;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                thumbnail = ThumbnailUtils.createImageThumbnail(imageFile, new Size(200, 200), null);
            } else {
                // Fallback for older versions
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inSampleSize = 4; // Scale down by factor of 4
                Bitmap bitmap = BitmapFactory.decodeFile(imagePath, options);
                if (bitmap != null) {
                    thumbnail = Bitmap.createScaledBitmap(bitmap, 200, 200, true);
                    bitmap.recycle();
                } else {
                    return null;
                }
            }
            
            if (thumbnail != null) {
                // Save thumbnail to file
                try (FileOutputStream out = new FileOutputStream(thumbnailFile)) {
                    thumbnail.compress(Bitmap.CompressFormat.JPEG, 80, out);
                    thumbnail.recycle();
                    return thumbnailFile.getAbsolutePath();
                }
            }
            
        } catch (Exception e) {
            Logger.e(TAG, "Error generating thumbnail for " + imagePath + ": " + e.getMessage());
        }
        
        return null;
    }
    
    private void startObjectDetectionForUnprocessedImages() {
        // Run object detection in background without blocking the UI
        objectDetectionService.processUnprocessedImages(new ObjectDetectionService.ProcessingCallback() {
            @Override
            public void onProgress(int current, int total) {
                Logger.d(TAG, "Object detection progress: " + current + "/" + total);
            }

            @Override
            public void onComplete() {
                Logger.d(TAG, "Object detection completed for all unprocessed images");
            }

            @Override
            public void onError(String error) {
                Logger.e(TAG, "Object detection error: " + error);
            }
        });
    }

    public void detectObjectsForImage(RoomImage image, ObjectDetectionService.DetectionCallback callback) {
        objectDetectionService.detectObjectsInImage(image, callback);
    }

    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        if (objectDetectionService != null) {
            objectDetectionService.cleanup();
        }
    }
    
    // Helper class for image information
    private static class ImageInfo {
        long id;
        String name;
        String path;
        long size;
        int width;
        int height;
        long dateTaken;
        long dateModified;
        String mimeType;
    }
}
