R_DEF: Internal format may change without notice
local
color black
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color white
dimen btn_margin_top
drawable ic_arrow
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_next
id barrier1
id btn1
id btn2
id btn3
id btn_activity
id btn_adapter
id btn_add_prefs
id btn_alarm_service
id btn_broadcast_receiver
id btn_create_notification
id btn_create_remote_notification
id btn_database
id btn_exact_not_repeat
id btn_exact_repeat
id btn_frame
id btn_get_prefs
id btn_include
id btn_noti_custom
id btn_noti_default
id btn_notification
id btn_send_local_action
id btn_send_new_action
id btn_send_user_action
id btn_service
id btn_share_prefs
id btn_show_fragment1
id btn_show_fragment2
id btn_show_list_view
id btn_show_recycle_view
id btn_start_bounded_service_aidl
id btn_start_bounded_service_local
id btn_start_bounded_service_messenger
id btn_start_course
id btn_start_unbounded_service
id btn_start_xx
id btn_stop_bounded_service_aidl
id btn_stop_bounded_service_local
id btn_stop_bounded_service_messenger
id btn_stop_unbounded_service
id btn_test_async
id btn_test_executor
id btn_test_future_concurrent
id btn_test_thread
id btn_thread_handler
id btn_view_Layout
id but1
id but2
id but3
id but4
id but5
id but6
id but7
id edt_activity
id edt_top_fragment
id edt_value_prefs
id fileName
id fragment_bottom
id fragment_container_activity
id fragment_container_adapter
id fragment_top
id frame_layout
id gl_h_60
id gl_v_40
id group
id guideline_15
id guideline_40
id guideline_55
id guideline_item_user_vertical_15
id guideline_item_user_vertical_70
id guideline_item_vertical_30
id guideline_item_vertical_90
id guideline_main_20
id img_song_icon
id img_song_next
id item_song_view
id item_user_view
id listItemIcon
id list_view_song
id main
id noti_btn_send_broadcast
id noti_btn_start_activity
id noti_btn_start_service
id noti_footer
id noti_tv_content
id noti_tv_title
id recycle_view_song
id seek_bar_async
id tv_async_title
id tv_count
id tv_count_async
id tv_frame
id tv_include
id tv_show_content
id tv_song_author
id tv_song_name
id tv_song_time
id tv_thread_count
id tv_thread_title
id tv_title
id tv_user_id
id tv_user_name
id tv_user_phone
id tv_value_prefs
id user_recycle_view
layout activity_adapter
layout activity_alarm
layout activity_broadcast
layout activity_course
layout activity_custom
layout activity_database
layout activity_example
layout activity_main
layout activity_notification
layout activity_service
layout activity_share_prefs
layout activity_snooze
layout activity_thread
layout activity_view_layout
layout file_row
layout fragment_bottom
layout fragment_list_view
layout fragment_one
layout fragment_recycle_view
layout fragment_top
layout fragment_two
layout item_list_song
layout item_list_user
layout notification_layout
layout view_layout_include
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string firebase_database_url
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string hello_blank_fragment
string project_id
style Theme.AndroidTraining
xml backup_rules
xml data_extraction_rules
