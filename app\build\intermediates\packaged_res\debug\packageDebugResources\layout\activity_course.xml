<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.CourseActivity">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_main_20"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.2"
        />

    <Button
        android:id="@+id/btn_activity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Activity"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toTopOf="parent"
        />

    <Button
        android:id="@+id/btn_view_Layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test View Layout"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_activity"
        />

    <Button
        android:id="@+id/btn_adapter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Adapter"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_view_Layout"
        />

    <Button
        android:id="@+id/btn_broadcast_receiver"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Broadcast receiver"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_adapter"
        />

    <Button
        android:id="@+id/btn_notification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Notification"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_broadcast_receiver"
        />

    <Button
        android:id="@+id/btn_service"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Service"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_notification"
        />

    <Button
        android:id="@+id/btn_share_prefs"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test SharePreference"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_service"
        />

    <Button
        android:id="@+id/btn_database"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Database"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_share_prefs"
        />

    <Button
        android:id="@+id/btn_thread_handler"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Thread - Handler"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_database"
        />

    <Button
        android:id="@+id/btn_alarm_service"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Alarm Service"
        android:layout_marginTop="@dimen/btn_margin_top"
        app:layout_constraintStart_toStartOf="@+id/guideline_main_20"
        app:layout_constraintTop_toBottomOf="@id/btn_thread_handler"
        />

</androidx.constraintlayout.widget.ConstraintLayout>