<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.androidtraining"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="35" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Storage permissions: API < 30 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Storage permissions: API < 30 - MANAGE_EXTERNAL_STORAGE & request "All files access" permission -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <!-- Alarm service -->
    <uses-permission
        android:name="android.permission.SCHEDULE_EXACT_ALARM"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />

    <permission android:name="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />

    <!-- API 33+ -->
    <!-- Foreground Service: need foregroundServiceType & permission: FOREGROUND_SERVICE & FOREGROUND_SERVICE_SPECIAL_USE -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />

    <permission
        android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.example.androidtraining.app.MainApp"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.AndroidTraining" >
        <activity
            android:name="com.example.androidtraining.MainActivity"
            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.androidtraining.course.CourseActivity"
            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
            android:exported="true"
            android:launchMode="singleInstance" >

            <!-- <intent-filter> -->
            <!-- <action android:name="android.intent.action.MAIN" /> -->
            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
            <!-- </intent-filter> -->

            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="com.example.androidtraining.course.alarmservice.SnoozeActivity"
            android:exported="false"
            android:launchMode="singleInstance" />
        <activity
            android:name="com.example.androidtraining.course.alarmservice.AlarmActivity"
            android:exported="false" />
        <activity
            android:name="com.example.androidtraining.course.multithread.GalleryActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <activity
            android:name="com.example.androidtraining.course.viewlayout.ViewLayoutActivity"
            android:exported="false" />
        <activity
            android:name="com.example.androidtraining.course.activityfragment.ExampleActivity"
            android:exported="true" />
        <activity
            android:name="com.example.androidtraining.course.adapter.AdapterActivity"
            android:exported="false" />
        <activity
            android:name="com.example.androidtraining.course.broadcastreceiver.BroadcastActivity"
            android:exported="false" />
        <activity
            android:name="com.example.androidtraining.course.notification.NotificationActivity"
            android:exported="false" />
        <activity
            android:name="com.example.androidtraining.course.service.ServiceActivity"
            android:exported="false" />
        <activity
            android:name="com.example.androidtraining.course.shareprefs.SharePrefsActivity"
            android:exported="false" />
        <activity
            android:name="com.example.androidtraining.course.database.DatabaseActivity"
            android:exported="false" />
        <activity
            android:name="com.example.androidtraining.course.threadhandler.ThreadActivity"
            android:exported="false" />

        <receiver
            android:name="com.example.androidtraining.course.broadcastreceiver.StaticBroadcastReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>

                <!-- system action -->
                <action android:name="android.intent.action.AIRPLANE_MODE" />
                <action android:name="android.intent.action.SCREEN_ON" />
                <action android:name="android.intent.action.SCREEN_OFF" />
                <!-- user define action -->
                <action android:name="com.example.androidtraining.USER_ACTION" />
                <action android:name="com.example.androidtraining.NEW_ACTION" />
                <action android:name="com.example.androidtraining.LOCAL_ACTION" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.example.androidtraining.course.alarmservice.AlarmReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.example.androidtraining.ALARM_ACTION" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <service android:name="com.example.androidtraining.course.service.BackgroundService" />
        <service
            android:name="com.example.androidtraining.course.service.ForegroundService"
            android:foregroundServiceType="specialUse" />
        <service
            android:name="com.example.androidtraining.course.service.BoundedServiceLocal"
            android:exported="true" />
        <service
            android:name="com.example.androidtraining.course.service.BoundedServiceMessenger"
            android:exported="true"
            android:process=":remote" />
        <service
            android:name="com.example.androidtraining.course.service.BoundedServiceAIDLBasic"
            android:exported="true"
            android:process=":remote" />
        <service
            android:name="com.example.androidtraining.course.service.BoundedServiceAidlObject"
            android:exported="true" />

        <provider
            android:name="com.example.androidtraining.course.database.AppDbContentProvider"
            android:authorities="com.example.androidtraining.provider.AppDbContentProvider"
            android:enabled="true"
            android:exported="true"
            android:permission="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />

        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <activity
            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="firebase.auth"
                    android:path="/"
                    android:scheme="genericidp" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="firebase.auth"
                    android:path="/"
                    android:scheme="recaptcha" />
            </intent-filter>
        </activity>

        <service
            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
            android:enabled="true"
            android:exported="false" >
            <meta-data
                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
        </service>

        <activity
            android:name="androidx.credentials.playservices.HiddenActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:enabled="true"
            android:exported="false"
            android:fitsSystemWindows="true"
            android:theme="@style/Theme.Hidden" >
        </activity>
        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--
            Service handling Google Sign-In user revocation. For apps that do not integrate with
            Google Sign-In, this service will never be started.
        -->
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
            android:visibleToInstantApps="true" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.example.androidtraining.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.androidtraining.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
        <activity
            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
            android:exported="false"
            android:stateNotNeeded="true"
            android:theme="@style/Theme.PlayCore.Transparent" />
    </application>

</manifest>