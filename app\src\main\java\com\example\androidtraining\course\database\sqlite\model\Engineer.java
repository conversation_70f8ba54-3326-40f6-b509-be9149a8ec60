package com.example.androidtraining.course.database.sqlite.model;

import androidx.annotation.NonN<PERSON>;

public class Engineer { // model
    int id;
    String name;
    int gen;
    String singleId;

    public Engineer(int id, String name, int gen, String singleId) {
        this.id = id;
        this.name = name;
        this.gen = gen;
        this.singleId = singleId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getGen() {
        return gen;
    }

    public void setGen(int gen) {
        this.gen = gen;
    }

    public String getSingleId() {
        return singleId;
    }

    public void setSingleId(String singleId) {
        this.singleId = singleId;
    }

    @NonNull
    @Override
    public String toString() {
        return "Engineer{id=" + id + ", name=" + name + ", gen=" + gen + ", singleId=" + singleId + "}";
    }
}

