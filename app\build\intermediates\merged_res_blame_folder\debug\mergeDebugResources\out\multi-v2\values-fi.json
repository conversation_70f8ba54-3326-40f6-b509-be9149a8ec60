{"logs": [{"outputFile": "com.example.androidtraining.app-mergeDebugResources-48:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\2aa10d9fde787503bb7891509092037b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4643,4754,4907,5038,5144,5287,5413,5529,5786,5927,6033,6182,6308,6456,6595,6661,6731", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4749,4902,5033,5139,5282,5408,5524,5631,5922,6028,6177,6303,6451,6590,6656,6726,6809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\3e3a03410c222d389eb5acdf62f3d148\\transformed\\material-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3217,3293,3367,3450,3539,4355,4451,4559,6917,6979,7044,7446,7521,7586,7674,7739,7805,7863,7934,8000,8054,8164,8224,8288,8342,8415,8531,8615,8691,8782,8863,8944,9077,9162,9247,9380,9470,9544,9596,9647,9713,9790,9872,9943,10017,10091,10170,10247,10319,10426,10515,10591,10682,10777,10851,10924,11018,11072,11146,11218,11304,11390,11452,11516,11579,11650,11751,11854,11949,12049,12105,12160,12320,12406,12485", "endLines": "5,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "313,3288,3362,3445,3534,3616,4446,4554,4638,6974,7039,7132,7516,7581,7669,7734,7800,7858,7929,7995,8049,8159,8219,8283,8337,8410,8526,8610,8686,8777,8858,8939,9072,9157,9242,9375,9465,9539,9591,9642,9708,9785,9867,9938,10012,10086,10165,10242,10314,10421,10510,10586,10677,10772,10846,10919,11013,11067,11141,11213,11299,11385,11447,11511,11574,11645,11746,11849,11944,12044,12100,12155,12234,12401,12480,12555"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\e7f24da73f275a8a791d04076c131d9f\\transformed\\appcompat-1.7.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,12239", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,12315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\60115c31cdc3e8b7d77a5b34ad2884ea\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2988,3100", "endColumns": "111,116", "endOffsets": "3095,3212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\3b1f9487fa18ad75d94f820ca6c18c8a\\transformed\\core-1.16.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "40,41,42,43,44,45,46,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3621,3717,3819,3917,4022,4127,4239,12560", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3712,3814,3912,4017,4122,4234,4350,12656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\7ee81ce4615b9e8929af9df6afeffe39\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5636", "endColumns": "149", "endOffsets": "5781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\368b07b4e9b7912a6e22f44deed31ddf\\transformed\\browser-1.4.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6814,7137,7238,7347", "endColumns": "102,100,108,98", "endOffsets": "6912,7233,7342,7441"}}]}]}