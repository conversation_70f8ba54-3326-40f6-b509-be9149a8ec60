package com.example.androidtraining.course.adapter;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;

import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * A simple {@link Fragment} subclass.
 * Use the  factory method to
 * create an instance of this fragment.
 */
public class ListViewFragment extends Fragment {
    public static final String TAG = ListViewFragment.class.getSimpleName();

    Context mContext;
    ListView mListSongView;
    SongAdapterListView mCustomSongAdapter;
    List<MySong> mListSong = new ArrayList<>();

    public ListViewFragment() {
        Logger.d(TAG, "constructor: mListSong size=" + mListSong.size());
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        Logger.d(TAG, "onAttach");
        mContext = context;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate: mListSong size=" + mListSong.size());
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Logger.d(TAG, "onCreateView: mListSong size=" + mListSong.size());
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_list_view, container, false);
        mListSongView = view.findViewById(R.id.list_view_song);
        mCustomSongAdapter = new SongAdapterListView(mContext, R.layout.item_list_song, mListSong);
        mListSongView.setAdapter(mCustomSongAdapter);
        return view;
    }

    public void setListSong(@NonNull List<MySong> listSong) {
        mListSong = listSong;
    }

//    @Override
//    public void onStart() {
//        super.onStart();
//        Logger.d(TAG, "onStart");
//    }
//
//    @Override
//    public void onResume() {
//        super.onResume();
//        Logger.d(TAG, "onResume");
//    }
//
//    @Override
//    public void onStop() {
//        super.onStop();
//        Logger.d(TAG, "onStop");
//    }
//
//    @Override
//    public void onPause() {
//        super.onPause();
//        Logger.d(TAG, "onPause");
//    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Logger.d(TAG, "onDestroyView");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy");
    }

    @Override
    public void onDetach() {
        super.onDetach();
        Logger.d(TAG, "onDetach");
    }
}