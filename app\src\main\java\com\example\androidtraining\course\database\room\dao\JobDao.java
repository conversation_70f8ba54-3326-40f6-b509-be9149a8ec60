package com.example.androidtraining.course.database.room.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.androidtraining.course.database.room.entities.Job;

import java.util.List;

@Dao
public interface JobDao {

    @Query("SELECT * FROM tasks")
    List<Job> getAllJobs();

    @Query("SELECT * FROM tasks WHERE id IN (:ids)")
    List<Job> getListJobByIds(int[] ids);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long[] insertJobs(List<Job> listTask);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(Job task);

    @Update
    int update(Job task);

    @Delete
    int delete(Job task);

    @Query("DELETE FROM tasks")
    void deleteAll();
}
