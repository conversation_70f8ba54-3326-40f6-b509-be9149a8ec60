package com.example.androidtraining.course.multithread;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;

import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;

import java.util.Arrays;

public class GalleryActivity extends AppCompatActivity {
    private static final String TAG = GalleryActivity.class.getSimpleName();
    private static final int PERMISSION_REQUEST_CODE = 1001;
    
    private RecyclerView recyclerView;
    private ProgressBar progressBar;
    private Toolbar toolbar;
    private com.google.android.material.floatingactionbutton.FloatingActionButton fabSelectMode;

    private ImageAdapter imageAdapter;
    private ImageRepository imageRepository;
    private boolean isSelectionMode = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gallery);
        
        Logger.d(TAG, "onCreate");
        
        initViews();
        setupToolbar();
        setupRecyclerView();
        
        imageRepository = new ImageRepository(this);
        
        if (checkPermissions()) {
            loadImages();
        } else {
            requestPermissions();
        }
    }
    
    private void initViews() {
        recyclerView = findViewById(R.id.recycler_view_gallery);
        progressBar = findViewById(R.id.progress_bar);
        toolbar = findViewById(R.id.toolbar);
        fabSelectMode = findViewById(R.id.fab_select_mode);

        fabSelectMode.setOnClickListener(v -> toggleSelectionMode());
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("Gallery");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }
    
    private void setupRecyclerView() {
        GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
        recyclerView.setLayoutManager(layoutManager);

        imageAdapter = new ImageAdapter(this);
        imageAdapter.setOnImageClickListener(new ImageAdapter.OnImageClickListener() {
            @Override
            public void onImageClick(RoomImage image, int position) {
                Intent intent = new Intent(GalleryActivity.this, ImageDetailActivity.class);
                intent.putExtra(ImageDetailActivity.EXTRA_IMAGE_ID, image.getId());
                startActivityForResult(intent, 1001);
            }

            @Override
            public void onImageLongClick(RoomImage image, int position) {
                // Long click handled in adapter (selection mode)
            }
        });

        imageAdapter.setOnSelectionChangedListener(selectedCount -> {
            if (selectedCount > 0) {
                if (getSupportActionBar() != null) {
                    getSupportActionBar().setTitle(selectedCount + " selected");
                }
                isSelectionMode = true;
                invalidateOptionsMenu(); // Refresh menu
            } else {
                if (getSupportActionBar() != null) {
                    getSupportActionBar().setTitle("Gallery");
                }
                isSelectionMode = false;
                imageAdapter.setSelectionMode(false);
                invalidateOptionsMenu(); // Refresh menu
            }
        });

        recyclerView.setAdapter(imageAdapter);
    }
    
    private boolean checkPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R)
            return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES)
                    == PackageManager.PERMISSION_GRANTED;
        return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    private void requestPermissions() {
        Logger.d(TAG, "Requesting permission..., sdk: " + Build.VERSION.SDK_INT);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            requestPermissions(
                    new String[]{Manifest.permission.READ_MEDIA_IMAGES, Manifest.permission.READ_MEDIA_VIDEO},
                    PERMISSION_REQUEST_CODE);
        } else {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                    PERMISSION_REQUEST_CODE);
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Logger.d(TAG, "onRequestPermissionsResult: requestCode=" + requestCode + ", grantResults: " + Arrays.toString(grantResults));
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                loadImages();
            } else {
                Toast.makeText(this, "Permission denied. Cannot access images.", Toast.LENGTH_LONG).show();
                finish();
            }
        }
    }
    
    private void loadImages() {
        Logger.d(TAG, "Loading images...");
        progressBar.setVisibility(View.VISIBLE);
        
        // Load images in background thread
        imageRepository.loadAllImages(new ImageRepository.LoadCallback() {
            @Override
            public void onProgress(int current, int total) {
                runOnUiThread(() -> {
                    // Update progress if needed
                });
            }
            
            @Override
            public void onComplete(java.util.List<RoomImage> images) {
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    imageAdapter.updateImages(images);
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(GalleryActivity.this, "Error loading images: " + error, 
                                 Toast.LENGTH_LONG).show();
                });
            }
        });
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.gallery_menu, menu);

        // Show/hide menu items based on selection mode
        MenuItem deleteItem = menu.findItem(R.id.action_delete);
        MenuItem selectAllItem = menu.findItem(R.id.action_select_all);
        MenuItem refreshItem = menu.findItem(R.id.action_refresh);
        MenuItem sortItem = menu.findItem(R.id.action_sort);
        MenuItem filterItem = menu.findItem(R.id.action_filter);

        if (isSelectionMode) {
            if (deleteItem != null) deleteItem.setVisible(true);
            if (selectAllItem != null) selectAllItem.setVisible(true);
            if (refreshItem != null) refreshItem.setVisible(false);
            if (sortItem != null) sortItem.setVisible(false);
            if (filterItem != null) filterItem.setVisible(false);
        } else {
            if (deleteItem != null) deleteItem.setVisible(false);
            if (selectAllItem != null) selectAllItem.setVisible(false);
            if (refreshItem != null) refreshItem.setVisible(true);
            if (sortItem != null) sortItem.setVisible(true);
            if (filterItem != null) filterItem.setVisible(true);
        }

        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();
        
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_refresh) {
            loadImages();
            return true;
        } else if (id == R.id.action_sort) {
            showSortDialog();
            return true;
        } else if (id == R.id.action_filter) {
            showFilterDialog();
            return true;
        } else if (id == R.id.action_delete) {
            deleteSelectedImages();
            return true;
        } else if (id == R.id.action_select_all) {
            imageAdapter.selectAll();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    private void showSortDialog() {
        // TODO: Implement sort dialog
        Toast.makeText(this, "Sort dialog - Coming soon", Toast.LENGTH_SHORT).show();
    }
    
    private void showFilterDialog() {
        String[] filterOptions = {
            "All Images",
            "Liked Images",
            "Images with Objects",
            "Filter by Object Type"
        };

        new AlertDialog.Builder(this)
                .setTitle("Filter Images")
                .setItems(filterOptions, (dialog, which) -> {
                    switch (which) {
                        case 0: // All Images
                            loadImages();
                            break;
                        case 1: // Liked Images
                            filterLikedImages();
                            break;
                        case 2: // Images with Objects
                            filterImagesWithObjects();
                            break;
                        case 3: // Filter by Object Type
                            showObjectTypeFilterDialog();
                            break;
                    }
                })
                .show();
    }

    private void toggleSelectionMode() {
        //        if (imageAdapter.isSelectionMode()) {
//            imageAdapter.setSelectionMode(false);
//            isSelectionMode = false;
//            if (getSupportActionBar() != null) {
//                getSupportActionBar().setTitle("Gallery");
//            }
//        } else {
//            imageAdapter.setSelectionMode(true);
//            isSelectionMode = true;
//        }
//        invalidateOptionsMenu();
    }

    private void deleteSelectedImages() {
        java.util.List<RoomImage> selectedImages = imageAdapter.getSelectedImages();
        if (selectedImages.isEmpty()) {
            Toast.makeText(this, "No images selected", Toast.LENGTH_SHORT).show();
            return;
        }

        String message = "Delete " + selectedImages.size() + " image(s)?";
        new AlertDialog.Builder(this)
                .setTitle("Delete Images")
                .setMessage(message)
                .setPositiveButton("Delete", (dialog, which) -> {
                    performDelete(selectedImages);
                })
                .setNegativeButton("Cancel", null)
                .show();
    }

    private void performDelete(java.util.List<RoomImage> imagesToDelete) {
        progressBar.setVisibility(View.VISIBLE);

        // Perform deletion in background thread
        new Thread(() -> {
            try {
                RoomImageDao dao = RoomImageDatabase.getDatabase(this).roomImageDao();

                for (RoomImage image : imagesToDelete) {
                    // Delete from database
                    dao.deleteImage(image);

                    // Delete actual file
                    java.io.File imageFile = new java.io.File(image.getPath());
                    if (imageFile.exists()) {
                        imageFile.delete();
                    }

                    // Delete thumbnail
                    if (image.getThumbnailPath() != null) {
                        java.io.File thumbnailFile = new java.io.File(image.getThumbnailPath());
                        if (thumbnailFile.exists()) {
                            thumbnailFile.delete();
                        }
                    }
                }

                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(this, "Deleted " + imagesToDelete.size() + " image(s)",
                                 Toast.LENGTH_SHORT).show();

                    // Refresh the list
                    loadImages();

                    // Exit selection mode
                    imageAdapter.setSelectionMode(false);
                    isSelectionMode = false;
                    if (getSupportActionBar() != null) {
                        getSupportActionBar().setTitle("Gallery");
                    }
                    invalidateOptionsMenu();
                });

            } catch (Exception e) {
                Logger.e(TAG, "Error deleting images: " + e.getMessage());
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(this, "Error deleting images: " + e.getMessage(),
                                 Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }

    private void filterLikedImages() {
        progressBar.setVisibility(View.VISIBLE);

        new Thread(() -> {
            try {
                RoomImageDao dao = RoomImageDatabase.getDatabase(this).roomImageDao();
                java.util.List<RoomImage> likedImages = dao.getLikedImages();

                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    imageAdapter.updateImages(likedImages);
                    if (getSupportActionBar() != null) {
                        getSupportActionBar().setTitle("Liked Images (" + likedImages.size() + ")");
                    }
                });

            } catch (Exception e) {
                Logger.e(TAG, "Error filtering liked images: " + e.getMessage());
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(this, "Error filtering images", Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void filterImagesWithObjects() {
        progressBar.setVisibility(View.VISIBLE);

        new Thread(() -> {
            try {
                RoomImageDao dao = RoomImageDatabase.getDatabase(this).roomImageDao();
                java.util.List<RoomImage> allImages = dao.getAllImages();
                java.util.List<RoomImage> imagesWithObjects = new java.util.ArrayList<>();

                for (RoomImage image : allImages) {
                    if (image.getDetectedObjects() != null && !image.getDetectedObjects().isEmpty()
                        && !image.getDetectedObjects().equals("{}")) {
                        java.util.List<String> objectNames = ObjectDetectionService.extractObjectNames(image.getDetectedObjects());
                        if (!objectNames.isEmpty()) {
                            imagesWithObjects.add(image);
                        }
                    }
                }

                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    imageAdapter.updateImages(imagesWithObjects);
                    if (getSupportActionBar() != null) {
                        getSupportActionBar().setTitle("Images with Objects (" + imagesWithObjects.size() + ")");
                    }
                });

            } catch (Exception e) {
                Logger.e(TAG, "Error filtering images with objects: " + e.getMessage());
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(this, "Error filtering images", Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void showObjectTypeFilterDialog() {
        progressBar.setVisibility(View.VISIBLE);

        new Thread(() -> {
            try {
                RoomImageDao dao = RoomImageDatabase.getDatabase(this).roomImageDao();
                java.util.List<RoomImage> allImages = dao.getAllImages();
                java.util.Set<String> allObjectTypes = new java.util.HashSet<>();

                // Collect all unique object types
                for (RoomImage image : allImages) {
                    java.util.List<String> objectNames = ObjectDetectionService.extractObjectNames(image.getDetectedObjects());
                    allObjectTypes.addAll(objectNames);
                }

                java.util.List<String> objectTypesList = new java.util.ArrayList<>(allObjectTypes);
                java.util.Collections.sort(objectTypesList);

                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);

                    if (objectTypesList.isEmpty()) {
                        Toast.makeText(this, "No object types found. Please wait for object detection to complete.",
                                     Toast.LENGTH_LONG).show();
                        return;
                    }

                    String[] objectTypesArray = objectTypesList.toArray(new String[0]);

                    new AlertDialog.Builder(this)
                            .setTitle("Filter by Object Type")
                            .setItems(objectTypesArray, (dialog, which) -> {
                                String selectedObjectType = objectTypesArray[which];
                                filterImagesByObjectType(selectedObjectType);
                            })
                            .setNegativeButton("Cancel", null)
                            .show();
                });

            } catch (Exception e) {
                Logger.e(TAG, "Error getting object types: " + e.getMessage());
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(this, "Error getting object types", Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void filterImagesByObjectType(String objectType) {
        progressBar.setVisibility(View.VISIBLE);

        new Thread(() -> {
            try {
                RoomImageDao dao = RoomImageDatabase.getDatabase(this).roomImageDao();
                java.util.List<RoomImage> allImages = dao.getAllImages();
                java.util.List<RoomImage> filteredImages = new java.util.ArrayList<>();

                for (RoomImage image : allImages) {
                    java.util.List<String> objectNames = ObjectDetectionService.extractObjectNames(image.getDetectedObjects());
                    if (objectNames.contains(objectType.toLowerCase())) {
                        filteredImages.add(image);
                    }
                }

                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    imageAdapter.updateImages(filteredImages);
                    if (getSupportActionBar() != null) {
                        getSupportActionBar().setTitle(objectType + " (" + filteredImages.size() + ")");
                    }
                });

            } catch (Exception e) {
                Logger.e(TAG, "Error filtering images by object type: " + e.getMessage());
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(this, "Error filtering images", Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == 1001 && resultCode == RESULT_OK) {
            // Image was deleted in detail view, refresh the list
            loadImages();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (imageRepository != null) {
            imageRepository.cleanup();
        }
        if (imageAdapter != null) {
            imageAdapter.cleanup();
        }
    }
}
