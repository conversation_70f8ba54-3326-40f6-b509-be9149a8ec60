package com.example.androidtraining.course.broadcastreceiver;

import androidx.annotation.RequiresApi;
import androidx.localbroadcastmanager.content.LocalBroadcastManager; // DEPRECATED - use regular broadcasts with proper permissions instead

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.widget.Button;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;

public class BroadcastActivity extends BaseActivity {
    private static final String TAG = BroadcastActivity.class.getSimpleName();
    private static final String USER_ACTION = "com.example.androidtraining.USER_ACTION";
    private static final String NEW_ACTION = "com.example.androidtraining.NEW_ACTION";
    private static final String LOCAL_ACTION = "com.example.androidtraining.LOCAL_ACTION";

    DynamicBroadcastReceiver myBroadcastReceiver;
    BroadcastReceiver localBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            Logger.d(TAG, "localBroadcastReceiver - onReceive: " + intent.getAction());
        }
    };

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_broadcast;
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate: ");

        // user can not send the actions of system, like: ACTION_AIRPLANE_MODE_CHANGED
        // from Android 8.0 = API 26, StaticBroadcastReceiver can not receiver action without not set target project - setPackage("com.example.androidtraining")

        // 1. sendBroadcast
        Button sendUserBtn = findViewById(R.id.btn_send_user_action);
        sendUserBtn.setOnClickListener(v -> {
            Logger.d(TAG, "click btn_send_user_action, - sendBroadcast ");
            Intent intent = new Intent(USER_ACTION);
            intent.putExtra("action", "USER");
//            intent.setPackage("com.example.androidtraining");   // if not setPackage, StaticBroadcastReceiver can not receive this action
            // sendBroadcast for DynamicBroadcastReceiver - StaticBroadcastReceiver
            sendBroadcast(intent); // DynamicBroadcastReceiver only receive this action when app is running (not destroy) and not unregister it
        });

        Button sendNewBtn = findViewById(R.id.btn_send_new_action);
        sendNewBtn.setOnClickListener(v -> {
            Logger.d(TAG, "click btn_send_new_action, - sendBroadcast ");
            Intent intent = new Intent(NEW_ACTION);
            intent.putExtra("action", "NEW");
            intent.setPackage("com.example.androidtraining"); // if not setPackage, StaticBroadcastReceiver can not receive this action
            // sendBroadcast for DynamicBroadcastReceiver - StaticBroadcastReceiver
            sendBroadcast(intent);  // DynamicBroadcastReceiver only receive this action when app is running (not destroy) and not unregister it
        });

        Button sendLocalBtn = findViewById(R.id.btn_send_local_action);
        sendLocalBtn.setOnClickListener(v -> {
            Logger.d(TAG, "click btn_send_local_action, - sendBroadcast with package restriction");
            Intent intent = new Intent(LOCAL_ACTION);
            intent.putExtra("action", "LOCAL");
            intent.setPackage("com.example.androidtraining"); // through you setPackage but StaticBroadcastReceiver can not receive this action
            // LocalBroadcastManager: sendBroadcast
            LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
            // AI fixed
//            intent.setPackage("com.example.androidtraining"); // Restrict to our app package for security
//            // Use regular sendBroadcast instead of deprecated LocalBroadcastManager
//            sendBroadcast(intent);
        });

        // 2. registerReceiver
        // DynamicBroadcastReceiver: can receiver action only app running - not destroy
        myBroadcastReceiver = new DynamicBroadcastReceiver();
        IntentFilter intentFilter = new IntentFilter();
        // system action
        intentFilter.addAction(Intent.ACTION_AIRPLANE_MODE_CHANGED);
        intentFilter.addAction(Intent.ACTION_SCREEN_ON);
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF);
        // user define action
        intentFilter.addAction(USER_ACTION);
        intentFilter.addAction(NEW_ACTION);
        intentFilter.addAction(LOCAL_ACTION);

        // DynamicBroadcastReceiver: registerReceiver
        registerReceiver(myBroadcastReceiver, intentFilter, Context.RECEIVER_EXPORTED); // from API 33, need RECEIVER_EXPORTED / RECEIVER_NOT_EXPORTED for registerReceiver

        // Note: LocalBroadcastManager is deprecated.
        // For local broadcasts, use regular broadcasts with package restrictions
        // or consider using EventBus, LiveData, or other modern alternatives
        IntentFilter localIntentFilter = new IntentFilter();
        // system action
        localIntentFilter.addAction(Intent.ACTION_AIRPLANE_MODE_CHANGED);
        localIntentFilter.addAction(Intent.ACTION_SCREEN_ON);
        localIntentFilter.addAction(Intent.ACTION_SCREEN_OFF);
        // user define action
        localIntentFilter.addAction(USER_ACTION);
        localIntentFilter.addAction(NEW_ACTION);
        localIntentFilter.addAction(LOCAL_ACTION);
        LocalBroadcastManager.getInstance(this).registerReceiver(localBroadcastReceiver, localIntentFilter);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy: ");
        // unregisterReceiver with proper error handling
        try {
            if (myBroadcastReceiver != null) {
                unregisterReceiver(myBroadcastReceiver);
            }

            // LocalBroadcastManager: unregisterReceiver
            LocalBroadcastManager.getInstance(this).unregisterReceiver(localBroadcastReceiver);
            // Note: LocalBroadcastManager cleanup removed as it's deprecated
            // If using regular broadcasts for local communication, ensure proper cleanup
        } catch (IllegalArgumentException e) {
            Logger.d(TAG, "onDestroy: receiver not registered - " + e.getMessage());
        }
    }
}