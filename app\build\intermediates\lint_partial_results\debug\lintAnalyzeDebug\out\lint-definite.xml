<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.7.0" type="incidents">

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="78"
            column="9"
            startOffset="3026"
            endLine="78"
            endColumn="32"
            endOffset="3049"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="99"
            column="9"
            startOffset="3819"
            endLine="99"
            endColumn="32"
            endOffset="3842"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="110"
            column="9"
            startOffset="4200"
            endLine="110"
            endColumn="32"
            endOffset="4223"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="15"
            column="9"
            startOffset="604"
            endLine="15"
            endColumn="32"
            endOffset="627"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_snooze.xml"
            line="14"
            column="10"
            startOffset="573"
            endLine="14"
            endColumn="33"
            endOffset="596"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="28"
            column="9"
            startOffset="1060"
            endLine="28"
            endColumn="32"
            endOffset="1083"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="40"
            column="9"
            startOffset="1477"
            endLine="40"
            endColumn="32"
            endOffset="1500"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="65"
            column="9"
            startOffset="2355"
            endLine="65"
            endColumn="32"
            endOffset="2378"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="78"
            column="9"
            startOffset="2791"
            endLine="78"
            endColumn="32"
            endOffset="2814"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/file_row.xml"
            line="33"
            column="13"
            startOffset="1470"
            endLine="33"
            endColumn="36"
            endOffset="1493"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_one.xml"
            line="12"
            column="9"
            startOffset="452"
            endLine="12"
            endColumn="32"
            endOffset="475"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_two.xml"
            line="11"
            column="9"
            startOffset="447"
            endLine="11"
            endColumn="32"
            endOffset="470"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_song.xml"
            line="41"
            column="9"
            startOffset="1509"
            endLine="41"
            endColumn="32"
            endOffset="1532"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_song.xml"
            line="52"
            column="9"
            startOffset="1943"
            endLine="52"
            endColumn="32"
            endOffset="1966"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_song.xml"
            line="63"
            column="9"
            startOffset="2386"
            endLine="63"
            endColumn="32"
            endOffset="2409"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_user.xml"
            line="33"
            column="9"
            startOffset="1262"
            endLine="33"
            endColumn="32"
            endOffset="1285"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_user.xml"
            line="44"
            column="9"
            startOffset="1652"
            endLine="44"
            endColumn="32"
            endOffset="1675"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_user.xml"
            line="56"
            column="9"
            startOffset="2106"
            endLine="56"
            endColumn="32"
            endOffset="2129"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="48"
            column="13"
            startOffset="1932"
            endLine="48"
            endColumn="36"
            endOffset="1955"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="59"
            column="13"
            startOffset="2352"
            endLine="59"
            endColumn="36"
            endOffset="2375"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="70"
            column="13"
            startOffset="2769"
            endLine="70"
            endColumn="36"
            endOffset="2792"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableEndCompat` instead of `android:drawableEnd`">
        <fix-composite
            description="Use app namespace instead of android">
            <fix-attribute
                description="Set drawableEndCompat=&quot;@drawable/ic_arrow&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableEndCompat"
                value="@drawable/ic_arrow"/>
            <fix-attribute
                description="Delete drawableEnd"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableEnd"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="197"
            column="13"
            startOffset="7245"
            endLine="197"
            endColumn="53"
            endOffset="7285"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Use app namespace instead of android">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_arrow&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_arrow"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="198"
            column="13"
            startOffset="7298"
            endLine="198"
            endColumn="55"
            endOffset="7340"/>
    </incident>

    <incident
        id="ExportedReceiver"
        severity="warning"
        message="Exported receiver does not require permission">
        <fix-attribute
            description="Set permission"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="permission"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="118"
            column="10"
            startOffset="5196"
            endLine="118"
            endColumn="18"
            endOffset="5204"/>
    </incident>

    <incident
        id="ExportedService"
        severity="warning"
        message="Exported service does not require permission">
        <fix-alternatives>
            <fix-attribute
                description="Set permission"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="permission"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set exported=&quot;false&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="exported"
                value="false"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="133"
            column="10"
            startOffset="5775"
            endLine="133"
            endColumn="17"
            endOffset="5782"/>
    </incident>

    <incident
        id="ExportedService"
        severity="warning"
        message="Exported service does not require permission">
        <fix-alternatives>
            <fix-attribute
                description="Set permission"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="permission"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set exported=&quot;false&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="exported"
                value="false"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="136"
            column="10"
            startOffset="5897"
            endLine="136"
            endColumn="17"
            endOffset="5904"/>
    </incident>

    <incident
        id="ExportedService"
        severity="warning"
        message="Exported service does not require permission">
        <fix-alternatives>
            <fix-attribute
                description="Set permission"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="permission"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set exported=&quot;false&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="exported"
                value="false"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="140"
            column="10"
            startOffset="6062"
            endLine="140"
            endColumn="17"
            endOffset="6069"/>
    </incident>

    <incident
        id="ExportedService"
        severity="warning"
        message="Exported service does not require permission">
        <fix-alternatives>
            <fix-attribute
                description="Set permission"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="permission"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set exported=&quot;false&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="exported"
                value="false"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="144"
            column="10"
            startOffset="6227"
            endLine="144"
            endColumn="17"
            endOffset="6234"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/MainActivity.java"
            line="96"
            column="21"
            startOffset="4318"
            endLine="96"
            endColumn="67"
            endOffset="4364"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/MainActivity.java"
            line="135"
            column="17"
            startOffset="6253"
            endLine="135"
            endColumn="63"
            endOffset="6299"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="This folder configuration (`v23`) is unnecessary; `minSdkVersion` is 23. Merge all the resources in this folder into `values`.">
        <fix-data file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-v23" folderName="values" requiresApi="23"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-v23"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="This `AsyncTask` class should be static or leaks might occur (com.example.androidtraining.course.threadhandler.ThreadActivity.MyAsyncTask)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/threadhandler/ThreadActivity.java"
            line="246"
            column="11"
            startOffset="8597"
            endLine="246"
            endColumn="22"
            endOffset="8608"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/purple_200` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTraining`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_recycle_view.xml"
            line="6"
            column="5"
            startOffset="247"
            endLine="6"
            endColumn="43"
            endOffset="285"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/design_default_color_secondary` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTraining`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/view_layout_include.xml"
            line="5"
            column="5"
            startOffset="229"
            endLine="5"
            endColumn="63"
            endOffset="287"/>
    </incident>

    <incident
        id="IconXmlAndPng"
        severity="warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\drawable-anydpi\ic_arrow.xml, src\main\res\drawable-hdpi\ic_arrow.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_arrow.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_arrow.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_arrow.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_arrow.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_arrow.xml"/>
    </incident>

    <incident
        id="IconXmlAndPng"
        severity="warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\drawable-anydpi\ic_next.xml, src\main\res\drawable-hdpi\ic_next.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_next.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_next.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_next.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_next.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_next.xml"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive icon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="17"
            endOffset="272"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="17"
            endOffset="272"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="1353"
                    endOffset="3045"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="1780"
                    endOffset="2187"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="2199"
                    endOffset="2607"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="2619"
                    endOffset="3024"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="44"
            column="10"
            startOffset="1781"
            endLine="44"
            endColumn="16"
            endOffset="1787"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="1353"
                    endOffset="3045"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="1780"
                    endOffset="2187"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="2199"
                    endOffset="2607"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="2619"
                    endOffset="3024"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="55"
            column="10"
            startOffset="2200"
            endLine="55"
            endColumn="16"
            endOffset="2206"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="1353"
                    endOffset="3045"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="1780"
                    endOffset="2187"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="2199"
                    endOffset="2607"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
                    startOffset="2619"
                    endOffset="3024"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="66"
            column="10"
            startOffset="2620"
            endLine="66"
            endColumn="16"
            endOffset="2626"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="59"
            column="6"
            startOffset="2251"
            endLine="59"
            endColumn="14"
            endOffset="2259"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_share_prefs.xml"
            line="19"
            column="6"
            startOffset="717"
            endLine="19"
            endColumn="14"
            endOffset="725"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_top.xml"
            line="19"
            column="6"
            startOffset="723"
            endLine="19"
            endColumn="14"
            endOffset="731"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="59"
            column="6"
            startOffset="2251"
            endLine="59"
            endColumn="14"
            endOffset="2259"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_share_prefs.xml"
            line="19"
            column="6"
            startOffset="717"
            endLine="19"
            endColumn="14"
            endOffset="725"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_top.xml"
            line="19"
            column="6"
            startOffset="723"
            endLine="19"
            endColumn="14"
            endOffset="731"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_song.xml"
            line="28"
            column="6"
            startOffset="1060"
            endLine="28"
            endColumn="15"
            endOffset="1069"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_song.xml"
            line="71"
            column="6"
            startOffset="2690"
            endLine="71"
            endColumn="15"
            endOffset="2699"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/shareprefs/SharePrefsActivity.java"
            line="48"
            column="34"
            startOffset="1989"
            endLine="48"
            endColumn="51"
            endOffset="2006"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/shareprefs/SharePrefsActivity.java"
            line="48"
            column="34"
            startOffset="1989"
            endLine="48"
            endColumn="43"
            endOffset="1998"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/threadhandler/ThreadActivity.java"
            line="174"
            column="51"
            startOffset="6152"
            endLine="174"
            endColumn="62"
            endOffset="6163"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/threadhandler/ThreadActivity.java"
            line="210"
            column="34"
            startOffset="7264"
            endLine="210"
            endColumn="47"
            endOffset="7277"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/threadhandler/ThreadActivity.java"
            line="221"
            column="42"
            startOffset="7734"
            endLine="221"
            endColumn="65"
            endOffset="7757"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/threadhandler/ThreadActivity.java"
            line="221"
            column="42"
            startOffset="7734"
            endLine="221"
            endColumn="54"
            endOffset="7746"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/threadhandler/ThreadActivity.java"
            line="239"
            column="38"
            startOffset="8421"
            endLine="239"
            endColumn="78"
            endOffset="8461"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/threadhandler/ThreadActivity.java"
            line="239"
            column="51"
            startOffset="8434"
            endLine="239"
            endColumn="64"
            endOffset="8447"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/database/room/UserAdapter.java"
            line="67"
            column="26"
            startOffset="2193"
            endLine="67"
            endColumn="43"
            endOffset="2210"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Show List view&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_adapter.xml"
            line="24"
            column="9"
            startOffset="1019"
            endLine="24"
            endColumn="38"
            endOffset="1048"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Show Recycle view&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_adapter.xml"
            line="33"
            column="9"
            startOffset="1362"
            endLine="33"
            endColumn="41"
            endOffset="1394"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Alarm after 1 min - not repeating&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_alarm.xml"
            line="18"
            column="9"
            startOffset="788"
            endLine="18"
            endColumn="57"
            endOffset="836"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Alarm after 2 min - repeat 3 times for each 1 min&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_alarm.xml"
            line="29"
            column="9"
            startOffset="1233"
            endLine="29"
            endColumn="73"
            endOffset="1297"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Send User action&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_broadcast.xml"
            line="14"
            column="9"
            startOffset="586"
            endLine="14"
            endColumn="40"
            endOffset="617"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Send New action&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_broadcast.xml"
            line="25"
            column="9"
            startOffset="1009"
            endLine="25"
            endColumn="39"
            endOffset="1039"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Send Local action&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_broadcast.xml"
            line="36"
            column="9"
            startOffset="1419"
            endLine="36"
            endColumn="41"
            endOffset="1451"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Activity&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="22"
            column="9"
            startOffset="859"
            endLine="22"
            endColumn="37"
            endOffset="887"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test View Layout&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="32"
            column="9"
            startOffset="1238"
            endLine="32"
            endColumn="40"
            endOffset="1269"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Adapter&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="42"
            column="9"
            startOffset="1629"
            endLine="42"
            endColumn="36"
            endOffset="1656"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Broadcast receiver&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="52"
            column="9"
            startOffset="2030"
            endLine="52"
            endColumn="47"
            endOffset="2068"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Notification&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="62"
            column="9"
            startOffset="2432"
            endLine="62"
            endColumn="41"
            endOffset="2464"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="72"
            column="9"
            startOffset="2834"
            endLine="72"
            endColumn="36"
            endOffset="2861"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test SharePreference&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="82"
            column="9"
            startOffset="3229"
            endLine="82"
            endColumn="44"
            endOffset="3264"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Database&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="92"
            column="9"
            startOffset="3624"
            endLine="92"
            endColumn="37"
            endOffset="3652"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Thread - Handler&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="102"
            column="9"
            startOffset="4022"
            endLine="102"
            endColumn="45"
            endOffset="4058"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Alarm Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_course.xml"
            line="112"
            column="9"
            startOffset="4424"
            endLine="112"
            endColumn="42"
            endOffset="4457"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Button default&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_custom.xml"
            line="12"
            column="9"
            startOffset="502"
            endLine="12"
            endColumn="38"
            endOffset="531"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Button cusstom&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_custom.xml"
            line="22"
            column="9"
            startOffset="907"
            endLine="22"
            endColumn="38"
            endOffset="936"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Edit text of Activity&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="65"
            column="9"
            startOffset="2472"
            endLine="65"
            endColumn="45"
            endOffset="2508"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Show fragment 1 or fragment 2 below&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="77"
            column="9"
            startOffset="2967"
            endLine="77"
            endColumn="59"
            endOffset="3017"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Show Fragment 1&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="98"
            column="9"
            startOffset="3780"
            endLine="98"
            endColumn="39"
            endOffset="3810"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Show Fragment 2&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_example.xml"
            line="109"
            column="9"
            startOffset="4161"
            endLine="109"
            endColumn="39"
            endOffset="4191"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Start Training Course&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="14"
            column="9"
            startOffset="552"
            endLine="14"
            endColumn="45"
            endOffset="588"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Start XXX&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="25"
            column="9"
            startOffset="960"
            endLine="25"
            endColumn="33"
            endOffset="984"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Make notification&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml"
            line="14"
            column="9"
            startOffset="587"
            endLine="14"
            endColumn="41"
            endOffset="619"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Make Custom notification&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml"
            line="25"
            column="9"
            startOffset="1008"
            endLine="25"
            endColumn="48"
            endOffset="1047"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Count value is: &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="14"
            column="9"
            startOffset="564"
            endLine="14"
            endColumn="40"
            endOffset="595"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Start unbounded service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="28"
            column="9"
            startOffset="1109"
            endLine="28"
            endColumn="47"
            endOffset="1147"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Stop unbounded service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="39"
            column="9"
            startOffset="1525"
            endLine="39"
            endColumn="46"
            endOffset="1562"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Start bounded service local&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="50"
            column="9"
            startOffset="1964"
            endLine="50"
            endColumn="51"
            endOffset="2006"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Stop bounded service local&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="61"
            column="9"
            startOffset="2406"
            endLine="61"
            endColumn="50"
            endOffset="2447"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Start bounded service messenger&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="72"
            column="9"
            startOffset="2857"
            endLine="72"
            endColumn="55"
            endOffset="2903"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Stop bounded service messenger&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="83"
            column="9"
            startOffset="3311"
            endLine="83"
            endColumn="54"
            endOffset="3356"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Start bounded service AIDL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="94"
            column="9"
            startOffset="3765"
            endLine="94"
            endColumn="50"
            endOffset="3806"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Stop bounded service AIDL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_service.xml"
            line="105"
            column="9"
            startOffset="4213"
            endLine="105"
            endColumn="49"
            endOffset="4253"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Prefs&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_share_prefs.xml"
            line="12"
            column="9"
            startOffset="475"
            endLine="12"
            endColumn="33"
            endOffset="499"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Get Prefs&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_share_prefs.xml"
            line="34"
            column="9"
            startOffset="1284"
            endLine="34"
            endColumn="33"
            endOffset="1308"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Value: &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_share_prefs.xml"
            line="45"
            column="9"
            startOffset="1663"
            endLine="45"
            endColumn="31"
            endOffset="1685"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Snooze Activity&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_snooze.xml"
            line="13"
            column="9"
            startOffset="533"
            endLine="13"
            endColumn="39"
            endOffset="563"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Thread&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="16"
            column="9"
            startOffset="648"
            endLine="16"
            endColumn="35"
            endOffset="674"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Thread count: &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="29"
            column="9"
            startOffset="1092"
            endLine="29"
            endColumn="38"
            endOffset="1121"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="41"
            column="9"
            startOffset="1509"
            endLine="41"
            endColumn="25"
            endOffset="1525"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Async&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="53"
            column="9"
            startOffset="1928"
            endLine="53"
            endColumn="34"
            endOffset="1953"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;AsyncTask count: &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="66"
            column="9"
            startOffset="2387"
            endLine="66"
            endColumn="41"
            endOffset="2419"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="79"
            column="9"
            startOffset="2823"
            endLine="79"
            endColumn="25"
            endOffset="2839"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Executor&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="102"
            column="9"
            startOffset="3667"
            endLine="102"
            endColumn="37"
            endOffset="3695"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Future Concurrent&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_thread.xml"
            line="113"
            column="9"
            startOffset="4064"
            endLine="113"
            endColumn="46"
            endOffset="4101"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Button 11111 1111111 11111&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="30"
            column="9"
            startOffset="1101"
            endLine="30"
            endColumn="50"
            endOffset="1142"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Button 222222&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="49"
            column="9"
            startOffset="1745"
            endLine="49"
            endColumn="37"
            endOffset="1773"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Button 3&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="70"
            column="9"
            startOffset="2507"
            endLine="70"
            endColumn="32"
            endOffset="2530"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;But 1&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="82"
            column="9"
            startOffset="2921"
            endLine="82"
            endColumn="29"
            endOffset="2941"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;But 2&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="94"
            column="9"
            startOffset="3376"
            endLine="94"
            endColumn="29"
            endOffset="3396"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;But 3&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="105"
            column="9"
            startOffset="3772"
            endLine="105"
            endColumn="29"
            endOffset="3792"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;But 7&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="116"
            column="9"
            startOffset="4167"
            endLine="116"
            endColumn="29"
            endOffset="4187"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;But 4&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="128"
            column="9"
            startOffset="4604"
            endLine="128"
            endColumn="29"
            endOffset="4624"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;But 5&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="139"
            column="9"
            startOffset="4986"
            endLine="139"
            endColumn="29"
            endOffset="5006"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;But 6&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="151"
            column="9"
            startOffset="5436"
            endLine="151"
            endColumn="29"
            endOffset="5456"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Button frame&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="180"
            column="13"
            startOffset="6577"
            endLine="180"
            endColumn="40"
            endOffset="6604"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Frame Layout here, background grey&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_view_layout.xml"
            line="188"
            column="13"
            startOffset="6866"
            endLine="188"
            endColumn="62"
            endOffset="6915"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Name goes here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/file_row.xml"
            line="32"
            column="13"
            startOffset="1427"
            endLine="32"
            endColumn="42"
            endOffset="1456"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Bottom Fragment&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_bottom.xml"
            line="10"
            column="9"
            startOffset="408"
            endLine="10"
            endColumn="39"
            endOffset="438"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Content of EditText:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_bottom.xml"
            line="23"
            column="9"
            startOffset="871"
            endLine="23"
            endColumn="44"
            endOffset="906"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Content is shown here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_bottom.xml"
            line="35"
            column="9"
            startOffset="1329"
            endLine="35"
            endColumn="45"
            endOffset="1365"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Fragment 1-1-1&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_one.xml"
            line="11"
            column="9"
            startOffset="413"
            endLine="11"
            endColumn="38"
            endOffset="442"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Top Fragment&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_top.xml"
            line="10"
            column="9"
            startOffset="408"
            endLine="10"
            endColumn="36"
            endOffset="435"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Edit text here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_top.xml"
            line="23"
            column="9"
            startOffset="876"
            endLine="23"
            endColumn="38"
            endOffset="905"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Fragment 2-2-2&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_two.xml"
            line="10"
            column="9"
            startOffset="408"
            endLine="10"
            endColumn="38"
            endOffset="437"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Title&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="18"
            column="9"
            startOffset="727"
            endLine="18"
            endColumn="29"
            endOffset="747"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Activity&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="47"
            column="13"
            startOffset="1895"
            endLine="47"
            endColumn="36"
            endOffset="1918"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Broadcast&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="58"
            column="13"
            startOffset="2314"
            endLine="58"
            endColumn="37"
            endOffset="2338"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Service&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/notification_layout.xml"
            line="69"
            column="13"
            startOffset="2733"
            endLine="69"
            endColumn="35"
            endOffset="2755"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;include layout here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/view_layout_include.xml"
            line="12"
            column="9"
            startOffset="501"
            endLine="12"
            endColumn="43"
            endOffset="535"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Button include&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/view_layout_include.xml"
            line="23"
            column="9"
            startOffset="916"
            endLine="23"
            endColumn="38"
            endOffset="945"/>
    </incident>

</incidents>
