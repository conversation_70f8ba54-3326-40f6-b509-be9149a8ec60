<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/action_refresh"
        android:icon="@drawable/ic_refresh"
        android:title="Refresh"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_sort"
        android:icon="@drawable/ic_sort"
        android:title="Sort"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_filter"
        android:icon="@drawable/ic_filter"
        android:title="Filter"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_search"
        android:icon="@drawable/ic_search"
        android:title="Search"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_delete"
        android:icon="@drawable/ic_delete"
        android:title="Delete"
        android:visible="false"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_select_all"
        android:icon="@drawable/ic_select_all"
        android:title="Select All"
        android:visible="false"
        app:showAsAction="ifRoom" />

</menu>
