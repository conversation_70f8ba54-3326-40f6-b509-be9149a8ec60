-- Merging decision tree log ---
manifest
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:2:1-156:12
INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:2:1-156:12
INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:2:1-156:12
INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:2:1-156:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3e3a03410c222d389eb5acdf62f3d148\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\4c8c51a1a3225a01967f94847ffcc76d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\f33265535b69c0b3de4977a1d1da2c59\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\e7f24da73f275a8a791d04076c131d9f\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a43ce57c16c9c156e0fe94d42baa1a99\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\60115c31cdc3e8b7d77a5b34ad2884ea\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\4c1ba56281892686089b82b9f6e49a36\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a680feb8682ec44dcd6dd862fc9448df\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a27edbe248c372ba998d97f1205e0ef6\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a65eaaae0d1b7d25b57295104d4a28c9\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\cb9de1a9a83d1c3b2462665a6c1e9b5a\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\253ec1374eea80b56bfea2ee38477352\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11\transforms\037c63d66d92cec76ec9fb1a1da1532c\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\2441a2181b644ce9b70446048016e7e0\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\b3b1d1a6741186d7c23ba66b4144d119\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\cf6b953fe0a9083ee2ef794c8f55b21c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\9a8860da6e73b4fb9c97b73de56f7f5c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\c3c255fa386ca3a73fce55181abaf033\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f3067ca641cd059d8f2893f57dd39a34\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a95e2ed5d944959ab36e0e3c00e536d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\39d6e85f1ad5999a3e0145757fd105de\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f14b8119db62e65e810e4e827522aa24\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\368b07b4e9b7912a6e22f44deed31ddf\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a3457426da9bfa39b7e62e085bc7fe54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f3e0cdc654b8e56ca6b790da1ae7d5b0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a3b66ae65be2a3d58a88a61973e235c4\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f7dfb688e05bf61f4f3b680110338cbe\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\8ddc9c35ed5204c26b42af8bb94e5eeb\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\193d153386bf3b4b4b92269ab3d6da57\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\86c9a94664088b153a4e39504897a03e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\2f07d74e157a9f24338b8f6b5e7d266c\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\106c1a2a67ec136c928fddda77e8e745\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\7c3e0705a88a964252b4a934dbd92b11\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\889317c0397e8e088495ab6acb4e71dd\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\021aa5c135ea41246cd81f18aef73e08\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\6dbb5bc14392ca7ef978a3ebcee7010d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ae8587026035f2349876281c2f351bbe\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11\transforms\558d81a7edca70cb3fd7ddbc2a4d6d2d\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\b9d3d8229028e2208bbfcf80727e7065\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\1a9a64ae7cf7f7ba66ad00ea0bd568da\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\486dcb621057a2615fd863ee63882bae\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f16f45c511c2f635dab8a573d40f3214\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\3c3cfbba5619225bed27ba7293615253\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\97b68b6915836a266b4c96d904b3b653\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\40d8f25fe4ff665e385c34c138d2ffba\transformed\jetified-sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\d9cb1f4e7bfe99caf7b31d3aae060078\transformed\jetified-sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a7620b00852e149f80280ec99d39a934\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\90772ddcb9edf7e15b4df4328f8bebbb\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\2dca824aacace62393cb79264d3f3597\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\8251b772fd219c723e464ed51bffbd4d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\25a380b47674329a3f076148981b0f41\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\0b9035748fab9e9e0bc8ec7646af896f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\63a250dbc0f87c2711b5f5b21b596be4\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\50f3dd0cd019763749bbcd34c270c2b1\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a40f0a2eb78f9da6567867f757f1d6de\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\cbca15f31347e85319480744b347e3a8\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:5-77
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:22-74
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:5-80
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:8:5-10:40
	tools:ignore
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:10:9-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:9:9-66
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:5-14:38
	android:maxSdkVersion
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:14:9-35
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:22-76
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:15:5-74
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:15:22-71
uses-permission#com.android.alarm.permission.SET_ALARM
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:16:5-78
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:16:22-75
permission#com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:5-113
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:17-110
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:5-77
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:23:5-89
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:23:22-86
application
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:25:5-154:19
INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:25:5-154:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3e3a03410c222d389eb5acdf62f3d148\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3e3a03410c222d389eb5acdf62f3d148\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\4c8c51a1a3225a01967f94847ffcc76d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\4c8c51a1a3225a01967f94847ffcc76d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a680feb8682ec44dcd6dd862fc9448df\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a680feb8682ec44dcd6dd862fc9448df\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a27edbe248c372ba998d97f1205e0ef6\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a27edbe248c372ba998d97f1205e0ef6\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a65eaaae0d1b7d25b57295104d4a28c9\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a65eaaae0d1b7d25b57295104d4a28c9\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\253ec1374eea80b56bfea2ee38477352\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\253ec1374eea80b56bfea2ee38477352\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ae8587026035f2349876281c2f351bbe\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ae8587026035f2349876281c2f351bbe\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\97b68b6915836a266b4c96d904b3b653\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\97b68b6915836a266b4c96d904b3b653\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\0b9035748fab9e9e0bc8ec7646af896f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\0b9035748fab9e9e0bc8ec7646af896f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:33:9-35
	android:label
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:31:9-41
	android:fullBackupContent
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:29:9-54
	android:roundIcon
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:32:9-54
	tools:targetApi
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:35:9-29
	android:icon
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:30:9-43
	android:allowBackup
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:27:9-35
	android:theme
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:34:9-53
	android:dataExtractionRules
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:28:9-65
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:26:9-36
activity#com.example.androidtraining.MainActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:37:9-46:20
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:40:13-36
	android:configChanges
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:39:13-88
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:38:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:41:13-45:29
action#android.intent.action.MAIN
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:17-69
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:17-77
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:27-74
activity#com.example.androidtraining.course.CourseActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:48:9-62:20
	android:launchMode
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:52:13-48
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:51:13-36
	android:configChanges
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:50:13-88
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:49:13-50
meta-data#android.app.lib_name
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:59:13-61:36
	android:value
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:61:17-33
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:60:17-52
activity#com.example.androidtraining.course.alarmservice.SnoozeActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:64:9-67:40
	android:launchMode
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:66:13-48
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:67:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:65:13-63
activity#com.example.androidtraining.course.alarmservice.AlarmActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:69:9-71:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:71:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:70:13-62
activity#com.example.androidtraining.course.viewlayout.ViewLayoutActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:73:9-75:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:75:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:74:13-65
activity#com.example.androidtraining.course.activityfragment.ExampleActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:77:9-79:39
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:79:13-36
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:78:13-68
activity#com.example.androidtraining.course.adapter.AdapterActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:80:9-82:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:82:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:81:13-59
activity#com.example.androidtraining.course.broadcastreceiver.BroadcastActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:83:9-85:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:85:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:84:13-71
activity#com.example.androidtraining.course.notification.NotificationActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:86:9-88:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:88:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:87:13-69
activity#com.example.androidtraining.course.service.ServiceActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:89:9-91:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:91:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:90:13-59
activity#com.example.androidtraining.course.shareprefs.SharePrefsActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:92:9-94:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:94:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:93:13-65
activity#com.example.androidtraining.course.database.DatabaseActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:95:9-97:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:97:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:96:13-61
activity#com.example.androidtraining.course.threadhandler.ThreadActivity
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:98:9-100:40
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:100:13-37
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:99:13-64
receiver#com.example.androidtraining.course.broadcastreceiver.StaticBroadcastReceiver
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:102:9-116:20
	android:enabled
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:104:13-35
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:105:13-36
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:103:13-77
intent-filter#action:name:android.intent.action.AIRPLANE_MODE+action:name:android.intent.action.SCREEN_OFF+action:name:android.intent.action.SCREEN_ON+action:name:com.example.androidtraining.LOCAL_ACTION+action:name:com.example.androidtraining.NEW_ACTION+action:name:com.example.androidtraining.USER_ACTION
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:106:13-115:29
action#android.intent.action.AIRPLANE_MODE
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:108:17-78
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:108:25-75
action#android.intent.action.SCREEN_ON
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:109:17-74
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:109:25-71
action#android.intent.action.SCREEN_OFF
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:110:17-75
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:110:25-72
action#com.example.androidtraining.USER_ACTION
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:112:17-82
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:112:25-79
action#com.example.androidtraining.NEW_ACTION
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:17-81
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:25-78
action#com.example.androidtraining.LOCAL_ACTION
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:17-83
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:25-80
receiver#com.example.androidtraining.course.alarmservice.AlarmReceiver
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:118:9-125:20
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:120:13-36
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:119:13-62
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:com.example.androidtraining.ALARM_ACTION
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:121:13-124:29
action#com.example.androidtraining.ALARM_ACTION
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:122:17-83
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:122:25-80
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:17-79
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:25-76
service#com.example.androidtraining.course.service.BackgroundService
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:127:9-69
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:127:18-66
service#com.example.androidtraining.course.service.ForegroundService
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:129:9-131:58
	android:foregroundServiceType
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:131:13-55
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:130:13-61
service#com.example.androidtraining.course.service.BoundedServiceLocal
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:133:9-135:39
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:135:13-36
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:134:13-63
service#com.example.androidtraining.course.service.BoundedServiceMessenger
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:136:9-139:41
	android:process
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:139:13-38
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:138:13-36
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:137:13-67
service#com.example.androidtraining.course.service.BoundedServiceAIDLBasic
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:140:9-143:41
	android:process
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:143:13-38
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:142:13-36
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:141:13-67
service#com.example.androidtraining.course.service.BoundedServiceAidlObject
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:144:9-146:39
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:146:13-36
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:145:13-68
provider#com.example.androidtraining.course.database.AppDbContentProvider
ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:148:9-153:115
	android:enabled
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:151:13-35
	android:authorities
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:150:13-92
	android:exported
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:152:13-36
	android:permission
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:153:13-112
	android:name
		ADDED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:149:13-65
uses-sdk
INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml
INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3e3a03410c222d389eb5acdf62f3d148\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3e3a03410c222d389eb5acdf62f3d148\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\4c8c51a1a3225a01967f94847ffcc76d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\4c8c51a1a3225a01967f94847ffcc76d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\f33265535b69c0b3de4977a1d1da2c59\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\f33265535b69c0b3de4977a1d1da2c59\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\e7f24da73f275a8a791d04076c131d9f\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\e7f24da73f275a8a791d04076c131d9f\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a43ce57c16c9c156e0fe94d42baa1a99\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a43ce57c16c9c156e0fe94d42baa1a99\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\60115c31cdc3e8b7d77a5b34ad2884ea\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\60115c31cdc3e8b7d77a5b34ad2884ea\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\4c1ba56281892686089b82b9f6e49a36\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\4c1ba56281892686089b82b9f6e49a36\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a680feb8682ec44dcd6dd862fc9448df\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a680feb8682ec44dcd6dd862fc9448df\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a27edbe248c372ba998d97f1205e0ef6\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a27edbe248c372ba998d97f1205e0ef6\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a65eaaae0d1b7d25b57295104d4a28c9\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a65eaaae0d1b7d25b57295104d4a28c9\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\cb9de1a9a83d1c3b2462665a6c1e9b5a\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\cb9de1a9a83d1c3b2462665a6c1e9b5a\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\253ec1374eea80b56bfea2ee38477352\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\253ec1374eea80b56bfea2ee38477352\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11\transforms\037c63d66d92cec76ec9fb1a1da1532c\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11\transforms\037c63d66d92cec76ec9fb1a1da1532c\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\2441a2181b644ce9b70446048016e7e0\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\2441a2181b644ce9b70446048016e7e0\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\b3b1d1a6741186d7c23ba66b4144d119\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\b3b1d1a6741186d7c23ba66b4144d119\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\cf6b953fe0a9083ee2ef794c8f55b21c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\cf6b953fe0a9083ee2ef794c8f55b21c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\9a8860da6e73b4fb9c97b73de56f7f5c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\9a8860da6e73b4fb9c97b73de56f7f5c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\c3c255fa386ca3a73fce55181abaf033\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\c3c255fa386ca3a73fce55181abaf033\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f3067ca641cd059d8f2893f57dd39a34\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f3067ca641cd059d8f2893f57dd39a34\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a95e2ed5d944959ab36e0e3c00e536d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a95e2ed5d944959ab36e0e3c00e536d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\39d6e85f1ad5999a3e0145757fd105de\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\39d6e85f1ad5999a3e0145757fd105de\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f14b8119db62e65e810e4e827522aa24\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f14b8119db62e65e810e4e827522aa24\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\368b07b4e9b7912a6e22f44deed31ddf\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\368b07b4e9b7912a6e22f44deed31ddf\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a3457426da9bfa39b7e62e085bc7fe54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a3457426da9bfa39b7e62e085bc7fe54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f3e0cdc654b8e56ca6b790da1ae7d5b0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f3e0cdc654b8e56ca6b790da1ae7d5b0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a3b66ae65be2a3d58a88a61973e235c4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a3b66ae65be2a3d58a88a61973e235c4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f7dfb688e05bf61f4f3b680110338cbe\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f7dfb688e05bf61f4f3b680110338cbe\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\8ddc9c35ed5204c26b42af8bb94e5eeb\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\8ddc9c35ed5204c26b42af8bb94e5eeb\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\193d153386bf3b4b4b92269ab3d6da57\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\193d153386bf3b4b4b92269ab3d6da57\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\86c9a94664088b153a4e39504897a03e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\86c9a94664088b153a4e39504897a03e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\2f07d74e157a9f24338b8f6b5e7d266c\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\2f07d74e157a9f24338b8f6b5e7d266c\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\106c1a2a67ec136c928fddda77e8e745\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\106c1a2a67ec136c928fddda77e8e745\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\7c3e0705a88a964252b4a934dbd92b11\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\7c3e0705a88a964252b4a934dbd92b11\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\889317c0397e8e088495ab6acb4e71dd\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\889317c0397e8e088495ab6acb4e71dd\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\021aa5c135ea41246cd81f18aef73e08\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\021aa5c135ea41246cd81f18aef73e08\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\6dbb5bc14392ca7ef978a3ebcee7010d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\6dbb5bc14392ca7ef978a3ebcee7010d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ae8587026035f2349876281c2f351bbe\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ae8587026035f2349876281c2f351bbe\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11\transforms\558d81a7edca70cb3fd7ddbc2a4d6d2d\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11\transforms\558d81a7edca70cb3fd7ddbc2a4d6d2d\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\b9d3d8229028e2208bbfcf80727e7065\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\b9d3d8229028e2208bbfcf80727e7065\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\1a9a64ae7cf7f7ba66ad00ea0bd568da\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\1a9a64ae7cf7f7ba66ad00ea0bd568da\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\486dcb621057a2615fd863ee63882bae\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\486dcb621057a2615fd863ee63882bae\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f16f45c511c2f635dab8a573d40f3214\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\f16f45c511c2f635dab8a573d40f3214\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\3c3cfbba5619225bed27ba7293615253\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\3c3cfbba5619225bed27ba7293615253\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\97b68b6915836a266b4c96d904b3b653\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\97b68b6915836a266b4c96d904b3b653\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\40d8f25fe4ff665e385c34c138d2ffba\transformed\jetified-sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\40d8f25fe4ff665e385c34c138d2ffba\transformed\jetified-sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\d9cb1f4e7bfe99caf7b31d3aae060078\transformed\jetified-sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\d9cb1f4e7bfe99caf7b31d3aae060078\transformed\jetified-sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a7620b00852e149f80280ec99d39a934\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a7620b00852e149f80280ec99d39a934\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\90772ddcb9edf7e15b4df4328f8bebbb\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\90772ddcb9edf7e15b4df4328f8bebbb\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\2dca824aacace62393cb79264d3f3597\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\2dca824aacace62393cb79264d3f3597\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\8251b772fd219c723e464ed51bffbd4d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\8251b772fd219c723e464ed51bffbd4d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\25a380b47674329a3f076148981b0f41\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\25a380b47674329a3f076148981b0f41\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\0b9035748fab9e9e0bc8ec7646af896f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\0b9035748fab9e9e0bc8ec7646af896f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\63a250dbc0f87c2711b5f5b21b596be4\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\63a250dbc0f87c2711b5f5b21b596be4\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\50f3dd0cd019763749bbcd34c270c2b1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\50f3dd0cd019763749bbcd34c270c2b1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a40f0a2eb78f9da6567867f757f1d6de\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\a40f0a2eb78f9da6567867f757f1d6de\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\cbca15f31347e85319480744b347e3a8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\cbca15f31347e85319480744b347e3a8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
uses-permission#android.permission.INTERNET
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar
ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:25:13-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\0b9035748fab9e9e0bc8ec7646af896f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\0b9035748fab9e9e0bc8ec7646af896f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
