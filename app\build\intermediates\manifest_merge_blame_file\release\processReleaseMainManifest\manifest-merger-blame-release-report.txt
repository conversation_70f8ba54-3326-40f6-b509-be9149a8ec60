1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.androidtraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Storage permissions: API < 30 -->
11-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:5-77
11-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:5-80
12-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Storage permissions: API < 30 - MANAGE_EXTERNAL_STORAGE & request "All files access" permission -->
13-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:5-81
13-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:8:5-10:40
14-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:9:9-66
15
16    <!-- Alarm service -->
17    <uses-permission
17-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:5-14:38
18        android:name="android.permission.SCHEDULE_EXACT_ALARM"
18-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:22-76
19        android:maxSdkVersion="32" />
19-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:14:9-35
20    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
20-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:15:5-74
20-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:15:22-71
21    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
21-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:16:5-78
21-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:16:22-75
22
23    <permission android:name="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />
23-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:5-113
23-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:17-110
24
25    <!-- API 33+ -->
26    <!-- Foreground Service: need foregroundServiceType & permission: FOREGROUND_SERVICE & FOREGROUND_SERVICE_SPECIAL_USE -->
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
27-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:5-77
27-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:22-74
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
28-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:23:5-89
28-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:23:22-86
29    <uses-permission android:name="android.permission.INTERNET" />
29-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
29-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:22-64
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
30-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
31    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
31-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b0355994fee210e5ee5d9497dea35968\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
31-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b0355994fee210e5ee5d9497dea35968\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
32
33    <permission
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\095e20247a4fac85d303d824da9b477b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\095e20247a4fac85d303d824da9b477b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\095e20247a4fac85d303d824da9b477b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\095e20247a4fac85d303d824da9b477b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\095e20247a4fac85d303d824da9b477b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:25:5-154:19
40        android:name="com.example.androidtraining.app.MainApp"
40-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:26:9-36
41        android:allowBackup="true"
41-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:27:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\095e20247a4fac85d303d824da9b477b\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:28:9-65
44        android:extractNativeLibs="false"
45        android:fullBackupContent="@xml/backup_rules"
45-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:29:9-54
46        android:icon="@mipmap/ic_launcher"
46-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:30:9-43
47        android:label="@string/app_name"
47-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:31:9-41
48        android:roundIcon="@mipmap/ic_launcher_round"
48-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:32:9-54
49        android:supportsRtl="true"
49-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:33:9-35
50        android:theme="@style/Theme.AndroidTraining" >
50-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:34:9-53
51        <activity
51-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:37:9-46:20
52            android:name="com.example.androidtraining.MainActivity"
52-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:38:13-41
53            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
53-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:39:13-88
54            android:exported="true" >
54-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:40:13-36
55            <intent-filter>
55-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:41:13-45:29
56                <action android:name="android.intent.action.MAIN" />
56-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:17-69
56-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:25-66
57
58                <category android:name="android.intent.category.LAUNCHER" />
58-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:17-77
58-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:27-74
59            </intent-filter>
60        </activity>
61        <activity
61-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:48:9-62:20
62            android:name="com.example.androidtraining.course.CourseActivity"
62-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:49:13-50
63            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
63-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:50:13-88
64            android:exported="true"
64-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:51:13-36
65            android:launchMode="singleInstance" >
65-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:52:13-48
66
67            <!-- <intent-filter> -->
68            <!-- <action android:name="android.intent.action.MAIN" /> -->
69            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
70            <!-- </intent-filter> -->
71
72            <meta-data
72-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:59:13-61:36
73                android:name="android.app.lib_name"
73-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:60:17-52
74                android:value="" />
74-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:61:17-33
75        </activity>
76        <activity
76-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:64:9-67:40
77            android:name="com.example.androidtraining.course.alarmservice.SnoozeActivity"
77-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:65:13-63
78            android:exported="false"
78-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:67:13-37
79            android:launchMode="singleInstance" />
79-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:66:13-48
80        <activity
80-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:69:9-71:40
81            android:name="com.example.androidtraining.course.alarmservice.AlarmActivity"
81-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:70:13-62
82            android:exported="false" />
82-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:71:13-37
83        <activity
83-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:73:9-75:40
84            android:name="com.example.androidtraining.course.viewlayout.ViewLayoutActivity"
84-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:74:13-65
85            android:exported="false" />
85-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:75:13-37
86        <activity
86-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:77:9-79:39
87            android:name="com.example.androidtraining.course.activityfragment.ExampleActivity"
87-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:78:13-68
88            android:exported="true" />
88-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:79:13-36
89        <activity
89-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:80:9-82:40
90            android:name="com.example.androidtraining.course.adapter.AdapterActivity"
90-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:81:13-59
91            android:exported="false" />
91-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:82:13-37
92        <activity
92-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:83:9-85:40
93            android:name="com.example.androidtraining.course.broadcastreceiver.BroadcastActivity"
93-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:84:13-71
94            android:exported="false" />
94-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:85:13-37
95        <activity
95-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:86:9-88:40
96            android:name="com.example.androidtraining.course.notification.NotificationActivity"
96-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:87:13-69
97            android:exported="false" />
97-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:88:13-37
98        <activity
98-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:89:9-91:40
99            android:name="com.example.androidtraining.course.service.ServiceActivity"
99-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:90:13-59
100            android:exported="false" />
100-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:91:13-37
101        <activity
101-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:92:9-94:40
102            android:name="com.example.androidtraining.course.shareprefs.SharePrefsActivity"
102-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:93:13-65
103            android:exported="false" />
103-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:94:13-37
104        <activity
104-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:95:9-97:40
105            android:name="com.example.androidtraining.course.database.DatabaseActivity"
105-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:96:13-61
106            android:exported="false" />
106-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:97:13-37
107        <activity
107-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:98:9-100:40
108            android:name="com.example.androidtraining.course.threadhandler.ThreadActivity"
108-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:99:13-64
109            android:exported="false" />
109-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:100:13-37
110
111        <receiver
111-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:102:9-116:20
112            android:name="com.example.androidtraining.course.broadcastreceiver.StaticBroadcastReceiver"
112-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:103:13-77
113            android:enabled="true"
113-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:104:13-35
114            android:exported="true" >
114-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:105:13-36
115            <intent-filter>
115-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:106:13-115:29
116
117                <!-- system action -->
118                <action android:name="android.intent.action.AIRPLANE_MODE" />
118-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:108:17-78
118-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:108:25-75
119                <action android:name="android.intent.action.SCREEN_ON" />
119-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:109:17-74
119-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:109:25-71
120                <action android:name="android.intent.action.SCREEN_OFF" />
120-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:110:17-75
120-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:110:25-72
121                <!-- user define action -->
122                <action android:name="com.example.androidtraining.USER_ACTION" />
122-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:112:17-82
122-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:112:25-79
123                <action android:name="com.example.androidtraining.NEW_ACTION" />
123-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:17-81
123-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:25-78
124                <action android:name="com.example.androidtraining.LOCAL_ACTION" />
124-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:17-83
124-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:25-80
125            </intent-filter>
126        </receiver>
127        <receiver
127-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:118:9-125:20
128            android:name="com.example.androidtraining.course.alarmservice.AlarmReceiver"
128-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:119:13-62
129            android:exported="true" >
129-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:120:13-36
130            <intent-filter>
130-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:121:13-124:29
131                <action android:name="com.example.androidtraining.ALARM_ACTION" />
131-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:122:17-83
131-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:122:25-80
132                <action android:name="android.intent.action.BOOT_COMPLETED" />
132-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:17-79
132-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:25-76
133            </intent-filter>
134        </receiver>
135
136        <service android:name="com.example.androidtraining.course.service.BackgroundService" />
136-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:127:9-69
136-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:127:18-66
137        <service
137-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:129:9-131:58
138            android:name="com.example.androidtraining.course.service.ForegroundService"
138-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:130:13-61
139            android:foregroundServiceType="specialUse" />
139-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:131:13-55
140        <service
140-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:133:9-135:39
141            android:name="com.example.androidtraining.course.service.BoundedServiceLocal"
141-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:134:13-63
142            android:exported="true" />
142-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:135:13-36
143        <service
143-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:136:9-139:41
144            android:name="com.example.androidtraining.course.service.BoundedServiceMessenger"
144-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:137:13-67
145            android:exported="true"
145-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:138:13-36
146            android:process=":remote" />
146-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:139:13-38
147        <service
147-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:140:9-143:41
148            android:name="com.example.androidtraining.course.service.BoundedServiceAIDLBasic"
148-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:141:13-67
149            android:exported="true"
149-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:142:13-36
150            android:process=":remote" />
150-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:143:13-38
151        <service
151-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:144:9-146:39
152            android:name="com.example.androidtraining.course.service.BoundedServiceAidlObject"
152-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:145:13-68
153            android:exported="true" />
153-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:146:13-36
154
155        <provider
155-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:148:9-153:51
156            android:name="com.example.androidtraining.course.database.AppDbContentProvider"
156-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:149:13-65
157            android:authorities="com.example.androidtraining.provider.AppDbContentProvider"
157-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:150:13-92
158            android:enabled="true"
158-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:151:13-35
159            android:exported="false"
159-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:152:13-37
160            android:grantUriPermissions="false" />
160-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:153:13-48
161
162        <service
162-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fcc945a0986f9fe43f7315d06600e81\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
163            android:name="com.google.firebase.components.ComponentDiscoveryService"
163-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fcc945a0986f9fe43f7315d06600e81\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
164            android:directBootAware="true"
164-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
165            android:exported="false" >
165-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fcc945a0986f9fe43f7315d06600e81\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
166            <meta-data
166-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fcc945a0986f9fe43f7315d06600e81\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
167                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
167-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fcc945a0986f9fe43f7315d06600e81\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9fcc945a0986f9fe43f7315d06600e81\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
169            <meta-data
169-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
170                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
170-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
172            <meta-data
172-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e0afe85e101cafd267cc11d0746eb83\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
173                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
173-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e0afe85e101cafd267cc11d0746eb83\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e0afe85e101cafd267cc11d0746eb83\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
175            <meta-data
175-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2fa89eab181b78ae8709f9dd16524663\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
176                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
176-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2fa89eab181b78ae8709f9dd16524663\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2fa89eab181b78ae8709f9dd16524663\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
178            <meta-data
178-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2fa89eab181b78ae8709f9dd16524663\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
179                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
179-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2fa89eab181b78ae8709f9dd16524663\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2fa89eab181b78ae8709f9dd16524663\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
181            <meta-data
181-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d44b6552f841c62f62c61e8a4437c119\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
182                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
182-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d44b6552f841c62f62c61e8a4437c119\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d44b6552f841c62f62c61e8a4437c119\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
184            <meta-data
184-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
185                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
185-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
187        </service>
188
189        <activity
189-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
190            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
190-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
191            android:excludeFromRecents="true"
191-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
192            android:exported="true"
192-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
193            android:launchMode="singleTask"
193-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
194            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
194-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
195            <intent-filter>
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
196                <action android:name="android.intent.action.VIEW" />
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
197
198                <category android:name="android.intent.category.DEFAULT" />
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
199                <category android:name="android.intent.category.BROWSABLE" />
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
200
201                <data
201-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
202                    android:host="firebase.auth"
202-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
203                    android:path="/"
203-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
204                    android:scheme="genericidp" />
204-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
205            </intent-filter>
206        </activity>
207        <activity
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
208            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
208-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
209            android:excludeFromRecents="true"
209-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
210            android:exported="true"
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
211            android:launchMode="singleTask"
211-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
212            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
212-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
213            <intent-filter>
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
214                <action android:name="android.intent.action.VIEW" />
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
215
216                <category android:name="android.intent.category.DEFAULT" />
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
217                <category android:name="android.intent.category.BROWSABLE" />
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
218
219                <data
219-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
220                    android:host="firebase.auth"
220-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
221                    android:path="/"
221-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
222                    android:scheme="recaptcha" />
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3dd77cb038cc3a7df61844966ed8608\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
223            </intent-filter>
224        </activity>
225
226        <service
226-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
227            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
227-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
228            android:enabled="true"
228-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
229            android:exported="false" >
229-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
230            <meta-data
230-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
231                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
231-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
232                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
232-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
233        </service>
234
235        <activity
235-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
236            android:name="androidx.credentials.playservices.HiddenActivity"
236-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
237            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
237-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
238            android:enabled="true"
238-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
239            android:exported="false"
239-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
240            android:fitsSystemWindows="true"
240-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
241            android:theme="@style/Theme.Hidden" >
241-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
242        </activity>
243        <activity
243-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
244            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
244-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
245            android:excludeFromRecents="true"
245-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
246            android:exported="false"
246-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
247            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
247-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
248        <!--
249            Service handling Google Sign-In user revocation. For apps that do not integrate with
250            Google Sign-In, this service will never be started.
251        -->
252        <service
252-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
253            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
253-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
254            android:exported="true"
254-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
255            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
255-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
256            android:visibleToInstantApps="true" />
256-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
257
258        <provider
258-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
259            android:name="com.google.firebase.provider.FirebaseInitProvider"
259-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
260            android:authorities="com.example.androidtraining.firebaseinitprovider"
260-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
261            android:directBootAware="true"
261-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
262            android:exported="false"
262-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
263            android:initOrder="100" />
263-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
264
265        <activity
265-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc9cb779b45efc5b219ad065a80ba44f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
266            android:name="com.google.android.gms.common.api.GoogleApiActivity"
266-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc9cb779b45efc5b219ad065a80ba44f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
267            android:exported="false"
267-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc9cb779b45efc5b219ad065a80ba44f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
268            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
268-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc9cb779b45efc5b219ad065a80ba44f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
269
270        <service
270-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a827a7569734c096f6c51754843b6f79\transformed\jetified-room-runtime-release\AndroidManifest.xml:24:9-28:63
271            android:name="androidx.room.MultiInstanceInvalidationService"
271-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a827a7569734c096f6c51754843b6f79\transformed\jetified-room-runtime-release\AndroidManifest.xml:25:13-74
272            android:directBootAware="true"
272-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a827a7569734c096f6c51754843b6f79\transformed\jetified-room-runtime-release\AndroidManifest.xml:26:13-43
273            android:exported="false" />
273-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a827a7569734c096f6c51754843b6f79\transformed\jetified-room-runtime-release\AndroidManifest.xml:27:13-37
274
275        <provider
275-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\70fb0731d49f0f40a2e251129eafd12c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
276            android:name="androidx.startup.InitializationProvider"
276-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\70fb0731d49f0f40a2e251129eafd12c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
277            android:authorities="com.example.androidtraining.androidx-startup"
277-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\70fb0731d49f0f40a2e251129eafd12c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
278            android:exported="false" >
278-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\70fb0731d49f0f40a2e251129eafd12c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
279            <meta-data
279-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\70fb0731d49f0f40a2e251129eafd12c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
280                android:name="androidx.emoji2.text.EmojiCompatInitializer"
280-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\70fb0731d49f0f40a2e251129eafd12c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
281                android:value="androidx.startup" />
281-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\70fb0731d49f0f40a2e251129eafd12c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
282            <meta-data
282-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfc8dafba1ab47ce24cc0ddd91aa7655\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
283                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
283-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfc8dafba1ab47ce24cc0ddd91aa7655\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
284                android:value="androidx.startup" />
284-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfc8dafba1ab47ce24cc0ddd91aa7655\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
285            <meta-data
285-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
286                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
286-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
287                android:value="androidx.startup" />
287-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
288        </provider>
289
290        <meta-data
290-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\080c78dea5e8cf5cc3e1cd266282ad7e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
291            android:name="com.google.android.gms.version"
291-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\080c78dea5e8cf5cc3e1cd266282ad7e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
292            android:value="@integer/google_play_services_version" />
292-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\080c78dea5e8cf5cc3e1cd266282ad7e\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
293
294        <receiver
294-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
295            android:name="androidx.profileinstaller.ProfileInstallReceiver"
295-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
296            android:directBootAware="false"
296-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
297            android:enabled="true"
297-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
298            android:exported="true"
298-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
299            android:permission="android.permission.DUMP" >
299-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
300            <intent-filter>
300-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
301                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
301-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
301-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
302            </intent-filter>
303            <intent-filter>
303-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
304                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
304-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
304-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
305            </intent-filter>
306            <intent-filter>
306-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
307                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
307-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
307-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
308            </intent-filter>
309            <intent-filter>
309-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
310                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
311            </intent-filter>
312        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
313        <activity
313-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\2160d4f6b58cf79ed05103e1c9204437\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
314            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
314-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\2160d4f6b58cf79ed05103e1c9204437\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
315            android:exported="false"
315-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\2160d4f6b58cf79ed05103e1c9204437\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
316            android:stateNotNeeded="true"
316-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\2160d4f6b58cf79ed05103e1c9204437\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
317            android:theme="@style/Theme.PlayCore.Transparent" />
317-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\2160d4f6b58cf79ed05103e1c9204437\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
318    </application>
319
320</manifest>
