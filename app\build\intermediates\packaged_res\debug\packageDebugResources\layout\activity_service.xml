<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.service.ServiceActivity">

    <TextView
        android:id="@+id/tv_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Count value is: "
        android:textSize="18dp"
        android:textStyle="bold"
        android:layout_marginTop="30dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_start_unbounded_service"
        />

    <Button
        android:id="@+id/btn_start_unbounded_service"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Start unbounded service"
        android:layout_marginTop="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_count"
        />

    <Button
        android:id="@+id/btn_stop_unbounded_service"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Stop unbounded service"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_start_unbounded_service"
        />

    <Button
        android:id="@+id/btn_start_bounded_service_local"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Start bounded service local"
        android:layout_marginTop="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_stop_unbounded_service"
        />

    <Button
        android:id="@+id/btn_stop_bounded_service_local"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Stop bounded service local"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_start_bounded_service_local"
        />

    <Button
        android:id="@+id/btn_start_bounded_service_messenger"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Start bounded service messenger"
        android:layout_marginTop="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_stop_bounded_service_local"
        />

    <Button
        android:id="@+id/btn_stop_bounded_service_messenger"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Stop bounded service messenger"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_start_bounded_service_messenger"
        />

    <Button
        android:id="@+id/btn_start_bounded_service_aidl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Start bounded service AIDL"
        android:layout_marginTop="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_stop_bounded_service_messenger"
        />

    <Button
        android:id="@+id/btn_stop_bounded_service_aidl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Stop bounded service AIDL"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_start_bounded_service_aidl"
        />

</androidx.constraintlayout.widget.ConstraintLayout>