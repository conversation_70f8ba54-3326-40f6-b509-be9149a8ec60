<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res"><file name="ic_launcher_background" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_arrow" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-anydpi\ic_arrow.xml" qualifiers="anydpi-v4" type="drawable"/><file name="ic_next" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-anydpi\ic_next.xml" qualifiers="anydpi-v4" type="drawable"/><file name="ic_arrow" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-hdpi\ic_arrow.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_next" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-hdpi\ic_next.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_arrow" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-mdpi\ic_arrow.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_next" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-mdpi\ic_next.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="ic_arrow" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xhdpi\ic_arrow.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_next" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xhdpi\ic_next.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_arrow" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xxhdpi\ic_arrow.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_next" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xxhdpi\ic_next.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="activity_adapter" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_adapter.xml" qualifiers="" type="layout"/><file name="activity_alarm" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_alarm.xml" qualifiers="" type="layout"/><file name="activity_broadcast" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_broadcast.xml" qualifiers="" type="layout"/><file name="activity_course" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml" qualifiers="" type="layout"/><file name="activity_custom" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_custom.xml" qualifiers="" type="layout"/><file name="activity_database" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_database.xml" qualifiers="" type="layout"/><file name="activity_example" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_notification" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_notification.xml" qualifiers="" type="layout"/><file name="activity_service" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml" qualifiers="" type="layout"/><file name="activity_share_prefs" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml" qualifiers="" type="layout"/><file name="activity_snooze" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_snooze.xml" qualifiers="" type="layout"/><file name="activity_thread" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml" qualifiers="" type="layout"/><file name="activity_view_layout" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml" qualifiers="" type="layout"/><file name="file_row" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\file_row.xml" qualifiers="" type="layout"/><file name="fragment_bottom" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_bottom.xml" qualifiers="" type="layout"/><file name="fragment_list_view" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_list_view.xml" qualifiers="" type="layout"/><file name="fragment_one" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_one.xml" qualifiers="" type="layout"/><file name="fragment_recycle_view" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_recycle_view.xml" qualifiers="" type="layout"/><file name="fragment_top" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml" qualifiers="" type="layout"/><file name="fragment_two" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_two.xml" qualifiers="" type="layout"/><file name="item_list_song" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml" qualifiers="" type="layout"/><file name="item_list_user" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml" qualifiers="" type="layout"/><file name="notification_layout" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml" qualifiers="" type="layout"/><file name="view_layout_include" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\view_layout_include.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values\dimen.xml" qualifiers=""><dimen name="btn_margin_top">20dp</dimen></file><file path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">AndroidTraining</string><string name="hello_blank_fragment">Hello blank fragment</string></file><file path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.AndroidTraining" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.AndroidTraining" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\resValues\debug"/><source path="D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\resValues\debug"/><source path="D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\processDebugGoogleServices"><file path="D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="firebase_database_url" translatable="false">https://apptest-53581-default-rtdb.asia-southeast1.firebasedatabase.app</string><string name="gcm_defaultSenderId" translatable="false">855608031391</string><string name="google_api_key" translatable="false">AIzaSyCOU_JbOl7u7EwoYLjHFw-cUa7WrO-ghq0</string><string name="google_app_id" translatable="false">1:855608031391:android:57c3c9ff8f83cd5735caaa</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCOU_JbOl7u7EwoYLjHFw-cUa7WrO-ghq0</string><string name="google_storage_bucket" translatable="false">apptest-53581.firebasestorage.app</string><string name="project_id" translatable="false">apptest-53581</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>