#Wed Jul 09 16:32:37 ICT 2025
com.example.androidtraining.app-main-5\:/layout/fragment_two.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_two.xml
com.example.androidtraining.app-main-5\:/layout/fragment_one.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_one.xml
com.example.androidtraining.app-main-5\:/layout/file_row.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\file_row.xml
com.example.androidtraining.app-main-5\:/layout/activity_service.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_service.xml
com.example.androidtraining.app-main-5\:/layout/item_list_song.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_list_song.xml
com.example.androidtraining.app-main-5\:/layout/activity_broadcast.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_broadcast.xml
com.example.androidtraining.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-5\:/layout/item_list_user.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_list_user.xml
com.example.androidtraining.app-main-5\:/drawable-anydpi/ic_next.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-anydpi-v4\\ic_next.xml
com.example.androidtraining.app-main-5\:/layout/view_layout_include.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\view_layout_include.xml
com.example.androidtraining.app-main-5\:/layout/activity_share_prefs.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_share_prefs.xml
com.example.androidtraining.app-main-5\:/layout/activity_view_layout.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_view_layout.xml
com.example.androidtraining.app-main-5\:/drawable-anydpi/ic_arrow.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-anydpi-v4\\ic_arrow.xml
com.example.androidtraining.app-main-5\:/xml/backup_rules.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\backup_rules.xml
com.example.androidtraining.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-5\:/layout/fragment_list_view.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_list_view.xml
com.example.androidtraining.app-main-5\:/layout/fragment_top.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_top.xml
com.example.androidtraining.app-main-5\:/layout/activity_alarm.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_alarm.xml
com.example.androidtraining.app-main-5\:/layout/fragment_recycle_view.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_recycle_view.xml
com.example.androidtraining.app-main-5\:/drawable-hdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_arrow.png
com.example.androidtraining.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-5\:/layout/activity_database.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_database.xml
com.example.androidtraining.app-main-5\:/layout/activity_snooze.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_snooze.xml
com.example.androidtraining.app-main-5\:/drawable-mdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-mdpi-v4\\ic_next.png
com.example.androidtraining.app-main-5\:/layout/activity_adapter.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_adapter.xml
com.example.androidtraining.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-5\:/drawable-mdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-mdpi-v4\\ic_arrow.png
com.example.androidtraining.app-main-5\:/layout/activity_thread.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_thread.xml
com.example.androidtraining.app-main-5\:/layout/fragment_bottom.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_bottom.xml
com.example.androidtraining.app-main-5\:/layout/activity_course.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_course.xml
com.example.androidtraining.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.androidtraining.app-main-5\:/layout/activity_main.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_main.xml
com.example.androidtraining.app-main-5\:/drawable-v24/ic_launcher_foreground.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-v24\\ic_launcher_foreground.xml
com.example.androidtraining.app-main-5\:/drawable-xhdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-xhdpi-v4\\ic_arrow.png
com.example.androidtraining.app-main-5\:/drawable-xxhdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-xxhdpi-v4\\ic_arrow.png
com.example.androidtraining.app-main-5\:/layout/activity_custom.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_custom.xml
com.example.androidtraining.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.androidtraining.app-main-5\:/drawable-xhdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-xhdpi-v4\\ic_next.png
com.example.androidtraining.app-main-5\:/drawable-hdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_next.png
com.example.androidtraining.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-5\:/mipmap-hdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-5\:/mipmap-mdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.androidtraining.app-main-5\:/layout/notification_layout.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\notification_layout.xml
com.example.androidtraining.app-main-5\:/drawable-xxhdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-xxhdpi-v4\\ic_next.png
com.example.androidtraining.app-main-5\:/layout/activity_example.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_example.xml
com.example.androidtraining.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.androidtraining.app-main-5\:/drawable/ic_launcher_background.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_launcher_background.xml
com.example.androidtraining.app-main-5\:/layout/activity_notification.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_notification.xml
com.example.androidtraining.app-main-5\:/xml/data_extraction_rules.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\data_extraction_rules.xml
