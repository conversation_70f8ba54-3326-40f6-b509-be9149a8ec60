{"logs": [{"outputFile": "com.example.androidtraining.app-mergeDebugResources-48:/values-night-v8/values-night-v8.xml", "map": [{"source": "D:\\AndroidStudioProjects\\AndroidTraining\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "102", "endLines": "14", "endColumns": "12", "endOffsets": "818"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "14", "endColumns": "12", "endOffsets": "638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\e7f24da73f275a8a791d04076c131d9f\\transformed\\appcompat-1.7.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "15,16,17,18,19,20,21,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "643,713,797,881,977,1079,1181,4163", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "708,792,876,972,1074,1176,1270,4247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\3e3a03410c222d389eb5acdf62f3d148\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1275,1350,1461,1550,1651,1758,1865,1964,2071,2174,2301,2389,2513,2615,2717,2833,2935,3049,3177,3293,3415,3551,3671,3805,3925,4037,4252,4369,4493,4623,4745,4883,5017,5133", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1345,1456,1545,1646,1753,1860,1959,2066,2169,2296,2384,2508,2610,2712,2828,2930,3044,3172,3288,3410,3546,3666,3800,3920,4032,4158,4364,4488,4618,4740,4878,5012,5128,5248"}}]}]}