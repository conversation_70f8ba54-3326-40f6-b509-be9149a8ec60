package com.example.androidtraining.course.notification;

import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationChannelGroup;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.widget.Button;
import android.widget.RemoteViews;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;
import com.example.androidtraining.course.broadcastreceiver.BroadcastActivity;
import com.example.androidtraining.course.service.ForegroundService;

public class NotificationActivity extends BaseActivity {
    String TAG = NotificationActivity.class.getSimpleName();

    NotificationManager mNotificationManager;

    String CHANEL_ID = "My Chanel";
    String CHANEL_NAME = "MY_NOTIFICATION";
    int NOTIFICATION_ID = 10;

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_notification;
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate: start");

        mNotificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        // Default notification
        Button btnMakeNotification = findViewById(R.id.btn_create_notification);
        btnMakeNotification.setOnClickListener(v -> {
            Logger.d(TAG, "click to show default notification");
            createDefaultNotification();
        });
        // customize notification
        Button btnCustomNotification = findViewById(R.id.btn_create_remote_notification);
        btnCustomNotification.setOnClickListener(v -> {
            Logger.d(TAG, "click to show custom notification");
            createCustomNotification();
        });
//        new Thread(this::getAllSongs).start();
    }

    int countNotification = 0;
    private void createDefaultNotification() {
        Intent intent = new Intent(this, BroadcastActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        // 1. create builder -> set fields for notification
        NotificationCompat.Builder builder;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String groupId = "default_group_id";
            CharSequence groupName = "Default Group";
            mNotificationManager.createNotificationChannelGroup(new NotificationChannelGroup(groupId, groupName));

            NotificationChannel notificationChannel = new NotificationChannel(CHANEL_ID, CHANEL_NAME, NotificationManager.IMPORTANCE_HIGH);
            notificationChannel.setGroup(groupId);
            mNotificationManager.createNotificationChannel(notificationChannel); // 0. create NotificationChannel (from Android O, API 26)
            builder = new NotificationCompat.Builder(this, CHANEL_ID); // set chanel for notification
        } else {
            builder = new NotificationCompat.Builder(this);
        }
        String content = "This is my content of notification, This is my content of notification" +
                "This is my content of notification, This is my content of notification," +
                "This is my content of notification, This is my content of notification";
        builder.setSmallIcon(R.mipmap.ic_launcher_round)
                .setContentTitle("Title of Notification " + countNotification)
                .setTicker("Notification-xxx")
                .setContentText(content)
                .setContentIntent(pendingIntent);       // open BroadcastActivity when user tap to notification
        // 2. build notification from builder
        Notification notification = builder.build();
        // 3. show notification
        mNotificationManager.notify(NOTIFICATION_ID, notification); // mNotificationManager = getSystemService(NotificationManager.class);
        countNotification++;
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    private void createCustomNotification() {
        // 0.1. Set custom layout for notification
        RemoteViews customNotification = new RemoteViews(getPackageName(), R.layout.notification_layout);
        // 0.2. Set event for View element of custom notification
        // start an activity
//        Intent actIntent = new Intent(this, CourseActivity.class);
        Intent actIntent = new Intent();
        actIntent.setComponent(new ComponentName("com.example.testapp", "com.example.testapp.MainActivity")); // start another app's activity
        PendingIntent actPendingIntent = PendingIntent.getActivity(this, 0, actIntent,
                PendingIntent.FLAG_CANCEL_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        customNotification.setOnClickPendingIntent(R.id.noti_btn_start_activity, actPendingIntent);
        // send a broadcast
        Intent brcIntent = new Intent("com.example.androidtraining.USER_ACTION");
        brcIntent.setPackage("com.example.androidtraining");
        PendingIntent brcPendingIntent = PendingIntent.getBroadcast(this, 1, brcIntent, PendingIntent.FLAG_IMMUTABLE);
        customNotification.setOnClickPendingIntent(R.id.noti_btn_send_broadcast, brcPendingIntent);
        // start a service
        Intent srvIntent = new Intent(this, ForegroundService.class);
        PendingIntent srvPendingIntent = PendingIntent.getService(this, 3, srvIntent, PendingIntent.FLAG_IMMUTABLE);
        customNotification.setOnClickPendingIntent(R.id.noti_btn_start_service, srvPendingIntent);

        // 1. Create builder -> set fields for notification
        Notification.Builder builder;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String groupId = "custom_group_id";
            CharSequence groupName = "Custom Group";
            mNotificationManager.createNotificationChannelGroup(new NotificationChannelGroup(groupId, groupName));
            String channelId = "Custom Noti";
            NotificationChannel channel = new NotificationChannel(channelId, CHANEL_NAME, NotificationManager.IMPORTANCE_DEFAULT);
            channel.setGroup(groupId);
            mNotificationManager.createNotificationChannel(channel); // create NotificationChannel (from Android O, API 26)
            builder = new Notification.Builder(this, channelId);
        } else {
            builder = new Notification.Builder(this);
        }
        builder.setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle("Custom Notification")         // show for collapsed notification
                .setContentText("Custom Notification content " + countNotification)  // show for collapsed notification
//                .setCustomContentView(customNotification)       // non expanded = collapsed: 64dp
                .setCustomBigContentView(customNotification);   // large notification = expanded: 256dp
        // for large notification = expanded: 256dp
        customNotification.setTextViewText(R.id.noti_tv_title, "Custom Notification - New title " + countNotification);
        String content = "This is my content of notification, This is my content of notification" +
                "This is my content of notification, This is my content of notification," +
                "This is my content of notification, This is my content of notification";
        customNotification.setTextViewText(R.id.noti_tv_content,
                "Custom Notification content - New content\n" + content);
        // 2. Build notification
        Notification notification = builder.build();
        // 3. Show notification
        mNotificationManager.notify(NOTIFICATION_ID + 1, notification);
        countNotification++;
    }

    @SuppressLint("Range")
    public void getAllSongs() {
        Logger.d(TAG, "getAllSongs: start");
        Uri allsongsuri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
        String selection = MediaStore.Audio.Media.IS_MUSIC + " != 0";
        Cursor cursor = getContentResolver().query(allsongsuri, null, null, null, selection);
        Logger.d(TAG, "getAllSongs: cursor: " + cursor);
        if (cursor != null) {
            if (cursor.moveToFirst()) {
                do {
                    String song_name = cursor.getString(cursor.getColumnIndex(MediaStore.Audio.Media.DISPLAY_NAME));
                    int song_id = cursor.getInt(cursor.getColumnIndex(MediaStore.Audio.Media._ID));
                    String fullpath = cursor.getString(cursor.getColumnIndex(MediaStore.Audio.Media.DATA));
                    String Duration = cursor.getString(cursor.getColumnIndex(MediaStore.Audio.Media.DURATION));
                    Logger.d(TAG, "Song Name ::"+song_name+" Song Id :"+song_id+" fullpath ::"+fullpath+" Duration ::"+Duration);
                } while (cursor.moveToNext());
            }
            cursor.close();
        }
    }
}