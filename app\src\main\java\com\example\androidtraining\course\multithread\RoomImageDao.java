package com.example.androidtraining.course.multithread;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

@Dao
public interface RoomImageDao {
    
    // Basic CRUD operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertImage(RoomImage image);
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertImages(List<RoomImage> images);
    
    @Update
    void updateImage(RoomImage image);
    
    @Delete
    void deleteImage(RoomImage image);
    
    @Query("DELETE FROM room_images WHERE id IN (:ids)")
    void deleteImagesByIds(List<Integer> ids);
    
    @Query("DELETE FROM room_images WHERE path = :path")
    void deleteImageByPath(String path);
    
    // Query operations
    @Query("SELECT * FROM room_images ORDER BY date_taken DESC")
    List<RoomImage> getAllImages();
    
    @Query("SELECT * FROM room_images WHERE id = :id")
    RoomImage getImageById(int id);
    
    @Query("SELECT * FROM room_images WHERE path = :path")
    RoomImage getImageByPath(String path);
    
    @Query("SELECT * FROM room_images WHERE name LIKE :name")
    List<RoomImage> getImagesByName(String name);
    
    // Filtering operations
    @Query("SELECT * FROM room_images WHERE is_liked = 1 ORDER BY date_taken DESC")
    List<RoomImage> getLikedImages();
    
    @Query("SELECT * FROM room_images WHERE date_taken BETWEEN :startDate AND :endDate ORDER BY date_taken DESC")
    List<RoomImage> getImagesByDateRange(long startDate, long endDate);
    
    @Query("SELECT * FROM room_images WHERE detected_objects LIKE :objectName ORDER BY date_taken DESC")
    List<RoomImage> getImagesByDetectedObject(String objectName);
    
    // Sorting operations
    @Query("SELECT * FROM room_images ORDER BY date_taken ASC")
    List<RoomImage> getAllImagesSortedByDateAsc();
    
    @Query("SELECT * FROM room_images ORDER BY date_taken DESC")
    List<RoomImage> getAllImagesSortedByDateDesc();
    
    @Query("SELECT * FROM room_images ORDER BY name ASC")
    List<RoomImage> getAllImagesSortedByNameAsc();
    
    @Query("SELECT * FROM room_images ORDER BY name DESC")
    List<RoomImage> getAllImagesSortedByNameDesc();
    
    @Query("SELECT * FROM room_images ORDER BY size ASC")
    List<RoomImage> getAllImagesSortedBySizeAsc();
    
    @Query("SELECT * FROM room_images ORDER BY size DESC")
    List<RoomImage> getAllImagesSortedBySizeDesc();
    
    // Statistics and utility queries
    @Query("SELECT COUNT(*) FROM room_images")
    int getImageCount();
    
    @Query("SELECT COUNT(*) FROM room_images WHERE is_liked = 1")
    int getLikedImageCount();
    
    @Query("SELECT SUM(size) FROM room_images")
    long getTotalSize();
    
    @Query("SELECT * FROM room_images WHERE is_processed = 0")
    List<RoomImage> getUnprocessedImages();
    
    @Query("UPDATE room_images SET is_liked = :isLiked WHERE id = :id")
    void updateLikeStatus(int id, boolean isLiked);
    
    @Query("UPDATE room_images SET detected_objects = :detectedObjects, is_processed = 1 WHERE id = :id")
    void updateDetectedObjects(int id, String detectedObjects);
    
    @Query("UPDATE room_images SET thumbnail_path = :thumbnailPath WHERE id = :id")
    void updateThumbnailPath(int id, String thumbnailPath);
    
    // Search operations
    @Query("SELECT * FROM room_images WHERE name LIKE '%' || :query || '%' OR detected_objects LIKE '%' || :query || '%' ORDER BY date_taken DESC")
    List<RoomImage> searchImages(String query);
    
    // Check if image exists
    @Query("SELECT EXISTS(SELECT 1 FROM room_images WHERE path = :path)")
    boolean imageExists(String path);
    
    // Get images by date (same day)
    @Query("SELECT * FROM room_images WHERE date(date_taken/1000, 'unixepoch') = date(:date/1000, 'unixepoch') ORDER BY date_taken DESC")
    List<RoomImage> getImagesByDate(long date);
    
    // Get distinct dates for grouping
    @Query("SELECT DISTINCT date(date_taken/1000, 'unixepoch') as date FROM room_images ORDER BY date DESC")
    List<String> getDistinctDates();
    
    // Get images by month
    @Query("SELECT * FROM room_images WHERE strftime('%Y-%m', date_taken/1000, 'unixepoch') = :yearMonth ORDER BY date_taken DESC")
    List<RoomImage> getImagesByMonth(String yearMonth);
    
    // Clear all images
    @Query("DELETE FROM room_images")
    void clearAll();
}
