<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.7.0" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="6"
            column="36"
            startOffset="317"
            endLine="6"
            endColumn="76"
            endOffset="357"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="7"
            column="36"
            startOffset="398"
            endLine="7"
            endColumn="77"
            endOffset="439"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ExactAlarm"
        severity="error"
        message="`USE_EXACT_ALARM` can only be used when targeting API level 33 or higher">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="15"
            column="36"
            startOffset="863"
            endLine="15"
            endColumn="70"
            endOffset="897"/>
        <map>
            <condition targetLT="33"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/alarmservice/AlarmReceiver.java"
            line="78"
            column="9"
            startOffset="3707"
            endLine="78"
            endColumn="55"
            endOffset="3753"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/CustomActivity.java"
            line="73"
            column="9"
            startOffset="2960"
            endLine="73"
            endColumn="58"
            endOffset="3009"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/CustomActivity.java"
            line="100"
            column="9"
            startOffset="4547"
            endLine="100"
            endColumn="58"
            endOffset="4596"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/NotificationActivity.java"
            line="91"
            column="9"
            startOffset="4205"
            endLine="91"
            endColumn="67"
            endOffset="4263"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/NotificationActivity.java"
            line="146"
            column="9"
            startOffset="7997"
            endLine="146"
            endColumn="71"
            endOffset="8059"/>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="fffffffff8000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/file_row.xml"
            line="31"
            column="13"
            startOffset="1385"
            endLine="31"
            endColumn="41"
            endOffset="1413"/>
        <map>
            <entry
                name="message"
                string="Attribute `textFontWeight` is only used in API level 28 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="fffffffff8000000"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="fffffffffe000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_user.xml"
            line="45"
            column="9"
            startOffset="1685"
            endLine="45"
            endColumn="35"
            endOffset="1711"/>
        <map>
            <entry
                name="message"
                string="Attribute `tooltipText` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="fffffffffe000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_list_user.xml"
            line="57"
            column="9"
            startOffset="2139"
            endLine="57"
            endColumn="36"
            endOffset="2166"/>
        <map>
            <entry
                name="message"
                string="Attribute `tooltipText` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_arrow.xml"
            line="8"
            column="5"
            startOffset="243"
            endLine="8"
            endColumn="25"
            endOffset="263"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="This attribute is not supported in images generated from this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_arrow.xml"
            line="10"
            column="26"
            startOffset="307"
            endLine="10"
            endColumn="46"
            endOffset="327"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_next.xml"
            line="8"
            column="5"
            startOffset="243"
            endLine="8"
            endColumn="25"
            endOffset="263"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="This attribute is not supported in images generated from this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_next.xml"
            line="14"
            column="28"
            startOffset="437"
            endLine="14"
            endColumn="48"
            endOffset="457"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1870"
                endOffset="1890"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="35"
            column="9"
            startOffset="1870"
            endLine="35"
            endColumn="29"
            endOffset="1890"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 33">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/broadcastreceiver/BroadcastActivity.java"
                startOffset="1413"
                endOffset="1461"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/broadcastreceiver/BroadcastActivity.java"
            line="34"
            column="5"
            startOffset="1413"
            endLine="34"
            endColumn="53"
            endOffset="1461"/>
        <map>
            <condition minGE="ffffffff00000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/CustomActivity.java"
                startOffset="1680"
                endOffset="1721"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/CustomActivity.java"
            line="55"
            column="5"
            startOffset="1680"
            endLine="55"
            endColumn="46"
            endOffset="1721"/>
        <map>
            <condition minGE="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/CustomActivity.java"
                startOffset="3021"
                endOffset="3062"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/CustomActivity.java"
            line="75"
            column="5"
            startOffset="3021"
            endLine="75"
            endColumn="46"
            endOffset="3062"/>
        <map>
            <condition minGE="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/service/ForegroundService.java"
                startOffset="1277"
                endOffset="1318"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/service/ForegroundService.java"
            line="41"
            column="5"
            startOffset="1277"
            endLine="41"
            endColumn="46"
            endOffset="1318"/>
        <map>
            <condition minGE="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/NotificationActivity.java"
                startOffset="1301"
                endOffset="1342"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/NotificationActivity.java"
            line="39"
            column="5"
            startOffset="1301"
            endLine="39"
            endColumn="46"
            endOffset="1342"/>
        <map>
            <condition minGE="ffffffffff800000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/NotificationActivity.java"
                startOffset="4376"
                endOffset="4417"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/notification/NotificationActivity.java"
            line="95"
            column="5"
            startOffset="4376"
            endLine="95"
            endColumn="46"
            endOffset="4417"/>
        <map>
            <condition minGE="ffffffffff800000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 28">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/service/ServiceActivity.java"
                startOffset="8508"
                endOffset="8549"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/service/ServiceActivity.java"
            line="184"
            column="5"
            startOffset="8508"
            endLine="184"
            endColumn="46"
            endOffset="8549"/>
        <map>
            <condition minGE="fffffffff8000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/utils/Temp.java"
                startOffset="342"
                endOffset="364"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/utils/Temp.java"
            line="14"
            column="5"
            startOffset="342"
            endLine="14"
            endColumn="27"
            endOffset="364"/>
        <map>
            <condition minGE="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_share_prefs.xml"
            line="19"
            column="6"
            startOffset="717"
            endLine="19"
            endColumn="14"
            endOffset="725"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

</incidents>
