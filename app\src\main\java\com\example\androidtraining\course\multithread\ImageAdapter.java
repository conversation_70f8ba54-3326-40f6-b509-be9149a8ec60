package com.example.androidtraining.course.multithread;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ImageAdapter extends RecyclerView.Adapter<ImageAdapter.ImageViewHolder> {
    private static final String TAG = ImageAdapter.class.getSimpleName();
    
    private final Context context;
    private List<RoomImage> images;
    private final Set<Integer> selectedItems;
    private boolean isSelectionMode;
    private OnImageClickListener clickListener;
    private OnSelectionChangedListener selectionListener;
    private final ExecutorService executorService;
    
    public interface OnImageClickListener {
        void onImageClick(RoomImage image, int position);
        void onImageLongClick(RoomImage image, int position);
    }
    
    public interface OnSelectionChangedListener {
        void onSelectionChanged(int selectedCount);
    }
    
    public ImageAdapter(Context context) {
        this.context = context;
        this.images = new ArrayList<>();
        this.selectedItems = new HashSet<>();
        this.isSelectionMode = false;
        this.executorService = Executors.newFixedThreadPool(3);
    }
    
    public void setOnImageClickListener(OnImageClickListener listener) {
        this.clickListener = listener;
    }
    
    public void setOnSelectionChangedListener(OnSelectionChangedListener listener) {
        this.selectionListener = listener;
    }
    
    public void updateImages(List<RoomImage> newImages) {
        this.images = new ArrayList<>(newImages);
        notifyDataSetChanged();
    }
    
    public void setSelectionMode(boolean selectionMode) {
        this.isSelectionMode = selectionMode;
        if (!selectionMode) {
            selectedItems.clear();
        }
        notifyDataSetChanged();
        if (selectionListener != null) {
            selectionListener.onSelectionChanged(selectedItems.size());
        }
    }
    
    public boolean isSelectionMode() {
        return isSelectionMode;
    }
    
    public Set<Integer> getSelectedItems() {
        return new HashSet<>(selectedItems);
    }
    
    public List<RoomImage> getSelectedImages() {
        List<RoomImage> selectedImages = new ArrayList<>();
        for (int position : selectedItems) {
            if (position < images.size()) {
                selectedImages.add(images.get(position));
            }
        }
        return selectedImages;
    }
    
    public void selectAll() {
        selectedItems.clear();
        for (int i = 0; i < images.size(); i++) {
            selectedItems.add(i);
        }
        notifyDataSetChanged();
        if (selectionListener != null) {
            selectionListener.onSelectionChanged(selectedItems.size());
        }
    }
    
    public void clearSelection() {
        selectedItems.clear();
        notifyDataSetChanged();
        if (selectionListener != null) {
            selectionListener.onSelectionChanged(0);
        }
    }
    
    @NonNull
    @Override
    public ImageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_gallery_image, parent, false);
        return new ImageViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ImageViewHolder holder, int position) {
        RoomImage image = images.get(position);
        holder.bind(image, position);
    }
    
    @Override
    public int getItemCount() {
        return images.size();
    }
    
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
    
    class ImageViewHolder extends RecyclerView.ViewHolder {
        private final ImageView imageThumbnail;
        private final CheckBox checkboxSelect;
        private final ImageView iconLike;
        private final View selectionOverlay;
        private final View infoOverlay;
        private final TextView textImageName;
        private final TextView textImageSize;
        private final ProgressBar progressLoading;
        
        public ImageViewHolder(@NonNull View itemView) {
            super(itemView);
            
            imageThumbnail = itemView.findViewById(R.id.image_thumbnail);
            checkboxSelect = itemView.findViewById(R.id.checkbox_select);
            iconLike = itemView.findViewById(R.id.icon_like);
            selectionOverlay = itemView.findViewById(R.id.selection_overlay);
            infoOverlay = itemView.findViewById(R.id.info_overlay);
            textImageName = itemView.findViewById(R.id.text_image_name);
            textImageSize = itemView.findViewById(R.id.text_image_size);
            progressLoading = itemView.findViewById(R.id.progress_loading);
            
            // Set click listeners
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION) {
                    if (isSelectionMode) {
                        toggleSelection(position);
                    } else if (clickListener != null) {
                        clickListener.onImageClick(images.get(position), position);
                    }
                }
            });
            
            itemView.setOnLongClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION) {
                    if (!isSelectionMode) {
                        setSelectionMode(true);
                        toggleSelection(position);
                    }
                    if (clickListener != null) {
                        clickListener.onImageLongClick(images.get(position), position);
                    }
                }
                return true;
            });
            
            checkboxSelect.setOnCheckedChangeListener((buttonView, isChecked) -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION) {
                    if (isChecked) {
                        selectedItems.add(position);
                    } else {
                        selectedItems.remove(position);
                    }
                    updateSelectionUI(position);
                    if (selectionListener != null) {
                        selectionListener.onSelectionChanged(selectedItems.size());
                    }
                }
            });
        }
        
        public void bind(RoomImage image, int position) {
            // Update selection UI
            boolean isSelected = selectedItems.contains(position);
            checkboxSelect.setVisibility(isSelectionMode ? View.VISIBLE : View.GONE);
            checkboxSelect.setChecked(isSelected);
            selectionOverlay.setVisibility(isSelected ? View.VISIBLE : View.GONE);
            
            // Show like indicator
            iconLike.setVisibility(image.isLiked() ? View.VISIBLE : View.GONE);
            
            // Update info overlay
            textImageName.setText(image.getName());
            textImageSize.setText(image.getFormattedSize() + " • " + image.getDimensions());
            
            // Load thumbnail
            loadThumbnail(image);
        }
        
        private void toggleSelection(int position) {
            if (selectedItems.contains(position)) {
                selectedItems.remove(position);
            } else {
                selectedItems.add(position);
            }
            updateSelectionUI(position);
            if (selectionListener != null) {
                selectionListener.onSelectionChanged(selectedItems.size());
            }
        }
        
        private void updateSelectionUI(int position) {
            boolean isSelected = selectedItems.contains(position);
            checkboxSelect.setChecked(isSelected);
            selectionOverlay.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        }
        
        private void loadThumbnail(RoomImage image) {
            // Show loading indicator
            progressLoading.setVisibility(View.VISIBLE);
            imageThumbnail.setImageResource(R.drawable.ic_image_placeholder);
            
            executorService.execute(() -> {
                Bitmap thumbnail = null;
                
                // Try to load thumbnail first
                if (image.getThumbnailPath() != null) {
                    File thumbnailFile = new File(image.getThumbnailPath());
                    if (thumbnailFile.exists()) {
                        thumbnail = BitmapFactory.decodeFile(image.getThumbnailPath());
                    }
                }
                
                // If no thumbnail, try to load original image with scaling
                if (thumbnail == null) {
                    try {
                        BitmapFactory.Options options = new BitmapFactory.Options();
                        options.inSampleSize = 4; // Scale down by factor of 4
                        options.inJustDecodeBounds = false;
                        thumbnail = BitmapFactory.decodeFile(image.getPath(), options);
                    } catch (Exception e) {
                        Logger.e(TAG, "Error loading image: " + e.getMessage());
                    }
                }
                
                final Bitmap finalThumbnail = thumbnail;
                
                // Update UI on main thread
                if (itemView.getContext() instanceof android.app.Activity) {
                    ((android.app.Activity) itemView.getContext()).runOnUiThread(() -> {
                        progressLoading.setVisibility(View.GONE);
                        if (finalThumbnail != null) {
                            imageThumbnail.setImageBitmap(finalThumbnail);
                        } else {
                            imageThumbnail.setImageResource(R.drawable.ic_image_placeholder);
                        }
                    });
                }
            });
        }
    }
}
