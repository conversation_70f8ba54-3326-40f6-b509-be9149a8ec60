<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.threadhandler.ThreadActivity">

    <Button
        android:id="@+id/btn_test_thread"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="10dp"
        android:text="Test Thread"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/tv_thread_title"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="20dp"
        android:gravity="center_vertical"
        android:textSize="20dp"
        android:text="Thread count: "
        app:layout_constraintStart_toEndOf="@+id/btn_test_thread"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/tv_thread_count"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="30dp"
        android:gravity="center_vertical"
        android:textSize="20dp"
        android:text="0"
        app:layout_constraintStart_toEndOf="@+id/tv_thread_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <Button
        android:id="@+id/btn_test_async"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="10dp"
        android:text="Test Async"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_test_thread"
        />

    <TextView
        android:id="@+id/tv_async_title"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="20dp"
        android:gravity="center_vertical"
        android:textSize="20dp"
        android:text="AsyncTask count: "
        app:layout_constraintStart_toEndOf="@+id/btn_test_async"
        app:layout_constraintTop_toBottomOf="@+id/btn_test_thread"
        />


    <TextView
        android:id="@+id/tv_count_async"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="30dp"
        android:gravity="center_vertical"
        android:textSize="20dp"
        android:text="0"
        app:layout_constraintStart_toEndOf="@+id/tv_async_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_test_thread"
        />

    <SeekBar
        android:id="@+id/seek_bar_async"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_async_title"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        />

    <Button
        android:id="@+id/btn_test_executor"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="50dp"
        android:layout_marginStart="10dp"
        android:text="Test Executor"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_test_async"
        />

    <Button
        android:id="@+id/btn_test_future_concurrent"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="50dp"
        android:layout_marginStart="10dp"
        android:text="Test Future Concurrent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_test_executor"
        />

</androidx.constraintlayout.widget.ConstraintLayout>