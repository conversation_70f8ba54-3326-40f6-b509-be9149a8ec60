<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.adapter.AdapterActivity">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_container_adapter"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_show_list_view" />

    <Button
        android:id="@+id/btn_show_list_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Show List view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="10dp" />

    <Button
        android:id="@+id/btn_show_recycle_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Show Recycle view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="10dp" />

</androidx.constraintlayout.widget.ConstraintLayout>