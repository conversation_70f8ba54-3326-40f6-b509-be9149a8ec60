package com.example.androidtraining.course.database.room;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.androidtraining.R;
import com.example.androidtraining.course.database.room.entities.User;

import java.util.ArrayList;
import java.util.List;

public class UserAdapter extends RecyclerView.Adapter<UserAdapter.UserViewHolder> {
    List<User> listUser = new ArrayList<>();
    OnItemClickedListener clickedListener;

    @SuppressLint("NotifyDataSetChanged")
    public void setListUser(List<User> list) {
        listUser = list;
//        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public UserViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_list_user, parent, false);
        return new UserViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull UserViewHolder holder, int position) {
        User user = listUser.get(position);
        holder.setData(user);
        final int pos = position;
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (clickedListener != null) clickedListener.onItemClick(pos);
            }
        });
    }

    public void setClickedListener(OnItemClickedListener listener) {
        clickedListener = listener;
    }

    @Override
    public int getItemCount() {
        return listUser.size();
    }

    public static class UserViewHolder extends RecyclerView.ViewHolder {
        TextView tvId, tvName, tvPhone;
        public UserViewHolder(@NonNull View itemView) {
            super(itemView);
            tvId = itemView.findViewById(R.id.tv_user_id);
            tvName = itemView.findViewById(R.id.tv_user_name);
            tvPhone = itemView.findViewById(R.id.tv_user_phone);
        }

        public void setData(User user) {
            tvId.setText(user.getId() + "");
            tvName.setText(user.getName());
            tvPhone.setText(user.getSingleId());
        }
    }

    public interface OnItemClickedListener {
        void onItemClick(int pos);
    }
}
