package com.example.androidtraining.course.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.androidtraining.R;
import com.google.android.material.snackbar.Snackbar;

import java.util.List;

public class SongAdapterListView extends ArrayAdapter<MySong> {
    List<MySong> mListSong;
    Context mContext;
    int mResourceId;
//    ImageView mSongIcon;
//    TextView mSongName;
//    TextView mSongAuthor;
//    TextView mSongTime;

    public SongAdapterListView(@NonNull Context context, int resource, @NonNull List<MySong> listSong) {
        super(context, resource, listSong);
        mContext = context;
        mResourceId = resource;
        mListSong = listSong;
    }

    @Override
    public int getCount() {
        return mListSong.size();
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        View view = convertView;
        ViewHolderSong viewHolderSong;
        if (view == null) {
            // normal
            view = LayoutInflater.from(mContext).inflate(mResourceId, parent, false);
            // optimize by ViewHolder
            viewHolderSong = new ViewHolderSong(view);
            view.setTag(viewHolderSong);
        } else { // optimize by ViewHolder
            viewHolderSong = (ViewHolderSong) view.getTag();
        }
        MySong song = mListSong.get(position);
        // set data for item
//        setNormalViewItem(view, song);
        // optimize by ViewHolder
        viewHolderSong.setViewItemData(song);
        view.setOnClickListener(v -> Snackbar.make(v, "List view onClick, pos: " + position, Snackbar.LENGTH_LONG).show());
        // return item
        return view;
    }

//    private void setNormalViewItem(View itemView, MySong song) {
//        if (song != null) {
//            mSongIcon = itemView.findViewById(R.id.img_song_icon);
//            mSongName = itemView.findViewById(R.id.tv_song_name);
//            mSongAuthor = itemView.findViewById(R.id.tv_song_author);
//            mSongTime = itemView.findViewById(R.id.tv_song_time);
//            mSongIcon.setImageResource(song.getResId());
//            mSongName.setText(song.getName());
//            mSongAuthor.setText(song.getAuthor());
//            mSongTime.setText(song.getTime());
//        }
//    }
}

class ViewHolderSong {
    ImageView songIcon;
    TextView songName;
    TextView songAuthor;
    TextView songTime;

    public ViewHolderSong(@NonNull View itemView) {
        songIcon = itemView.findViewById(R.id.img_song_icon);
        songName = itemView.findViewById(R.id.tv_song_name);
        songAuthor = itemView.findViewById(R.id.tv_song_author);
        songTime = itemView.findViewById(R.id.tv_song_time);
    }

    public void setViewItemData(@NonNull MySong song) {
        songIcon.setImageResource(song.getResId());
        songName.setText(song.getName());
        songAuthor.setText(song.getAuthor());
        songTime.setText(song.getTime());
    }
}