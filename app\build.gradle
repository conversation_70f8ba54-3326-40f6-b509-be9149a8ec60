plugins {
    id 'com.android.application'        // for Java
    id 'kotlin-android'                 // for Kotlin
    id 'com.google.gms.google-services' // for FireBase
}

android {
    namespace 'com.example.androidtraining'
    compileSdk 35

    defaultConfig {
        applicationId "com.example.androidtraining"
        minSdk 23
        //noinspection EditedTargetSdkVersion,OldTargetApi
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        aidl true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

dependencies {
    implementation 'androidx.activity:activity-ktx:1.10.1'
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    // Room dependencies
    implementation 'androidx.room:room-runtime:2.7.2'
    annotationProcessor 'androidx.room:room-compiler:2.7.2'

    // Kotlin
    implementation 'androidx.core:core-ktx:1.16.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:2.1.20"

    // Firebase
    implementation platform('com.google.firebase:firebase-bom:33.16.0') // BOM for auto detect version of FB Auth & Realtime Database
    implementation 'com.google.firebase:firebase-auth'              // Auth for Java
    implementation 'com.google.firebase:firebase-database'          // Realtime Database for Java
    implementation 'com.google.firebase:firebase-auth-ktx'          // Auth for Kotlin
    implementation 'com.google.firebase:firebase-database-ktx'      // Realtime Database for Kotlin
}
