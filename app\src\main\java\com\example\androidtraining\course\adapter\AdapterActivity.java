package com.example.androidtraining.course.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import android.os.Bundle;
import android.widget.Button;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;

import java.util.ArrayList;
import java.util.List;

public class AdapterActivity extends BaseActivity {
    public static final String TAG = AdapterActivity.class.getSimpleName();

    FragmentManager mFragmentManager;
    ListViewFragment mListViewFragment;
    RecycleViewFragment mRecycleViewFragment;

    Button mBtnShowListView;
    Button mBtnShowRecycleView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_adapter;
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate");

        mFragmentManager = getSupportFragmentManager();
        mListViewFragment = new ListViewFragment();
        mRecycleViewFragment = new RecycleViewFragment();

        List<MySong> listSong = new ArrayList<>();
        for (int i = 1; i < 50; i++) {
            listSong.add(new MySong(R.mipmap.ic_launcher_round, "Song name " + i, "Author " + i, "04:00"));
        }
        mListViewFragment.setListSong(listSong);
        mRecycleViewFragment.setListSong(listSong);

        mBtnShowListView = findViewById(R.id.btn_show_list_view);
        mBtnShowRecycleView = findViewById(R.id.btn_show_recycle_view);
        mBtnShowListView.setOnClickListener(v -> {
            Fragment fragmentByTag = mFragmentManager.findFragmentByTag("list_view_fragment");
            Fragment fragmentById = mFragmentManager.findFragmentById(R.id.fragment_container_adapter);
            Logger.d(TAG, "BtnShowListView onClicked, fragmentByTag=" + fragmentByTag + ", count=" + mFragmentManager.getBackStackEntryCount());
            Logger.d(TAG, "BtnShowListView onClicked, fragmentByTId=" + fragmentById);
            FragmentTransaction ft = mFragmentManager.beginTransaction();
            if (fragmentByTag != null) {
                if (!fragmentByTag.equals(fragmentById)) {
                    ft.replace(R.id.fragment_container_adapter, fragmentByTag).commit();
                    Logger.d(TAG, "BtnShowListView onClicked - replaced fragmentByTag");
                } else {
                    Logger.d(TAG, "BtnShowListView onClicked - same fragment");
                }
            } else {
                // currently, this block can not set tag for fragment, but still addToBackStack
//                FragmentTransaction ft = mFragmentManager.beginTransaction();
//                ft.replace(R.id.fragment_container_adapter, mListViewFragment);
//                ft.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN);
//                ft.addToBackStack("list_view_fragment");
//                ft.commit();
                // end

                ft.replace(R.id.fragment_container_adapter, mListViewFragment, "list_view_fragment")
                        .setReorderingAllowed(true)
                        .addToBackStack("list_view_fragment")
                        .commit();
                Logger.d(TAG, "BtnShowListView onClicked - replaced fragmentByTag & addToBackStack");
            }
        });

        mBtnShowRecycleView.setOnClickListener(v -> {
            Fragment fragmentByTag = mFragmentManager.findFragmentByTag("recycle_view_fragment");
            Logger.d(TAG, "BtnShowRecycleView onClicked, fragmentByTag=" + fragmentByTag + ", count=" + mFragmentManager.getBackStackEntryCount());
            Fragment fragmentById = mFragmentManager.findFragmentById(R.id.fragment_container_adapter);
            Logger.d(TAG, "BtnShowRecycleView onClicked, fragmentByTId=" + fragmentById);
            FragmentTransaction ft = mFragmentManager.beginTransaction();
            if (fragmentByTag != null) {
                if (!fragmentByTag.equals(fragmentById)) {
                    ft.replace(R.id.fragment_container_adapter, fragmentByTag).commit();
                    Logger.d(TAG, "BtnShowRecycleView onClicked - replaced fragmentByTag");
                } else {
                    Logger.d(TAG, "BtnShowRecycleView onClicked - same fragment");
                }
            } else {
                ft.replace(R.id.fragment_container_adapter, mRecycleViewFragment, "recycle_view_fragment")
                        .setReorderingAllowed(true)
                        .addToBackStack("recycle_view_fragment")
                        .commit();
                Logger.d(TAG, "BtnShowRecycleView onClicked - replaced fragmentByTag & addToBackStack");
            }
        });
    }
    // 1. Prepare Data: define Model
    // 2. Define layout of item
    // 3. Define Adapter extends BaseAdapter<Model>: contains List<Model>
    // 4. Set Adapter to ListView


    @Override
    protected void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy");
    }
}