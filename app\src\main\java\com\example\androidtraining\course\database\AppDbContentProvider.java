package com.example.androidtraining.course.database;

import android.content.ContentProvider;
import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentValues;
import android.content.OperationApplicationException;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.androidtraining.course.database.room.AppDatabaseRoom;
import com.example.androidtraining.course.database.room.dao.JobDao;
import com.example.androidtraining.course.database.room.dao.UserDao;
import com.example.androidtraining.course.database.room.entities.Job;
import com.example.androidtraining.course.database.room.entities.User;
import com.example.androidtraining.course.database.sqlite.SQLiteDbHelper;
import com.example.androidtraining.utils.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;

public class AppDbContentProvider extends ContentProvider {
    private static final String TAG = AppDbContentProvider.class.getSimpleName();
    static final String AUTHORITIES = "com.example.androidtraining.provider.AppDbContentProvider";
    static final int CODE_ENGINEER_ALL = 1;
    static final int CODE_ENGINEER_ITEM = 2;
    static final int CODE_TASK_ALL = 11;
    static final int CODE_TASK_ITEM = 12;
    private static final UriMatcher sUriMatcher = new UriMatcher(UriMatcher.NO_MATCH);
    static {
        // ENGINEER_TABLE
        // content://com.example.androidtraining.provider.AppDbContentProvider/engineers
        sUriMatcher.addURI(AUTHORITIES, DbConstants.ENGINEER_TABLE, CODE_ENGINEER_ALL);
        // content://com.example.androidtraining.provider.AppDbContentProvider/engineers/2
        sUriMatcher.addURI(AUTHORITIES, DbConstants.ENGINEER_TABLE + "/#", CODE_ENGINEER_ITEM);

        // TASK_TABLE
        sUriMatcher.addURI(AUTHORITIES, DbConstants.TASK_TABLE, CODE_TASK_ALL);
        sUriMatcher.addURI(AUTHORITIES, DbConstants.TASK_TABLE + "/#", CODE_TASK_ITEM);
    }

    // SQLite
    SQLiteDatabase sqLiteDatabase = null;

    // Room Db
    AppDatabaseRoom appDatabaseRoom = null;
    UserDao userDao;
    JobDao jobDao;

    @Override
    public boolean onCreate() {
        Logger.d(TAG, "onCreate ");
        SQLiteDbHelper dbHelper = new SQLiteDbHelper(getContext());
        sqLiteDatabase = dbHelper.getWritableDatabase();

        appDatabaseRoom = AppDatabaseRoom.getDatabase(getContext());
        userDao = appDatabaseRoom.userDao();
        jobDao = appDatabaseRoom.jobDao();
        Logger.d(TAG, "onCreate success");
        return true;
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String selection,
                        @Nullable String[] selectionArgs, @Nullable String sortOrder) {
        int code = sUriMatcher.match(uri);
        Logger.d(TAG, "query: uri=" + uri + ", projection=" + Arrays.toString(projection) + ", selection=" + selection +
                ", selectionArgs=" + Arrays.toString(selectionArgs) + ", sortOrder=" + sortOrder + ", code=" + code);
        Cursor cursor = null, cursorRoom = null;
        switch (code) {
            case CODE_ENGINEER_ITEM:
                Pair<String, String[]> pair = handleUriWithId(uri, selection, selectionArgs);
                selection = pair.first;
                selectionArgs = pair.second;
                // SQLite
                cursor = sqLiteDatabase.query(DbConstants.ENGINEER_TABLE, projection, selection,
                        selectionArgs, null, null, sortOrder);

                // RoomDb
                // convert query params to sql query string
                String sql = convertSqlParamsToString(DbConstants.ENGINEER_TABLE, projection, selection, selectionArgs, sortOrder);
                cursorRoom = AppDatabaseRoom.getDatabase(getContext()).getOpenHelper().getReadableDatabase().query(sql);
                break;
            case CODE_ENGINEER_ALL:
                // SQLite
                cursor = sqLiteDatabase.query(DbConstants.ENGINEER_TABLE, projection, selection,
                        selectionArgs, null, null, sortOrder);

                // Room db
                sql = convertSqlParamsToString(DbConstants.ENGINEER_TABLE, projection, selection, selectionArgs, sortOrder);
                cursorRoom = AppDatabaseRoom.getDatabase(getContext()).getOpenHelper().getReadableDatabase().query(sql);
                break;
            case CODE_TASK_ITEM:
                pair = handleUriWithId(uri, selection, selectionArgs);
                selection = pair.first;
                selectionArgs = pair.second;
                cursor = sqLiteDatabase.query(DbConstants.TASK_TABLE, projection, selection,
                        selectionArgs, null, null, sortOrder);
                break;
            case CODE_TASK_ALL:
                cursor = sqLiteDatabase.query(DbConstants.TASK_TABLE, projection, selection,
                        selectionArgs, null, null, sortOrder);
                break;
            default:
                break;
        }
        Logger.d(TAG, "query: uri=" + uri + ", cursor=" + cursor + ", count=" + (cursor != null ? cursor.getCount() : 0));
        Logger.d(TAG, "query: uri=" + uri + ", cursorRoom=" + cursorRoom + ", count=" + (cursorRoom != null ? cursorRoom.getCount() : 0));
        return cursor;
//        return cursorRoom;
    }

    private Pair<String, String[]> handleUriWithId(@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
        String id = uri.getPathSegments().get(1);
        selection = selection == null ? DbConstants.ENGINEER_ID_COL + "=?" : selection + " AND " + DbConstants.ENGINEER_ID_COL + "=?";
        if (selectionArgs == null) {
            selectionArgs = new String[]{id};
        } else {
            List<String> list = Arrays.asList(selectionArgs);
            list.add(id);
            selectionArgs = list.toArray(new String[0]);
        }
        Logger.d(TAG, "handleUriWithId: id=" + id + ", selection=" + selection + ", selectionArgs=" + Arrays.toString(selectionArgs));
        return new Pair<>(selection, selectionArgs);
    }

    private String convertSqlParamsToString(String tableName, @Nullable String[] projection, @Nullable String selection,
          @Nullable String[] selectionArgs, @Nullable String sortOrder) {
        // must handle SQL Injection error, ex: selection = "id=? AND name=?", selectionArgs={"1 OR true;", "ngoc nguyen"}
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT ");
        // projection
        if (projection == null) builder.append("* ");
        else {
            int i = 0, len = projection.length;
            for (String str : projection) {
                i++;
                if (str.contains(";") || str.contains("true")) continue; // ignore SQL Injection
                if (i < len) builder.append(str).append(",");
                else builder.append(str).append(" ");
            }
        }
        // tableName
        builder.append("FROM ").append(tableName);
        // selection & selectionArgs
        if (!TextUtils.isEmpty(selection)) {
            for (String str : selectionArgs) {
                if (str.contains(";") || str.contains("true")) continue; // ignore SQL Injection
                selection = selection.replaceFirst("\\?", str);
            }
            builder.append(" WHERE ").append(selection);
        }
        // sortOrder
        if (!TextUtils.isEmpty(sortOrder)) builder.append(" ORDER BY ").append(sortOrder);
        builder.append(";");
        Logger.d(TAG, "convertSqlParamsToString: sql=" + builder);
        return builder.toString();
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        Logger.d(TAG, "getType: uri=" + uri);
        switch (sUriMatcher.match(uri)) {
            case CODE_ENGINEER_ALL:
                return "vnd.android.cursor.dir/" + AUTHORITIES + "." + DbConstants.ENGINEER_TABLE;
            case CODE_ENGINEER_ITEM:
                return "vnd.android.cursor.item/" + AUTHORITIES + "." + DbConstants.ENGINEER_TABLE;
            case CODE_TASK_ALL:
                return "vnd.android.cursor.dir/" + AUTHORITIES + "." + DbConstants.TASK_TABLE;
            case CODE_TASK_ITEM:
                return "vnd.android.cursor.item/" + AUTHORITIES + "." + DbConstants.TASK_TABLE;
            default:
//                throw new IllegalArgumentException("Unknown URI: " + uri);
                return "";
        }
    }

    // only one way for Insert: only match CODE_ALL
    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues value) {
        if (value == null) return null;
        long rowId = -1, rowIdRoom = -1;
        switch (sUriMatcher.match(uri)) {
            case CODE_ENGINEER_ALL:
                rowId = sqLiteDatabase.insert(DbConstants.ENGINEER_TABLE, null, value);
                rowIdRoom = userDao.insertUser(User.fromContentValues(value));
                break;
            case CODE_TASK_ALL:
                rowId = sqLiteDatabase.insert(DbConstants.TASK_TABLE, null, value);
                rowIdRoom = jobDao.insert(Job.fromContentValues(value));
                break;
            default:
                break;
        }
        Logger.d(TAG, "insert: uri=" + uri + ", rowId=" + rowId + ", value=" + value + ", rowIdRoom=" + rowIdRoom);
        if (rowId > 0) return Uri.withAppendedPath(uri, "" + rowId);
        return null;
    }

    // For RoomDb update & delete, 2 way:
    // way 1: allow to update/delete only one item, it means that uri must match only CODE_ITEM -> using convert value to DAO object: by method fromContentValues
    //      in case of CODE_ALL: throw exception
    // way 2: allow to update/delete many items by selection & selectionArgs -> must convert selection & selectionArgs to update/delete sql string and do same thing in query

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues value, @Nullable String selection, @Nullable String[] selectionArgs) {
        if (value == null) return 0;
        int count = 0, countRoom = 0;
        switch (sUriMatcher.match(uri)) {
            case CODE_ENGINEER_ALL:
                count = sqLiteDatabase.update(DbConstants.ENGINEER_TABLE, value, selection, selectionArgs);
                countRoom = AppDatabaseRoom.getDatabase(getContext()).getOpenHelper().getWritableDatabase()
                        .update(DbConstants.ENGINEER_TABLE, SQLiteDatabase. CONFLICT_REPLACE, value, selection, selectionArgs);
                break;
            case CODE_ENGINEER_ITEM:
                Pair<String, String[]> pair = handleUriWithId(uri, selection, selectionArgs);
                selection = pair.first;
                selectionArgs = pair.second;
                count = sqLiteDatabase.update(DbConstants.ENGINEER_TABLE, value, selection, selectionArgs);
                countRoom = userDao.updateUser(User.fromContentValues(value));
                break;
            case CODE_TASK_ITEM:
                pair = handleUriWithId(uri, selection, selectionArgs);
                selection = pair.first;
                selectionArgs = pair.second;
                count = sqLiteDatabase.update(DbConstants.TASK_TABLE, value, selection, selectionArgs);
                break;
            case CODE_TASK_ALL:
                count = sqLiteDatabase.update(DbConstants.TASK_TABLE, value, selection, selectionArgs);
                break;
            default:
                break;
        }
        Logger.d(TAG, "update: uri=" + uri + ", count=" + count + ", countRoom=" + countRoom + ", selection=" + selection + ", selectionArgs=" + Arrays.toString(selectionArgs) + ", value=" + value);
        return count;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
        int count = 0, countRoom = 0;
        switch (sUriMatcher.match(uri)) {
            case CODE_ENGINEER_ITEM:
                Pair<String, String[]> pair = handleUriWithId(uri, selection, selectionArgs);
                selection = pair.first;
                selectionArgs = pair.second;
                count = sqLiteDatabase.delete(DbConstants.ENGINEER_TABLE, selection, selectionArgs);
                countRoom = userDao.deleteUserById(uri.getPathSegments().get(1));
                break;
            case CODE_ENGINEER_ALL:
                count = sqLiteDatabase.delete(DbConstants.ENGINEER_TABLE, selection, selectionArgs);
                countRoom = AppDatabaseRoom.getDatabase(getContext()).getOpenHelper().getWritableDatabase()
                        .delete(DbConstants.ENGINEER_TABLE, selection, selectionArgs);
                break;
            case CODE_TASK_ITEM:
                pair = handleUriWithId(uri, selection, selectionArgs);
                selection = pair.first;
                selectionArgs = pair.second;
                count = sqLiteDatabase.delete(DbConstants.TASK_TABLE, selection, selectionArgs);
                break;
            case CODE_TASK_ALL:
                count = sqLiteDatabase.delete(DbConstants.TASK_TABLE, selection, selectionArgs);
                break;
            default:
                break;
        }
        Logger.d(TAG, "delete: uri=" + uri + ", count=" + count + ", countRoom=" + countRoom + ", selection=" + selection + ", selectionArgs=" + Arrays.toString(selectionArgs));
        return count;
    }

    @NonNull
    @Override
    public ContentProviderResult[] applyBatch(@NonNull ArrayList<ContentProviderOperation> operations) throws OperationApplicationException {
        return appDatabaseRoom.runInTransaction(new Callable<ContentProviderResult[]>() {
            @Override
            public ContentProviderResult[] call() throws Exception {
                return AppDbContentProvider.super.applyBatch(operations);
            }
        });
//        return appDatabaseRoom.runInTransaction(() -> AppDbContentProvider.super.applyBatch(operations));
    }

    @Override
    public int bulkInsert(@NonNull Uri uri, @NonNull ContentValues[] values) {
        switch (sUriMatcher.match(uri)) {
            case CODE_ENGINEER_ALL:
                List<User> userList = new ArrayList<>();
                for (ContentValues value : values) {
                    userList.add(User.fromContentValues(value));
                }
                return userDao.insertUsers(userList).length;
            case CODE_TASK_ALL:
                List<Job> jobList = new ArrayList<>();
                for (ContentValues value : values) {
                    jobList.add(Job.fromContentValues(value));
                }
                return jobDao.insertJobs(jobList).length;
            default:
                throw new IllegalArgumentException("Invalid URI, cannot insert with ID: " + uri);
        }
    }
}
