<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_song_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="10dp"
    android:paddingEnd="5dp"
    android:paddingTop="5dp"
    android:paddingBottom="5dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_item_vertical_30"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.25"
        />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_item_vertical_90"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.9"
        />

    <ImageView
        android:id="@+id/img_song_icon"
        android:layout_width="60dp"
        android:layout_height="60dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <TextView
        android:id="@+id/tv_song_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="16dp"
        android:textAlignment="viewStart"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="@id/guideline_item_vertical_30"
        app:layout_constraintEnd_toEndOf="@id/guideline_item_vertical_90"
        />

    <TextView
        android:id="@+id/tv_song_author"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="12dp"
        android:textAlignment="viewStart"
        app:layout_constraintTop_toBottomOf="@id/tv_song_name"
        app:layout_constraintStart_toEndOf="@id/guideline_item_vertical_30"
        app:layout_constraintEnd_toEndOf="@id/guideline_item_vertical_90"
        />

    <TextView
        android:id="@+id/tv_song_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="12dp"
        android:layout_marginEnd="5dp"
        android:textAlignment="viewEnd"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/guideline_item_vertical_90"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <ImageView
        android:id="@+id/img_song_next"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_marginEnd="5dp"
        android:src="@drawable/ic_next"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>