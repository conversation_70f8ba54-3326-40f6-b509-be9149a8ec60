package com.example.androidtraining.course.database.room.entities;

import android.content.ContentValues;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.example.androidtraining.course.database.DbConstants;

import java.util.HashMap;
import java.util.Map;

@Entity(tableName = DbConstants.TASK_TABLE)
public class Job {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = DbConstants.TASK_ID_COL)
    int id;
    @ColumnInfo(name = DbConstants.TASK_TITLE_COL)
    String title;

    @ColumnInfo(name = DbConstants.TASK_ENGINEER_ID_COL)
    int engineerId;

    public Job() {}

    public Job(int id, String title, int engineerId) {
        this.id = id;
        this.title = title;
        this.engineerId = engineerId;
    }

    public Job(String title, int engineerId) {
        this.title = title;
        this.engineerId = engineerId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getEngineerId() {
        return engineerId;
    }

    public void setEngineerId(int engineerId) {
        this.engineerId = engineerId;
    }

    @NonNull
    public static Job fromContentValues(@NonNull ContentValues values) {
        Job job = new Job();
        if (values != null) {
            if (values.containsKey(DbConstants.TASK_ID_COL)) job.setId(values.getAsInteger(DbConstants.TASK_ID_COL));
            if (values.containsKey(DbConstants.TASK_TITLE_COL)) job.setTitle(values.getAsString(DbConstants.TASK_TITLE_COL));
            if (values.containsKey(DbConstants.TASK_ENGINEER_ID_COL)) job.setEngineerId(values.getAsInteger(DbConstants.TASK_ENGINEER_ID_COL));
        }
        return job;
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put(DbConstants.TASK_TITLE_COL, title);
        map.put(DbConstants.TASK_ENGINEER_ID_COL, engineerId);

        return map;
    }
}
