package com.example.androidtraining.course.broadcastreceiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.widget.Toast;

import com.example.androidtraining.utils.Logger;

public class DynamicBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = DynamicBroadcastReceiver.class.getSimpleName();

    @Override
    public void onReceive(Context context, Intent intent) {
        String extra = intent.getStringExtra("action");
        Logger.d(TAG, "DynamicBroadcastReceiver - onReceiver: action = " + intent.getAction() + ", extra=" + extra);
//        Toast.makeText(context, "DynamicBroadcastReceiver - onReceiver: " + extra, Toast.LENGTH_SHORT).show();
//        goAsync() (< 10sec) / JobScheduler: should using for heavy task, don't start new thread
    }
}
