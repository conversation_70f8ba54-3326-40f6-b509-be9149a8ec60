package com.example.androidtraining.course.database.room.entities;

import android.content.ContentValues;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.example.androidtraining.course.database.DbConstants;

//@Entity(tableName = "engineers")
@Entity(tableName = DbConstants.ENGINEER_TABLE)
public class User {
    @PrimaryKey
    @ColumnInfo(name = DbConstants.ENGINEER_ID_COL)
    int id;
//    @ColumnInfo(name = "name")
    @ColumnInfo(name = DbConstants.ENGINEER_NAME_COL)
    String name;
    @ColumnInfo(name = DbConstants.ENGINEER_GEN_COL)
    int gen;
    @ColumnInfo(name = DbConstants.ENGINEER_SINGLE_ID_COL)
    String singleId;

    public User() {}

    public User(int id, String name, int gen, String singleId) {
        this.id = id;
        this.name = name;
        this.gen = gen;
        this.singleId = singleId;
    }

    public User(String name, int gen, String singleId) {
        this.name = name;
        this.gen = gen;
        this.singleId = singleId;
    }

    public int getId() {
        return id;
    }
    public void setId(int id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getSingleId() {
        return singleId;
    }
    public void setSingleId(String singleId) {
        this.singleId = singleId;
    }
    public int getGen() {
        return gen;
    }
    public void setGen(int gen) {
        this.gen = gen;
    }

    // For ContentProvider
    @NonNull
    public static User fromContentValues(@NonNull ContentValues values) {
        final User user = new User();
        if (values != null) {
            if (values.containsKey(DbConstants.ENGINEER_ID_COL)) user.setId(values.getAsInteger(DbConstants.ENGINEER_ID_COL));
            if (values.containsKey(DbConstants.ENGINEER_NAME_COL)) user.setName(values.getAsString(DbConstants.ENGINEER_NAME_COL));
            if (values.containsKey(DbConstants.ENGINEER_GEN_COL)) user.setGen(values.getAsInteger(DbConstants.ENGINEER_GEN_COL));
            if (values.containsKey(DbConstants.ENGINEER_SINGLE_ID_COL)) user.setName(values.getAsString(DbConstants.ENGINEER_SINGLE_ID_COL));
        }
        return user;
    }
}
