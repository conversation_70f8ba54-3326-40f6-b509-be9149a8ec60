package com.example.androidtraining.course.threadhandler;

import android.annotation.SuppressLint;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.TextView;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;

import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ThreadActivity extends BaseActivity {
    private static final String TAG = ThreadActivity.class.getSimpleName();

    private Button btnTestThread;
    private TextView tvThreadCount;

    private TextView tvAsyncCount;
    private SeekBar seekBarAsync;
    private Button btnTestAsync;
    private MyAsyncTask myAsyncTask;

    private Button btnTestExecutor;
    private Button btnTestFutureConcurrent;

    private WeakReference<MyAsyncTask> mMyAsyncTaskRef;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_thread;
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate");

        btnTestThread = findViewById(R.id.btn_test_thread);
        tvThreadCount = findViewById(R.id.tv_thread_count);

        btnTestAsync = findViewById(R.id.btn_test_async);
        tvAsyncCount = findViewById(R.id.tv_count_async);
        seekBarAsync = findViewById(R.id.seek_bar_async);

        btnTestExecutor = findViewById(R.id.btn_test_executor);
        btnTestFutureConcurrent = findViewById(R.id.btn_test_future_concurrent);

        // Thread
        btnTestThread.setOnClickListener(v -> testThread());

        // AsyncTask
        btnTestAsync.setOnClickListener((v -> {
            if (myAsyncTask == null) {
                mMyAsyncTaskRef = new WeakReference<>(new MyAsyncTask());
                myAsyncTask = mMyAsyncTaskRef.get();
                myAsyncTask.execute("1", "2");
            }

            // Updated to use ExecutorService instead of deprecated AsyncTask
            executeAsyncTask("1", "2");
        }));

        // Executor vs ThreadPool
        btnTestExecutor.setOnClickListener(v -> testExecutor());


        // Future Concurrent
        btnTestFutureConcurrent.setOnClickListener(v -> testFutureConcurrent());
    }

    // Add proper cleanup for ExecutorService and Thread
    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Stop the test thread
        shouldStopThread = true;
        if (testThread != null && testThread.isAlive()) {
            testThread.interrupt();
        }

        // Shutdown executor service
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }


    private void testFutureConcurrent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            CompletableFuture<Integer> future1 = CompletableFuture.supplyAsync(() -> {
                for (int i = 0; i < 10; i++) {
                    Logger.d(TAG, "future1: " + i);
                    try {
                        Thread.sleep(1000);
                    } catch (Exception ignored) {
                    }
                }
                return 40;
            });

            CompletableFuture<Integer> future2 = CompletableFuture.supplyAsync(() -> {
                for (int i = 0; i < 5; i++) {
                    Logger.d(TAG, "future-2: " + i);
                    try {
                        Thread.sleep(1000);
                    } catch (Exception ignored) {
                    }
                }
                return 2;
            });

//        CompletableFuture<Integer> combine = future1.thenCombine(future2, Integer::sum);
            CompletableFuture<Integer> combine = future2.thenCombine(future1, Integer::sum);
            combine.thenAccept(result -> Logger.d(TAG, "result: " + result));
        } else Logger.d(TAG, "testFutureConcurrent: not supported lower N os");
    }

    private void testExecutor() {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Handler handler = new Handler(Looper.getMainLooper());

        executor.execute(new Runnable() {
            @Override
            public void run() { // worker thread
                // do background work here
                int i = 0;
                while (i < 10) {
                    try {
                        Logger.d(TAG, "testExecutor: counting - i = " + i);
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Logger.d(TAG, "testExecutor interrupted: " + e.getMessage());
                    }
                    i++;
                }
                // work done
                handler.post(new Runnable() { // sendMessageDelayed(getPostMessage(r), 0);
                    @Override
                    public void run() { // main thread
                        // update UI here
                        Logger.d(TAG, "testExecutor: work completed on main thread");
                    }
                });
            }
        });

        // Properly shutdown executor after use
        executor.shutdown();
    }

    private Thread testThread;
    private volatile boolean shouldStopThread = false;

    private void testThread() {
        // Stop previous thread if running
        if (testThread != null && testThread.isAlive()) {
            shouldStopThread = true;
            testThread.interrupt();
        }

        shouldStopThread = false;
        testThread = new Thread(new Runnable() {
            @Override
            public void run() {
                Logger.d(TAG, "thread run - " + Thread.currentThread());
                int i = 0;
                while (!shouldStopThread && i <= 100) { // Better condition to prevent infinite loop
                    Logger.d(TAG, "thread count - i - " + i);
                    i++;
                    int finalI = i;
                    tvThreadCount.post(new Runnable() {
                        @Override
                        public void run() {
                            tvThreadCount.setText("" + finalI);
                        }
                    });
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Logger.d(TAG, "thread interrupted - " + e);
                        break; // Exit gracefully when interrupted
                    }
                }
                Logger.d(TAG, "thread finished");
            }
        });
        testThread.start();
    }

    class Result {
        public int code;
        public String msg;
    }

    // Deprecated AsyncTask - kept for reference but should not be used
    @Deprecated
    class MyAsyncTask extends AsyncTask<String, Integer, Result> {
        @Override
        protected void onPreExecute() {
            Logger.d(TAG, "onPreExecute");
        }
        @Override
        protected Result doInBackground(String... strings) {
            Logger.d(TAG, "doInBackground: " + Arrays.toString(strings));
            Result result = new Result();
            while (result.code < 100) {
                publishProgress(result.code);
                result.code++;
                result.msg = "counting";
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Logger.d(TAG, "doInBackground: sleep ex=" + e);
                }
            }
            result.msg = "count done";
            return result;
        }
        @SuppressLint("SetTextI18n")
        @Override
        protected void onProgressUpdate(Integer... values) {
            Logger.d(TAG, "onProgressUpdate: " + Arrays.toString(values));
            tvAsyncCount.setText("Counting: " + values[0]);
            seekBarAsync.setProgress(values[0]); // update progress bar
        }
        @SuppressLint("SetTextI18n")
        @Override
        protected void onPostExecute(Result result) {
            Logger.d(TAG, "onPostExecute: result = " + result.code + " - " + result.msg);
            tvAsyncCount.setText(result.msg + ": result = " + result.code);
        }
    }

    // Replace deprecated AsyncTask with ExecutorService
    private ExecutorService executorService;
    private Handler mainHandler;

    private void executeAsyncTask(String... params) {
        if (executorService == null) {
            executorService = Executors.newSingleThreadExecutor();
        }
        if (mainHandler == null) {
            mainHandler = new Handler(Looper.getMainLooper());
        }

        // onPreExecute equivalent
        mainHandler.post(() -> {
            Logger.d(TAG, "onPreExecute");
            tvAsyncCount.setText("Starting...");
        });

        executorService.execute(() -> {
            Logger.d(TAG, "doInBackground: " + Arrays.toString(params));
            Result result = new Result();
            while (result.code < 100) {
                // publishProgress equivalent
                final int progress = result.code;
                mainHandler.post(() -> {
                    Logger.d(TAG, "onProgressUpdate: " + progress);
                    tvAsyncCount.setText("Counting: " + progress);
                    seekBarAsync.setProgress(progress);
                });

                result.code++;
                result.msg = "counting";
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Logger.d(TAG, "doInBackground: sleep ex=" + e);
                    return; // Exit if interrupted
                }
            }
            result.msg = "count done";

            // onPostExecute equivalent
            mainHandler.post(() -> {
                Logger.d(TAG, "onPostExecute: result = " + result.code + " - " + result.msg);
                tvAsyncCount.setText(result.msg + ": result = " + result.code);
            });
        });
    }
}