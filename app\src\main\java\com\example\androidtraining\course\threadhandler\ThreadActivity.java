package com.example.androidtraining.course.threadhandler;

import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;

import android.annotation.SuppressLint;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.TextView;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;

import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

public class ThreadActivity extends BaseActivity {
    private static final String TAG = ThreadActivity.class.getSimpleName();

    private Button btnTestThread;
    private TextView tvThreadCount;

    private TextView tvAsyncCount;
    private SeekBar seekBarAsync;
    private Button btnTestAsync;
    private MyAsyncTask myAsyncTask;
    private WeakReference<MyAsyncTask> weakReference = new WeakReference<>(null);

    private Button btnTestExecutor;
    private Button btnTestFutureConcurrent;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_thread;
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate");

        btnTestThread = findViewById(R.id.btn_test_thread);
        tvThreadCount = findViewById(R.id.tv_thread_count);

        btnTestAsync = findViewById(R.id.btn_test_async);
        tvAsyncCount = findViewById(R.id.tv_count_async);
        seekBarAsync = findViewById(R.id.seek_bar_async);

        btnTestExecutor = findViewById(R.id.btn_test_executor);
        btnTestFutureConcurrent = findViewById(R.id.btn_test_future_concurrent);

        // Thread
        btnTestThread.setOnClickListener(v -> testThread());

        // AsyncTask
        btnTestAsync.setOnClickListener((v -> {
            myAsyncTask = new MyAsyncTask();
            myAsyncTask.execute("1", "2");
//        weakReference.
        }));

        // Executor vs ThreadPool
        btnTestExecutor.setOnClickListener(v -> testExecutor());


        // Future Concurrent
        btnTestFutureConcurrent.setOnClickListener(v -> testFutureConcurrent());
    }

    private void testFutureConcurrent() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            CompletableFuture<Integer> future1 = CompletableFuture.supplyAsync(() -> {
                for (int i = 0; i < 10; i++) {
                    Logger.d(TAG, "future1: " + i);
                    try {
                        Thread.sleep(1000);
                    } catch (Exception ignored) {
                    }
                }
                return 40;
            });

            CompletableFuture<Integer> future2 = CompletableFuture.supplyAsync(() -> {
                for (int i = 0; i < 5; i++) {
                    Logger.d(TAG, "future-2: " + i);
                    try {
                        Thread.sleep(1000);
                    } catch (Exception ignored) {
                    }
                }
                return 2;
            });

//        CompletableFuture<Integer> combine = future1.thenCombine(future2, Integer::sum);
            CompletableFuture<Integer> combine = future2.thenCombine(future1, Integer::sum);
            combine.thenAccept(result -> Logger.d(TAG, "result: " + result));
        } else Logger.d(TAG, "testFutureConcurrent: not supported lower N os");
    }

    private void testExecutor() {
        Executor executor = Executors.newSingleThreadExecutor();
        Handler handler = new Handler(Looper.getMainLooper());

        executor.execute(new Runnable() {
            @Override
            public void run() { // worker thread
                // do background work here
                // .......
                // work done
                handler.post(new Runnable() { // sendMessageDelayed(getPostMessage(r), 0);
                    @Override
                    public void run() { // main thread
                        // update UI here
                    }
                });
            }
        });
    }

    private void testThread() {
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                Logger.d(TAG, "thread run - " + Thread.currentThread());
                int i = 0;
                while (true) {
                    Logger.d(TAG, "thread count - i - " + i);
                    i++;
                    int finalI = i;
                    tvThreadCount.post(new Runnable() {
                        @Override
                        public void run() {
                            tvThreadCount.setText("" + finalI);
                        }
                    });
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Logger.d(TAG, "thread run - " + e);
                    }
                    if (i > 100) break; // stop thread after 100 seconds
                }
            }
        });
        thread.start();
//        thread.run();
    }

    class Result {
        public int code;
        public String msg;
    }
    class MyAsyncTask extends AsyncTask<String, Integer, Result> {
        @Override
        protected void onPreExecute() {
            Logger.d(TAG, "onPreExecute");
        }
        @Override
        protected Result doInBackground(String... strings) {
            Logger.d(TAG, "doInBackground: " + Arrays.toString(strings));
            Result result = new Result();
            while (result.code < 100) {
                publishProgress(result.code);
                result.code++;
                result.msg = "counting";
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Logger.d(TAG, "doInBackground: sleep ex=" + e);
                }
            }
            result.msg = "count done";
            return result;
        }
        @SuppressLint("SetTextI18n")
        @Override
        protected void onProgressUpdate(Integer... values) {
            Logger.d(TAG, "onProgressUpdate: " + Arrays.toString(values));
            tvAsyncCount.setText("Counting: " + values[0]);
            seekBarAsync.setProgress(values[0]); // update progress bar
        }
        @SuppressLint("SetTextI18n")
        @Override
        protected void onPostExecute(Result result) {
            Logger.d(TAG, "onPostExecute: result = " + result.code + " - " + result.msg);
            tvAsyncCount.setText(result.msg + ": result = " + result.code);
        }
    }
}