com.example.androidtraining.app-jetified-lifecycle-viewmodel-savedstate-2.6.2-0 C:\Users\<USER>\.gradle\caches\8.11\transforms\021aa5c135ea41246cd81f18aef73e08\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\res
com.example.androidtraining.app-jetified-startup-runtime-1.1.1-1 C:\Users\<USER>\.gradle\caches\8.11\transforms\0b9035748fab9e9e0bc8ec7646af896f\transformed\jetified-startup-runtime-1.1.1\res
com.example.androidtraining.app-lifecycle-livedata-core-2.6.2-2 C:\Users\<USER>\.gradle\caches\8.11\transforms\106c1a2a67ec136c928fddda77e8e745\transformed\lifecycle-livedata-core-2.6.2\res
com.example.androidtraining.app-jetified-emoji2-1.3.0-3 C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\res
com.example.androidtraining.app-jetified-lifecycle-viewmodel-ktx-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.11\transforms\193d153386bf3b4b4b92269ab3d6da57\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\res
com.example.androidtraining.app-jetified-activity-ktx-1.10.1-5 C:\Users\<USER>\.gradle\caches\8.11\transforms\1a9a64ae7cf7f7ba66ad00ea0bd568da\transformed\jetified-activity-ktx-1.10.1\res
com.example.androidtraining.app-jetified-savedstate-ktx-1.2.1-6 C:\Users\<USER>\.gradle\caches\8.11\transforms\2441a2181b644ce9b70446048016e7e0\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.androidtraining.app-core-runtime-2.2.0-7 C:\Users\<USER>\.gradle\caches\8.11\transforms\25a380b47674329a3f076148981b0f41\transformed\core-runtime-2.2.0\res
com.example.androidtraining.app-jetified-lifecycle-runtime-ktx-2.6.2-8 C:\Users\<USER>\.gradle\caches\8.11\transforms\2f07d74e157a9f24338b8f6b5e7d266c\transformed\jetified-lifecycle-runtime-ktx-2.6.2\res
com.example.androidtraining.app-browser-1.4.0-9 C:\Users\<USER>\.gradle\caches\8.11\transforms\368b07b4e9b7912a6e22f44deed31ddf\transformed\browser-1.4.0\res
com.example.androidtraining.app-jetified-lifecycle-process-2.6.2-10 C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\res
com.example.androidtraining.app-core-1.16.0-11 C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\res
com.example.androidtraining.app-jetified-annotation-experimental-1.4.1-12 C:\Users\<USER>\.gradle\caches\8.11\transforms\3c3cfbba5619225bed27ba7293615253\transformed\jetified-annotation-experimental-1.4.1\res
com.example.androidtraining.app-jetified-play-services-base-18.1.0-13 C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\res
com.example.androidtraining.app-material-1.12.0-14 C:\Users\<USER>\.gradle\caches\8.11\transforms\3e3a03410c222d389eb5acdf62f3d148\transformed\material-1.12.0\res
com.example.androidtraining.app-jetified-sqlite-framework-release-15 C:\Users\<USER>\.gradle\caches\8.11\transforms\40d8f25fe4ff665e385c34c138d2ffba\transformed\jetified-sqlite-framework-release\res
com.example.androidtraining.app-jetified-core-viewtree-1.0.0-16 C:\Users\<USER>\.gradle\caches\8.11\transforms\486dcb621057a2615fd863ee63882bae\transformed\jetified-core-viewtree-1.0.0\res
com.example.androidtraining.app-constraintlayout-2.2.1-17 C:\Users\<USER>\.gradle\caches\8.11\transforms\4c8c51a1a3225a01967f94847ffcc76d\transformed\constraintlayout-2.2.1\res
com.example.androidtraining.app-fragment-1.5.4-18 C:\Users\<USER>\.gradle\caches\8.11\transforms\558d81a7edca70cb3fd7ddbc2a4d6d2d\transformed\fragment-1.5.4\res
com.example.androidtraining.app-jetified-credentials-1.2.0-rc01-19 C:\Users\<USER>\.gradle\caches\8.11\transforms\60115c31cdc3e8b7d77a5b34ad2884ea\transformed\jetified-credentials-1.2.0-rc01\res
com.example.androidtraining.app-jetified-tracing-1.2.0-20 C:\Users\<USER>\.gradle\caches\8.11\transforms\63a250dbc0f87c2711b5f5b21b596be4\transformed\jetified-tracing-1.2.0\res
com.example.androidtraining.app-jetified-savedstate-1.2.1-21 C:\Users\<USER>\.gradle\caches\8.11\transforms\6dbb5bc14392ca7ef978a3ebcee7010d\transformed\jetified-savedstate-1.2.1\res
com.example.androidtraining.app-jetified-profileinstaller-1.4.0-22 C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\res
com.example.androidtraining.app-lifecycle-livedata-2.6.2-23 C:\Users\<USER>\.gradle\caches\8.11\transforms\7c3e0705a88a964252b4a934dbd92b11\transformed\lifecycle-livedata-2.6.2\res
com.example.androidtraining.app-jetified-play-services-basement-18.4.0-24 C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\res
com.example.androidtraining.app-lifecycle-viewmodel-2.6.2-25 C:\Users\<USER>\.gradle\caches\8.11\transforms\86c9a94664088b153a4e39504897a03e\transformed\lifecycle-viewmodel-2.6.2\res
com.example.androidtraining.app-lifecycle-runtime-2.6.2-26 C:\Users\<USER>\.gradle\caches\8.11\transforms\889317c0397e8e088495ab6acb4e71dd\transformed\lifecycle-runtime-2.6.2\res
com.example.androidtraining.app-cardview-1.0.0-27 C:\Users\<USER>\.gradle\caches\8.11\transforms\90772ddcb9edf7e15b4df4328f8bebbb\transformed\cardview-1.0.0\res
com.example.androidtraining.app-jetified-play-services-auth-20.7.0-28 C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\res
com.example.androidtraining.app-drawerlayout-1.1.1-29 C:\Users\<USER>\.gradle\caches\8.11\transforms\9a8860da6e73b4fb9c97b73de56f7f5c\transformed\drawerlayout-1.1.1\res
com.example.androidtraining.app-jetified-viewpager2-1.0.0-30 C:\Users\<USER>\.gradle\caches\8.11\transforms\a43ce57c16c9c156e0fe94d42baa1a99\transformed\jetified-viewpager2-1.0.0\res
com.example.androidtraining.app-jetified-core-ktx-1.16.0-31 C:\Users\<USER>\.gradle\caches\8.11\transforms\b3b1d1a6741186d7c23ba66b4144d119\transformed\jetified-core-ktx-1.16.0\res
com.example.androidtraining.app-jetified-activity-1.10.1-32 C:\Users\<USER>\.gradle\caches\8.11\transforms\b9d3d8229028e2208bbfcf80727e7065\transformed\jetified-activity-1.10.1\res
com.example.androidtraining.app-coordinatorlayout-1.1.0-33 C:\Users\<USER>\.gradle\caches\8.11\transforms\c3c255fa386ca3a73fce55181abaf033\transformed\coordinatorlayout-1.1.0\res
com.example.androidtraining.app-jetified-emoji2-views-helper-1.3.0-34 C:\Users\<USER>\.gradle\caches\8.11\transforms\cf6b953fe0a9083ee2ef794c8f55b21c\transformed\jetified-emoji2-views-helper-1.3.0\res
com.example.androidtraining.app-jetified-credentials-play-services-auth-1.2.0-rc01-35 C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.androidtraining.app-jetified-sqlite-release-36 C:\Users\<USER>\.gradle\caches\8.11\transforms\d9cb1f4e7bfe99caf7b31d3aae060078\transformed\jetified-sqlite-release\res
com.example.androidtraining.app-jetified-room-runtime-release-37 C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\res
com.example.androidtraining.app-appcompat-1.7.1-38 C:\Users\<USER>\.gradle\caches\8.11\transforms\e7f24da73f275a8a791d04076c131d9f\transformed\appcompat-1.7.1\res
com.example.androidtraining.app-jetified-firebase-common-21.0.0-39 C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\res
com.example.androidtraining.app-transition-1.5.0-40 C:\Users\<USER>\.gradle\caches\8.11\transforms\f3067ca641cd059d8f2893f57dd39a34\transformed\transition-1.5.0\res
com.example.androidtraining.app-jetified-appcompat-resources-1.7.1-41 C:\Users\<USER>\.gradle\caches\8.11\transforms\f33265535b69c0b3de4977a1d1da2c59\transformed\jetified-appcompat-resources-1.7.1\res
com.example.androidtraining.app-recyclerview-1.1.0-42 C:\Users\<USER>\.gradle\caches\8.11\transforms\f7dfb688e05bf61f4f3b680110338cbe\transformed\recyclerview-1.1.0\res
com.example.androidtraining.app-jetified-core-common-2.0.3-43 C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\res
com.example.androidtraining.app-pngs-44 D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\pngs\debug
com.example.androidtraining.app-res-45 D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\processDebugGoogleServices
com.example.androidtraining.app-resValues-46 D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\resValues\debug
com.example.androidtraining.app-packageDebugResources-47 D:\AndroidStudioProjects\AndroidTraining\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.androidtraining.app-packageDebugResources-48 D:\AndroidStudioProjects\AndroidTraining\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.androidtraining.app-debug-49 D:\AndroidStudioProjects\AndroidTraining\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.androidtraining.app-debug-50 D:\AndroidStudioProjects\AndroidTraining\app\src\debug\res
com.example.androidtraining.app-main-51 D:\AndroidStudioProjects\AndroidTraining\app\src\main\res
