package com.example.androidtraining.course.adapter;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;
import com.google.android.material.snackbar.Snackbar;

import java.util.ArrayList;
import java.util.List;

/**
 * A simple {@link Fragment} subclass.
 * Use the  factory method to
 * create an instance of this fragment.
 */
public class RecycleViewFragment extends Fragment
//        implements ItemClickListenerInterface
{
    public static final String TAG = RecycleViewFragment.class.getSimpleName();

    Context mContext;
    RecyclerView mRecyclerView;
    SongAdapterRecycleView mCustomSongAdapterRecycleView;
    List<MySong> mListSong = new ArrayList<>();

    public RecycleViewFragment() {
        Logger.d(TAG, "constructor: mListSong size=" + mListSong.size());
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        Logger.d(TAG, "onAttach");
        mContext = context;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate: mListSong size=" + mListSong.size());
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Logger.d(TAG, "onCreateView, mListSong size=" + mListSong.size());
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_recycle_view, container, false);
        mRecyclerView = view.findViewById(R.id.recycle_view_song);
        mCustomSongAdapterRecycleView = new SongAdapterRecycleView(mContext, mListSong);

        // set orientation
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
        layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mRecyclerView.setLayoutManager(layoutManager);
        // set item divider
        mRecyclerView.addItemDecoration(new DividerItemDecoration(mContext, LinearLayoutManager.VERTICAL));

        // bind Adapter
        mRecyclerView.setAdapter(mCustomSongAdapterRecycleView);
//        mCustomSongAdapterRecycleView.setItemClickListener(this);

        // set click event
        mCustomSongAdapterRecycleView.setItemClickListener(new SongItemClickListenerInterface() {
            @Override
            public void onItemClick(View view, int position) {
                Snackbar.make(view, "onClick pos: " + position, Snackbar.LENGTH_LONG).show();
            }

            @Override
            public void onItemLongClick(View view, int position) {
                Snackbar.make(view, "onLongPress pos: " + position, Snackbar.LENGTH_LONG).show();
            }

            @Override
            public void onNextSongClick(View view, int position) {
                Snackbar.make(view, "onNextSongClick pos: " + position, Snackbar.LENGTH_LONG).show();
            }
        });

        // Drag & Drop

        ItemTouchHelper.Callback itemTouch = new ItemTouchHelper.SimpleCallback(
                ItemTouchHelper.UP|ItemTouchHelper.DOWN, ItemTouchHelper.DOWN) {
            @Override
            public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
                int srcPos = viewHolder.getAdapterPosition();
                int dstPos = target.getAdapterPosition();
                Logger.d(TAG, "onMove, srcPos=" + srcPos + " dstPos=" + dstPos);
                recyclerView.getAdapter().notifyItemMoved(srcPos, dstPos);
                // save this moving position
                swapSong(srcPos, dstPos);
                return true;
            }

            @Override
            public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {
                Logger.d(TAG, "onSwiped, pos=" + viewHolder.getAdapterPosition() + " direction=" + direction);
            }
        };
        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(itemTouch);
        itemTouchHelper.attachToRecyclerView(mRecyclerView);

        // test
//        List<FileName> listFileName = new ArrayList<>();
//        for (int i = 0; i < 20; i++) {
//            listFileName.add(new FileName(R.drawable.ic_launcher_foreground, "File name " + i));
//        }
//        FileAdapter fileAdapter = new FileAdapter(mContext, listFileName);
//        mRecyclerView.setAdapter(fileAdapter);

        return view;
    }

    public void setListSong(@NonNull List<MySong> listSong) {
        mListSong = listSong;
    }

    private void swapSong(int idx1, int idx2) {
        if (idx1 >= mListSong.size() || idx2 >= mListSong.size()) return;
        MySong temp = mListSong.get(idx1);
        mListSong.set(idx1, mListSong.get(idx2));
        mListSong.set(idx2, temp);
    }
//
//    @Override
//    public void onItemClick(View view, int position) {
//
//    }
//
//    @Override
//    public void onItemLongPress(View view, int position) {
//
//    }

//    @Override
//    public void onStart() {
//        super.onStart();
//        Logger.d(TAG, "onStart");
//    }
//
//    @Override
//    public void onResume() {
//        super.onResume();
//        Logger.d(TAG, "onResume");
//    }
//
//    @Override
//    public void onStop() {
//        super.onStop();
//        Logger.d(TAG, "onStop");
//    }
//
//    @Override
//    public void onPause() {
//        super.onPause();
//        Logger.d(TAG, "onPause");
//    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Logger.d(TAG, "onDestroyView");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy");
    }

    @Override
    public void onDetach() {
        super.onDetach();
        Logger.d(TAG, "onDetach");
    }

    //   Callback from B -> A:
    // Step 1: define interface callback
    interface IListener {
        void onClick(String msg);
        //....
    }
    // Step 2: B define listener and has method setListener
    // when B has any event, call the callback to send data from B to A by listener
    class B {
        IListener listener; // ~ A
        public void setListener(IListener listener) {
            this.listener = listener;
        }
        public void onEvent() { // send data from B to A in here
            if (listener != null) listener.onClick("hello");
        }
    }
    //  Step 3: A contains instance of B, then set listener for this instance.
    class A implements IListener  {
        A() { // constructor
            B b = new B();
            b.setListener(this); // set listener for B, this listener is A.
        }
        @Override
        public void onClick(String msg) {
            // TODO msg: the callback from B is called here
        }
    }
}