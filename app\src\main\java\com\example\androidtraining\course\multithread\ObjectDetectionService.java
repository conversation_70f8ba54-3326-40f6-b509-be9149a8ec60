package com.example.androidtraining.course.multithread;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.ExifInterface;
import android.text.TextUtils;

import com.example.androidtraining.utils.Logger;
import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.objects.DetectedObject;
import com.google.mlkit.vision.objects.ObjectDetection;
import com.google.mlkit.vision.objects.ObjectDetector;
import com.google.mlkit.vision.objects.defaults.ObjectDetectorOptions;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ObjectDetectionService {
    private static final String TAG = ObjectDetectionService.class.getSimpleName();
    
    private final Context context;
    private final ObjectDetector objectDetector;
    private final ExecutorService executorService;
    private final RoomImageDao imageDao;
    
    // Object labels mapping
    private static final Map<Integer, String> OBJECT_LABELS = new HashMap<>();
    
//    static {
        // ML Kit Object Detection labels
//        OBJECT_LABELS.put(DetectedObject.TYPE_UNKNOWN, "Unknown");
//        OBJECT_LABELS.put(DetectedObject.TYPE_HOME_GOOD, "Home Good");
//        OBJECT_LABELS.put(DetectedObject.TYPE_FASHION_GOOD, "Fashion");
//        OBJECT_LABELS.put(DetectedObject.TYPE_FOOD, "Food");
//        OBJECT_LABELS.put(DetectedObject.TYPE_PLACE, "Place");
//        OBJECT_LABELS.put(DetectedObject.TYPE_PLANT, "Plant");
        
        // Add more specific labels based on your needs
        // Note: ML Kit's basic object detection has limited categories
        // For more specific detection (person, car, etc.), you'd need custom models
//    }
    
    public interface DetectionCallback {
        void onDetectionComplete(String detectedObjects);
        void onDetectionError(String error);
    }
    
    public ObjectDetectionService(Context context) {
        this.context = context;
        this.imageDao = RoomImageDatabase.getDatabase(context).roomImageDao();
        this.executorService = Executors.newSingleThreadExecutor();
        
        // Configure object detector
        ObjectDetectorOptions options = new ObjectDetectorOptions.Builder()
                .setDetectorMode(ObjectDetectorOptions.SINGLE_IMAGE_MODE)
                .enableMultipleObjects()
                .enableClassification()
                .build();
        
        this.objectDetector = ObjectDetection.getClient(options);
    }
    
    public void detectObjectsInImage(RoomImage image, DetectionCallback callback) {
        if (image.isProcessed()) {
            // Already processed, return existing results
            callback.onDetectionComplete(image.getDetectedObjects());
            return;
        }
        
        executorService.execute(() -> {
            try {
                File imageFile = new File(image.getPath());
                if (!imageFile.exists()) {
                    callback.onDetectionError("Image file not found");
                    return;
                }
                
                // Load and prepare image
                Bitmap bitmap = loadAndPrepareBitmap(image.getPath());
                if (bitmap == null) {
                    callback.onDetectionError("Could not load image");
                    return;
                }
                
                // Create InputImage for ML Kit
                InputImage inputImage = InputImage.fromBitmap(bitmap, 0);
                
                // Perform object detection
                objectDetector.process(inputImage)
                        .addOnSuccessListener(detectedObjects -> {
                            try {
                                String objectsJson = processDetectedObjects(detectedObjects);
                                
                                // Update database
                                imageDao.updateDetectedObjects(image.getId(), objectsJson);
                                
                                callback.onDetectionComplete(objectsJson);
                                
                            } catch (Exception e) {
                                Logger.e(TAG, "Error processing detected objects: " + e.getMessage());
                                callback.onDetectionError("Error processing results");
                            }
                        })
                        .addOnFailureListener(e -> {
                            Logger.e(TAG, "Object detection failed: " + e.getMessage());
                            callback.onDetectionError("Detection failed: " + e.getMessage());
                        });
                
            } catch (Exception e) {
                Logger.e(TAG, "Error in object detection: " + e.getMessage());
                callback.onDetectionError("Detection error: " + e.getMessage());
            }
        });
    }
    
    private Bitmap loadAndPrepareBitmap(String imagePath) {
        try {
            // Load bitmap with appropriate scaling to prevent OOM
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(imagePath, options);
            
            // Calculate sample size (max 1024x1024 for ML Kit)
            int sampleSize = calculateSampleSize(options, 1024, 1024);
            options.inSampleSize = sampleSize;
            options.inJustDecodeBounds = false;
            
            Bitmap bitmap = BitmapFactory.decodeFile(imagePath, options);
            
            if (bitmap == null) {
                return null;
            }
            
            // Handle image rotation based on EXIF data
            bitmap = rotateImageIfRequired(bitmap, imagePath);
            
            return bitmap;
            
        } catch (Exception e) {
            Logger.e(TAG, "Error loading bitmap: " + e.getMessage());
            return null;
        }
    }
    
    private int calculateSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;
        
        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;
            
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        
        return inSampleSize;
    }
    
    private Bitmap rotateImageIfRequired(Bitmap bitmap, String imagePath) {
        try {
            ExifInterface exif = new ExifInterface(imagePath);
            int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    return rotateBitmap(bitmap, 90);
                case ExifInterface.ORIENTATION_ROTATE_180:
                    return rotateBitmap(bitmap, 180);
                case ExifInterface.ORIENTATION_ROTATE_270:
                    return rotateBitmap(bitmap, 270);
                default:
                    return bitmap;
            }
        } catch (Exception e) {
            Logger.e(TAG, "Error reading EXIF data: " + e.getMessage());
            return bitmap;
        }
    }
    
    private Bitmap rotateBitmap(Bitmap bitmap, float degrees) {
        android.graphics.Matrix matrix = new android.graphics.Matrix();
        matrix.postRotate(degrees);
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }
    
    private String processDetectedObjects(List<DetectedObject> detectedObjects) {
        try {
            JSONArray objectsArray = new JSONArray();
            List<String> objectNames = new ArrayList<>();
            
            for (DetectedObject detectedObject : detectedObjects) {
                JSONObject objectJson = new JSONObject();
                
                // Get object bounds
                android.graphics.Rect bounds = detectedObject.getBoundingBox();
                objectJson.put("bounds", String.format("%d,%d,%d,%d", 
                    bounds.left, bounds.top, bounds.right, bounds.bottom));
                
                // Get tracking ID if available
                if (detectedObject.getTrackingId() != null) {
                    objectJson.put("trackingId", detectedObject.getTrackingId());
                }
                
                // Get labels with confidence
                JSONArray labelsArray = new JSONArray();
                for (DetectedObject.Label label : detectedObject.getLabels()) {
                    JSONObject labelJson = new JSONObject();
//                    String labelText = OBJECT_LABELS.getOrDefault(label.getIndex(), "Unknown");
                    String labelText = label.getText();
                    if (TextUtils.isEmpty(labelText)) labelText = "Unknown";
                    labelJson.put("text", labelText);
                    labelJson.put("confidence", label.getConfidence());
                    labelsArray.put(labelJson);
                    Logger.d(TAG, "Detected object: " + labelText + ", Confidence: " + label.getConfidence() + ", thread: " + Thread.currentThread().getName());
                    // Add to simple list for easy searching
                    if (label.getConfidence() > 0.5f) { // Only high confidence objects
                        objectNames.add(labelText.toLowerCase());
                    }
                }
                objectJson.put("labels", labelsArray);
                
                objectsArray.put(objectJson);
            }
            
            // Create final result
            JSONObject result = new JSONObject();
            result.put("objects", objectsArray);
            result.put("objectNames", new JSONArray(objectNames)); // For easy searching
            result.put("detectionTime", System.currentTimeMillis());
            
            return result.toString();
            
        } catch (Exception e) {
            Logger.e(TAG, "Error processing detected objects 0: " + e.getMessage());
            return "{}";
        }
    }
    
    public void processUnprocessedImages(ProcessingCallback callback) {
        executorService.execute(() -> {
            try {
                List<RoomImage> unprocessedImages = imageDao.getUnprocessedImages();
                int total = unprocessedImages.size();
                int processed = 0;
                
                Logger.d(TAG, "Processing " + total + " unprocessed images");
                
                for (RoomImage image : unprocessedImages) {
                    detectObjectsInImage(image, new DetectionCallback() {
                        @Override
                        public void onDetectionComplete(String detectedObjects) {
                            // Detection completed, database already updated
                        }
                        
                        @Override
                        public void onDetectionError(String error) {
                            Logger.e(TAG, "Detection error for image " + image.getName() + ": " + error);
                            // Mark as processed even if failed to avoid reprocessing
                            imageDao.updateDetectedObjects(image.getId(), "{}");
                        }
                    });
                    
                    processed++;
                    final int currentProgress = processed;
                    if (callback != null) {
                        callback.onProgress(currentProgress, total);
                    }
                    
                    // Small delay to prevent overwhelming the system
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        break;
                    }
                }
                
                if (callback != null) {
                    callback.onComplete();
                }
                
            } catch (Exception e) {
                Logger.e(TAG, "Error processing unprocessed images: " + e.getMessage());
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }
    
    public interface ProcessingCallback {
        void onProgress(int current, int total);
        void onComplete();
        void onError(String error);
    }
    
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        if (objectDetector != null) {
            objectDetector.close();
        }
    }
    
    // Helper method to extract simple object names from JSON
    public static List<String> extractObjectNames(String detectedObjectsJson) {
        List<String> objectNames = new ArrayList<>();
        try {
            if (detectedObjectsJson != null && !detectedObjectsJson.isEmpty() && !detectedObjectsJson.equals("{}")) {
                JSONObject json = new JSONObject(detectedObjectsJson);
                if (json.has("objectNames")) {
                    JSONArray namesArray = json.getJSONArray("objectNames");
                    for (int i = 0; i < namesArray.length(); i++) {
                        objectNames.add(namesArray.getString(i));
                    }
                }
            }
        } catch (Exception e) {
            Logger.e(TAG, "Error extracting object names: " + e.getMessage());
        }
        return objectNames;
    }
}
