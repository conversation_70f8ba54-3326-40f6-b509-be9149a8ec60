package com.example.androidtraining.course.multithread;

import androidx.annotation.NonNull;
import androidx.room.EntityDeleteOrUpdateAdapter;
import androidx.room.EntityInsertAdapter;
import androidx.room.RoomDatabase;
import androidx.room.util.DBUtil;
import androidx.room.util.SQLiteStatementUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.SQLiteStatement;
import java.lang.Class;
import java.lang.Integer;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation", "removal"})
public final class RoomImageDao_Impl implements RoomImageDao {
  private final RoomDatabase __db;

  private final EntityInsertAdapter<RoomImage> __insertAdapterOfRoomImage;

  private final EntityDeleteOrUpdateAdapter<RoomImage> __deleteAdapterOfRoomImage;

  private final EntityDeleteOrUpdateAdapter<RoomImage> __updateAdapterOfRoomImage;

  public RoomImageDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertAdapterOfRoomImage = new EntityInsertAdapter<RoomImage>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `room_images` (`id`,`name`,`path`,`size`,`width`,`height`,`date_taken`,`date_modified`,`is_liked`,`thumbnail_path`,`mime_type`,`detected_objects`,`is_processed`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final RoomImage entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindText(2, entity.getName());
        }
        if (entity.getPath() == null) {
          statement.bindNull(3);
        } else {
          statement.bindText(3, entity.getPath());
        }
        statement.bindLong(4, entity.getSize());
        statement.bindLong(5, entity.getWidth());
        statement.bindLong(6, entity.getHeight());
        statement.bindLong(7, entity.getDateTaken());
        statement.bindLong(8, entity.getDateModified());
        final int _tmp = entity.isLiked() ? 1 : 0;
        statement.bindLong(9, _tmp);
        if (entity.getThumbnailPath() == null) {
          statement.bindNull(10);
        } else {
          statement.bindText(10, entity.getThumbnailPath());
        }
        if (entity.getMimeType() == null) {
          statement.bindNull(11);
        } else {
          statement.bindText(11, entity.getMimeType());
        }
        if (entity.getDetectedObjects() == null) {
          statement.bindNull(12);
        } else {
          statement.bindText(12, entity.getDetectedObjects());
        }
        final int _tmp_1 = entity.isProcessed() ? 1 : 0;
        statement.bindLong(13, _tmp_1);
      }
    };
    this.__deleteAdapterOfRoomImage = new EntityDeleteOrUpdateAdapter<RoomImage>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `room_images` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final RoomImage entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfRoomImage = new EntityDeleteOrUpdateAdapter<RoomImage>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `room_images` SET `id` = ?,`name` = ?,`path` = ?,`size` = ?,`width` = ?,`height` = ?,`date_taken` = ?,`date_modified` = ?,`is_liked` = ?,`thumbnail_path` = ?,`mime_type` = ?,`detected_objects` = ?,`is_processed` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final RoomImage entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindText(2, entity.getName());
        }
        if (entity.getPath() == null) {
          statement.bindNull(3);
        } else {
          statement.bindText(3, entity.getPath());
        }
        statement.bindLong(4, entity.getSize());
        statement.bindLong(5, entity.getWidth());
        statement.bindLong(6, entity.getHeight());
        statement.bindLong(7, entity.getDateTaken());
        statement.bindLong(8, entity.getDateModified());
        final int _tmp = entity.isLiked() ? 1 : 0;
        statement.bindLong(9, _tmp);
        if (entity.getThumbnailPath() == null) {
          statement.bindNull(10);
        } else {
          statement.bindText(10, entity.getThumbnailPath());
        }
        if (entity.getMimeType() == null) {
          statement.bindNull(11);
        } else {
          statement.bindText(11, entity.getMimeType());
        }
        if (entity.getDetectedObjects() == null) {
          statement.bindNull(12);
        } else {
          statement.bindText(12, entity.getDetectedObjects());
        }
        final int _tmp_1 = entity.isProcessed() ? 1 : 0;
        statement.bindLong(13, _tmp_1);
        statement.bindLong(14, entity.getId());
      }
    };
  }

  @Override
  public long insertImage(final RoomImage image) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      return __insertAdapterOfRoomImage.insertAndReturnId(_connection, image);
    });
  }

  @Override
  public void insertImages(final List<RoomImage> images) {
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      __insertAdapterOfRoomImage.insert(_connection, images);
      return null;
    });
  }

  @Override
  public void deleteImage(final RoomImage image) {
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      __deleteAdapterOfRoomImage.handle(_connection, image);
      return null;
    });
  }

  @Override
  public void updateImage(final RoomImage image) {
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      __updateAdapterOfRoomImage.handle(_connection, image);
      return null;
    });
  }

  @Override
  public List<RoomImage> getAllImages() {
    final String _sql = "SELECT * FROM room_images ORDER BY date_taken DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public RoomImage getImageById(final int id) {
    final String _sql = "SELECT * FROM room_images WHERE id = ?";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final RoomImage _result;
        if (_stmt.step()) {
          _result = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _result.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _result.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _result.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _result.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _result.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _result.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _result.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _result.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _result.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _result.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _result.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _result.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _result.setProcessed(_tmpIsProcessed);
        } else {
          _result = null;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public RoomImage getImageByPath(final String path) {
    final String _sql = "SELECT * FROM room_images WHERE path = ?";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (path == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, path);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final RoomImage _result;
        if (_stmt.step()) {
          _result = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _result.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _result.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _result.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _result.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _result.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _result.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _result.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _result.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _result.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _result.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _result.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _result.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _result.setProcessed(_tmpIsProcessed);
        } else {
          _result = null;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getImagesByName(final String name) {
    final String _sql = "SELECT * FROM room_images WHERE name LIKE ?";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (name == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, name);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getLikedImages() {
    final String _sql = "SELECT * FROM room_images WHERE is_liked = 1 ORDER BY date_taken DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getImagesByDateRange(final long startDate, final long endDate) {
    final String _sql = "SELECT * FROM room_images WHERE date_taken BETWEEN ? AND ? ORDER BY date_taken DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, startDate);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, endDate);
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getImagesByDetectedObject(final String objectName) {
    final String _sql = "SELECT * FROM room_images WHERE detected_objects LIKE ? ORDER BY date_taken DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (objectName == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, objectName);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getAllImagesSortedByDateAsc() {
    final String _sql = "SELECT * FROM room_images ORDER BY date_taken ASC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getAllImagesSortedByDateDesc() {
    final String _sql = "SELECT * FROM room_images ORDER BY date_taken DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getAllImagesSortedByNameAsc() {
    final String _sql = "SELECT * FROM room_images ORDER BY name ASC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getAllImagesSortedByNameDesc() {
    final String _sql = "SELECT * FROM room_images ORDER BY name DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getAllImagesSortedBySizeAsc() {
    final String _sql = "SELECT * FROM room_images ORDER BY size ASC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getAllImagesSortedBySizeDesc() {
    final String _sql = "SELECT * FROM room_images ORDER BY size DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public int getImageCount() {
    final String _sql = "SELECT COUNT(*) FROM room_images";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _result;
        if (_stmt.step()) {
          _result = (int) (_stmt.getLong(0));
        } else {
          _result = 0;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public int getLikedImageCount() {
    final String _sql = "SELECT COUNT(*) FROM room_images WHERE is_liked = 1";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _result;
        if (_stmt.step()) {
          _result = (int) (_stmt.getLong(0));
        } else {
          _result = 0;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public long getTotalSize() {
    final String _sql = "SELECT SUM(size) FROM room_images";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final long _result;
        if (_stmt.step()) {
          _result = _stmt.getLong(0);
        } else {
          _result = 0L;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getUnprocessedImages() {
    final String _sql = "SELECT * FROM room_images WHERE is_processed = 0";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> searchImages(final String query) {
    final String _sql = "SELECT * FROM room_images WHERE name LIKE '%' || ? || '%' OR detected_objects LIKE '%' || ? || '%' ORDER BY date_taken DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (query == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, query);
        }
        _argIndex = 2;
        if (query == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, query);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public boolean imageExists(final String path) {
    final String _sql = "SELECT EXISTS(SELECT 1 FROM room_images WHERE path = ?)";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (path == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, path);
        }
        final boolean _result;
        if (_stmt.step()) {
          final int _tmp;
          _tmp = (int) (_stmt.getLong(0));
          _result = _tmp != 0;
        } else {
          _result = false;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getImagesByDate(final long date) {
    final String _sql = "SELECT * FROM room_images WHERE date(date_taken/1000, 'unixepoch') = date(?/1000, 'unixepoch') ORDER BY date_taken DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, date);
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<String> getDistinctDates() {
    final String _sql = "SELECT DISTINCT date(date_taken/1000, 'unixepoch') as date FROM room_images ORDER BY date DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final List<String> _result = new ArrayList<String>();
        while (_stmt.step()) {
          final String _item;
          final String _tmp;
          if (_stmt.isNull(0)) {
            _tmp = null;
          } else {
            _tmp = _stmt.getText(0);
          }
          _item = _tmp;
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<RoomImage> getImagesByMonth(final String yearMonth) {
    final String _sql = "SELECT * FROM room_images WHERE strftime('%Y-%m', date_taken/1000, 'unixepoch') = ? ORDER BY date_taken DESC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (yearMonth == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, yearMonth);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "path");
        final int _columnIndexOfSize = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "size");
        final int _columnIndexOfWidth = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "width");
        final int _columnIndexOfHeight = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "height");
        final int _columnIndexOfDateTaken = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_taken");
        final int _columnIndexOfDateModified = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "date_modified");
        final int _columnIndexOfIsLiked = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_liked");
        final int _columnIndexOfThumbnailPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "thumbnail_path");
        final int _columnIndexOfMimeType = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "mime_type");
        final int _columnIndexOfDetectedObjects = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "detected_objects");
        final int _columnIndexOfIsProcessed = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "is_processed");
        final List<RoomImage> _result = new ArrayList<RoomImage>();
        while (_stmt.step()) {
          final RoomImage _item;
          _item = new RoomImage();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpPath;
          if (_stmt.isNull(_columnIndexOfPath)) {
            _tmpPath = null;
          } else {
            _tmpPath = _stmt.getText(_columnIndexOfPath);
          }
          _item.setPath(_tmpPath);
          final long _tmpSize;
          _tmpSize = _stmt.getLong(_columnIndexOfSize);
          _item.setSize(_tmpSize);
          final int _tmpWidth;
          _tmpWidth = (int) (_stmt.getLong(_columnIndexOfWidth));
          _item.setWidth(_tmpWidth);
          final int _tmpHeight;
          _tmpHeight = (int) (_stmt.getLong(_columnIndexOfHeight));
          _item.setHeight(_tmpHeight);
          final long _tmpDateTaken;
          _tmpDateTaken = _stmt.getLong(_columnIndexOfDateTaken);
          _item.setDateTaken(_tmpDateTaken);
          final long _tmpDateModified;
          _tmpDateModified = _stmt.getLong(_columnIndexOfDateModified);
          _item.setDateModified(_tmpDateModified);
          final boolean _tmpIsLiked;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsLiked));
          _tmpIsLiked = _tmp != 0;
          _item.setLiked(_tmpIsLiked);
          final String _tmpThumbnailPath;
          if (_stmt.isNull(_columnIndexOfThumbnailPath)) {
            _tmpThumbnailPath = null;
          } else {
            _tmpThumbnailPath = _stmt.getText(_columnIndexOfThumbnailPath);
          }
          _item.setThumbnailPath(_tmpThumbnailPath);
          final String _tmpMimeType;
          if (_stmt.isNull(_columnIndexOfMimeType)) {
            _tmpMimeType = null;
          } else {
            _tmpMimeType = _stmt.getText(_columnIndexOfMimeType);
          }
          _item.setMimeType(_tmpMimeType);
          final String _tmpDetectedObjects;
          if (_stmt.isNull(_columnIndexOfDetectedObjects)) {
            _tmpDetectedObjects = null;
          } else {
            _tmpDetectedObjects = _stmt.getText(_columnIndexOfDetectedObjects);
          }
          _item.setDetectedObjects(_tmpDetectedObjects);
          final boolean _tmpIsProcessed;
          final int _tmp_1;
          _tmp_1 = (int) (_stmt.getLong(_columnIndexOfIsProcessed));
          _tmpIsProcessed = _tmp_1 != 0;
          _item.setProcessed(_tmpIsProcessed);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public void deleteImagesByIds(final List<Integer> ids) {
    final StringBuilder _stringBuilder = new StringBuilder();
    _stringBuilder.append("DELETE FROM room_images WHERE id IN (");
    final int _inputSize = ids == null ? 1 : ids.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (ids == null) {
          _stmt.bindNull(_argIndex);
        } else {
          for (Integer _item : ids) {
            if (_item == null) {
              _stmt.bindNull(_argIndex);
            } else {
              _stmt.bindLong(_argIndex, _item);
            }
            _argIndex++;
          }
        }
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public void deleteImageByPath(final String path) {
    final String _sql = "DELETE FROM room_images WHERE path = ?";
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (path == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, path);
        }
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public void updateLikeStatus(final int id, final boolean isLiked) {
    final String _sql = "UPDATE room_images SET is_liked = ? WHERE id = ?";
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        final int _tmp = isLiked ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public void updateDetectedObjects(final int id, final String detectedObjects) {
    final String _sql = "UPDATE room_images SET detected_objects = ?, is_processed = 1 WHERE id = ?";
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (detectedObjects == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, detectedObjects);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public void updateThumbnailPath(final int id, final String thumbnailPath) {
    final String _sql = "UPDATE room_images SET thumbnail_path = ? WHERE id = ?";
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (thumbnailPath == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, thumbnailPath);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public void clearAll() {
    final String _sql = "DELETE FROM room_images";
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
