package com.example.androidtraining.course.viewlayout;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;

public class ViewLayoutActivity extends BaseActivity {
    public static final String TAG = ViewLayoutActivity.class.getSimpleName();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_view_layout;
        super.onCreate(savedInstanceState);

        Button btn1 = findViewById(R.id.btn1);
        btn1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Logger.d(TAG, "Button 1 clicked");
            }
        });
        Button btn2 = findViewById(R.id.btn2);
        btn2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Logger.d(TAG, "Button 2 clicked");
            }
        });

//        FrameLayout frameLayout = findViewById(R.id.frame_layout);
//        frameLayout.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                Logger.d(TAG, "frameLayout clicked");
//            }
//        });

        TextView tvFrame = findViewById(R.id.tv_frame);
        Button btnFrame = findViewById(R.id.btn_frame);
//        Button btnFrame = findViewById(R.id.btn1);
        tvFrame.setOnClickListener((v) -> {
            Logger.d(TAG, "textViewFrame clicked");
        });
        btnFrame.setOnClickListener((v) -> {
            Logger.d(TAG, "buttonViewFrame clicked");
        });

        Button btnInclude = findViewById(R.id.btn_include);
        btnInclude.setOnClickListener((v) -> {
            Logger.d(TAG, "include button clicked");
        });
//        Looper
//        Handler handler = new Handler();
//        handler.sendMessage(new Message());
//        handler.post(new Runnable() {
//            @Override
//            public void run() {
//                getResources().getDrawable()
//            }
//        });
    }
}