package com.example.androidtraining.course.activityfragment;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;

public class BottomFragment extends Fragment {
    private final String TAG = BottomFragment.class.getSimpleName();

    TextView mTextView;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Logger.d(TAG, "onCreateView: ");
        View view = inflater.inflate(R.layout.fragment_bottom, container, false);
        mTextView = view.findViewById(R.id.tv_show_content);
        return view;
    }

    public void updateText(String text) {
        if (mTextView != null) mTextView.setText(text);
    }

    @Override
    public void onAttach(@NonNull Context context) {
        Logger.d(TAG, "onAttach: ");
        super.onAttach(context);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate: ");
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        Logger.d(TAG, "onActivityCreated: ");
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Logger.d(TAG, "onViewCreated: ");
    }

    @Override
    public void onStart() {
        super.onStart();
        Logger.d(TAG, "onStart: ");
    }

    @Override
    public void onResume() {
        super.onResume();
        Logger.d(TAG, "onResume: ");
    }

    @Override
    public void onPause() {
        super.onPause();
        Logger.d(TAG, "onPause: ");
    }

    @Override
    public void onStop() {
        super.onStop();
        Logger.d(TAG, "onStop: ");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Logger.d(TAG, "onDestroyView: ");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy: ");
    }

    @Override
    public void onDetach() {
        super.onDetach();
        Logger.d(TAG, "onDetach: ");
    }
}
