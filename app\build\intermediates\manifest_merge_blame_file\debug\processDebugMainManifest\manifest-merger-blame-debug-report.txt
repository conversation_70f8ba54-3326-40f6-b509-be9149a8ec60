1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.androidtraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Storage permissions: API < 30 -->
11-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:5-77
11-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:5-80
12-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Storage permissions: API < 30 - MANAGE_EXTERNAL_STORAGE & request "All files access" permission -->
13-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:5-81
13-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:8:5-10:40
14-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:9:9-66
15
16    <!-- Alarm service -->
17    <uses-permission
17-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:5-14:38
18        android:name="android.permission.SCHEDULE_EXACT_ALARM"
18-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:22-76
19        android:maxSdkVersion="32" />
19-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:14:9-35
20    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
20-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:15:5-74
20-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:15:22-71
21    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
21-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:16:5-78
21-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:16:22-75
22
23    <permission android:name="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />
23-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:5-113
23-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:17-110
24
25    <!-- API 33+ -->
26    <!-- Foreground Service: need foregroundServiceType & permission: FOREGROUND_SERVICE & FOREGROUND_SERVICE_SPECIAL_USE -->
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
27-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:5-77
27-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:22-74
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
28-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:23:5-89
28-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:23:22-86
29    <uses-permission android:name="android.permission.INTERNET" />
29-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
29-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:22-64
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
30-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
31    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
31-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
31-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
32
33    <permission
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:25:5-154:19
40        android:name="com.example.androidtraining.app.MainApp"
40-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:26:9-36
41        android:allowBackup="true"
41-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:27:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:28:9-65
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:fullBackupContent="@xml/backup_rules"
46-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:29:9-54
47        android:icon="@mipmap/ic_launcher"
47-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:30:9-43
48        android:label="@string/app_name"
48-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:31:9-41
49        android:roundIcon="@mipmap/ic_launcher_round"
49-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:32:9-54
50        android:supportsRtl="true"
50-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:33:9-35
51        android:theme="@style/Theme.AndroidTraining" >
51-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:34:9-53
52        <activity
52-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:37:9-46:20
53            android:name="com.example.androidtraining.MainActivity"
53-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:38:13-41
54            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
54-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:39:13-88
55            android:exported="true" >
55-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:40:13-36
56            <intent-filter>
56-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:41:13-45:29
57                <action android:name="android.intent.action.MAIN" />
57-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:17-69
57-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:17-77
59-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:27-74
60            </intent-filter>
61        </activity>
62        <activity
62-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:48:9-62:20
63            android:name="com.example.androidtraining.course.CourseActivity"
63-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:49:13-50
64            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
64-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:50:13-88
65            android:exported="true"
65-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:51:13-36
66            android:launchMode="singleInstance" >
66-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:52:13-48
67
68            <!-- <intent-filter> -->
69            <!-- <action android:name="android.intent.action.MAIN" /> -->
70            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
71            <!-- </intent-filter> -->
72
73            <meta-data
73-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:59:13-61:36
74                android:name="android.app.lib_name"
74-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:60:17-52
75                android:value="" />
75-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:61:17-33
76        </activity>
77        <activity
77-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:64:9-67:40
78            android:name="com.example.androidtraining.course.alarmservice.SnoozeActivity"
78-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:65:13-63
79            android:exported="false"
79-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:67:13-37
80            android:launchMode="singleInstance" />
80-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:66:13-48
81        <activity
81-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:69:9-71:40
82            android:name="com.example.androidtraining.course.alarmservice.AlarmActivity"
82-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:70:13-62
83            android:exported="false" />
83-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:71:13-37
84        <activity
84-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:73:9-75:40
85            android:name="com.example.androidtraining.course.viewlayout.ViewLayoutActivity"
85-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:74:13-65
86            android:exported="false" />
86-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:75:13-37
87        <activity
87-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:77:9-79:39
88            android:name="com.example.androidtraining.course.activityfragment.ExampleActivity"
88-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:78:13-68
89            android:exported="true" />
89-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:79:13-36
90        <activity
90-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:80:9-82:40
91            android:name="com.example.androidtraining.course.adapter.AdapterActivity"
91-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:81:13-59
92            android:exported="false" />
92-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:82:13-37
93        <activity
93-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:83:9-85:40
94            android:name="com.example.androidtraining.course.broadcastreceiver.BroadcastActivity"
94-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:84:13-71
95            android:exported="false" />
95-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:85:13-37
96        <activity
96-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:86:9-88:40
97            android:name="com.example.androidtraining.course.notification.NotificationActivity"
97-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:87:13-69
98            android:exported="false" />
98-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:88:13-37
99        <activity
99-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:89:9-91:40
100            android:name="com.example.androidtraining.course.service.ServiceActivity"
100-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:90:13-59
101            android:exported="false" />
101-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:91:13-37
102        <activity
102-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:92:9-94:40
103            android:name="com.example.androidtraining.course.shareprefs.SharePrefsActivity"
103-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:93:13-65
104            android:exported="false" />
104-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:94:13-37
105        <activity
105-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:95:9-97:40
106            android:name="com.example.androidtraining.course.database.DatabaseActivity"
106-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:96:13-61
107            android:exported="false" />
107-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:97:13-37
108        <activity
108-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:98:9-100:40
109            android:name="com.example.androidtraining.course.threadhandler.ThreadActivity"
109-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:99:13-64
110            android:exported="false" />
110-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:100:13-37
111
112        <receiver
112-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:102:9-116:20
113            android:name="com.example.androidtraining.course.broadcastreceiver.StaticBroadcastReceiver"
113-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:103:13-77
114            android:enabled="true"
114-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:104:13-35
115            android:exported="true" >
115-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:105:13-36
116            <intent-filter>
116-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:106:13-115:29
117
118                <!-- system action -->
119                <action android:name="android.intent.action.AIRPLANE_MODE" />
119-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:108:17-78
119-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:108:25-75
120                <action android:name="android.intent.action.SCREEN_ON" />
120-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:109:17-74
120-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:109:25-71
121                <action android:name="android.intent.action.SCREEN_OFF" />
121-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:110:17-75
121-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:110:25-72
122                <!-- user define action -->
123                <action android:name="com.example.androidtraining.USER_ACTION" />
123-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:112:17-82
123-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:112:25-79
124                <action android:name="com.example.androidtraining.NEW_ACTION" />
124-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:17-81
124-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:25-78
125                <action android:name="com.example.androidtraining.LOCAL_ACTION" />
125-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:17-83
125-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:25-80
126            </intent-filter>
127        </receiver>
128        <receiver
128-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:118:9-125:20
129            android:name="com.example.androidtraining.course.alarmservice.AlarmReceiver"
129-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:119:13-62
130            android:exported="true" >
130-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:120:13-36
131            <intent-filter>
131-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:121:13-124:29
132                <action android:name="com.example.androidtraining.ALARM_ACTION" />
132-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:122:17-83
132-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:122:25-80
133                <action android:name="android.intent.action.BOOT_COMPLETED" />
133-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:17-79
133-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:25-76
134            </intent-filter>
135        </receiver>
136
137        <service android:name="com.example.androidtraining.course.service.BackgroundService" />
137-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:127:9-69
137-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:127:18-66
138        <service
138-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:129:9-131:58
139            android:name="com.example.androidtraining.course.service.ForegroundService"
139-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:130:13-61
140            android:foregroundServiceType="specialUse" />
140-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:131:13-55
141        <service
141-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:133:9-135:39
142            android:name="com.example.androidtraining.course.service.BoundedServiceLocal"
142-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:134:13-63
143            android:exported="true" />
143-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:135:13-36
144        <service
144-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:136:9-139:41
145            android:name="com.example.androidtraining.course.service.BoundedServiceMessenger"
145-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:137:13-67
146            android:exported="true"
146-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:138:13-36
147            android:process=":remote" />
147-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:139:13-38
148        <service
148-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:140:9-143:41
149            android:name="com.example.androidtraining.course.service.BoundedServiceAIDLBasic"
149-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:141:13-67
150            android:exported="true"
150-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:142:13-36
151            android:process=":remote" />
151-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:143:13-38
152        <service
152-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:144:9-146:39
153            android:name="com.example.androidtraining.course.service.BoundedServiceAidlObject"
153-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:145:13-68
154            android:exported="true" />
154-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:146:13-36
155
156        <provider
156-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:148:9-153:115
157            android:name="com.example.androidtraining.course.database.AppDbContentProvider"
157-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:149:13-65
158            android:authorities="com.example.androidtraining.provider.AppDbContentProvider"
158-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:150:13-92
159            android:enabled="true"
159-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:151:13-35
160            android:exported="true"
160-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:152:13-36
161            android:permission="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />
161-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:153:13-112
162
163        <service
163-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
164            android:name="com.google.firebase.components.ComponentDiscoveryService"
164-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
165            android:directBootAware="true"
165-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
166            android:exported="false" >
166-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
167            <meta-data
167-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
168                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
168-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
170            <meta-data
170-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
171                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
171-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
173            <meta-data
173-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
174                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
174-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
176            <meta-data
176-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
177                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
177-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
179            <meta-data
179-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
180                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
180-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
182            <meta-data
182-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
183                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
183-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
185            <meta-data
185-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
186                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
186-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
188        </service>
189
190        <activity
190-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
191            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
191-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
192            android:excludeFromRecents="true"
192-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
193            android:exported="true"
193-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
194            android:launchMode="singleTask"
194-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
195            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
196            <intent-filter>
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
197                <action android:name="android.intent.action.VIEW" />
197-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
197-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
198
199                <category android:name="android.intent.category.DEFAULT" />
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
200                <category android:name="android.intent.category.BROWSABLE" />
200-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
200-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
201
202                <data
202-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
203                    android:host="firebase.auth"
203-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
204                    android:path="/"
204-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
205                    android:scheme="genericidp" />
205-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
206            </intent-filter>
207        </activity>
208        <activity
208-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
209            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
209-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
210            android:excludeFromRecents="true"
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
211            android:exported="true"
211-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
212            android:launchMode="singleTask"
212-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
213            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
214            <intent-filter>
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
215                <action android:name="android.intent.action.VIEW" />
215-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
215-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
216
217                <category android:name="android.intent.category.DEFAULT" />
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
218                <category android:name="android.intent.category.BROWSABLE" />
218-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
218-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
219
220                <data
220-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
221                    android:host="firebase.auth"
221-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
222                    android:path="/"
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
223                    android:scheme="recaptcha" />
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
224            </intent-filter>
225        </activity>
226
227        <service
227-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
228            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
228-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
229            android:enabled="true"
229-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
230            android:exported="false" >
230-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
231            <meta-data
231-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
232                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
232-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
233                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
233-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
234        </service>
235
236        <activity
236-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
237            android:name="androidx.credentials.playservices.HiddenActivity"
237-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
238            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
238-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
239            android:enabled="true"
239-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
240            android:exported="false"
240-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
241            android:fitsSystemWindows="true"
241-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
242            android:theme="@style/Theme.Hidden" >
242-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
243        </activity>
244        <activity
244-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
245            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
245-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
246            android:excludeFromRecents="true"
246-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
247            android:exported="false"
247-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
248            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
248-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
249        <!--
250            Service handling Google Sign-In user revocation. For apps that do not integrate with
251            Google Sign-In, this service will never be started.
252        -->
253        <service
253-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
254            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
254-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
255            android:exported="true"
255-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
256            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
256-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
257            android:visibleToInstantApps="true" />
257-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
258
259        <provider
259-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
260            android:name="com.google.firebase.provider.FirebaseInitProvider"
260-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
261            android:authorities="com.example.androidtraining.firebaseinitprovider"
261-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
262            android:directBootAware="true"
262-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
263            android:exported="false"
263-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
264            android:initOrder="100" />
264-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
265
266        <activity
266-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
267            android:name="com.google.android.gms.common.api.GoogleApiActivity"
267-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
268            android:exported="false"
268-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
269            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
269-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
270
271        <service
271-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:24:9-28:63
272            android:name="androidx.room.MultiInstanceInvalidationService"
272-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:25:13-74
273            android:directBootAware="true"
273-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:26:13-43
274            android:exported="false" />
274-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:27:13-37
275
276        <provider
276-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
277            android:name="androidx.startup.InitializationProvider"
277-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
278            android:authorities="com.example.androidtraining.androidx-startup"
278-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
279            android:exported="false" >
279-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
280            <meta-data
280-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
281                android:name="androidx.emoji2.text.EmojiCompatInitializer"
281-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
282                android:value="androidx.startup" />
282-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
283            <meta-data
283-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
284                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
284-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
285                android:value="androidx.startup" />
285-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
286            <meta-data
286-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
287                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
287-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
288                android:value="androidx.startup" />
288-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
289        </provider>
290
291        <meta-data
291-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
292            android:name="com.google.android.gms.version"
292-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
293            android:value="@integer/google_play_services_version" />
293-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
294
295        <receiver
295-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
296            android:name="androidx.profileinstaller.ProfileInstallReceiver"
296-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
297            android:directBootAware="false"
297-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
298            android:enabled="true"
298-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
299            android:exported="true"
299-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
300            android:permission="android.permission.DUMP" >
300-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
301            <intent-filter>
301-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
302                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
302-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
302-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
303            </intent-filter>
304            <intent-filter>
304-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
305                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
305-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
305-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
306            </intent-filter>
307            <intent-filter>
307-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
308                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
308-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
308-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
309            </intent-filter>
310            <intent-filter>
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
311                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
311-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
311-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
312            </intent-filter>
313        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
314        <activity
314-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
315            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
315-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
316            android:exported="false"
316-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
317            android:stateNotNeeded="true"
317-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
318            android:theme="@style/Theme.PlayCore.Transparent" />
318-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
319    </application>
320
321</manifest>
