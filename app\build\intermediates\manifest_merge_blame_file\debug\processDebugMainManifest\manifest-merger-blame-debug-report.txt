1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.androidtraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Storage permissions: API < 30 -->
11-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:5-77
11-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:5-80
12-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Storage permissions: API < 30 - MANAGE_EXTERNAL_STORAGE & request "All files access" permission -->
13-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:5-81
13-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:8:5-10:40
14-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:9:9-66
15
16    <!-- Image & Video & Audio -->
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:5-76
17-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:22-73
18    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
18-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:14:5-75
18-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:14:22-72
19
20    <!-- Alarm service -->
21    <uses-permission
21-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:17:5-18:38
22        android:name="android.permission.SCHEDULE_EXACT_ALARM"
22-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:17:22-76
23        android:maxSdkVersion="32" />
23-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:9-35
24    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
24-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:19:5-74
24-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:19:22-71
25    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
25-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:20:5-78
25-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:20:22-75
26
27    <permission android:name="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />
27-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:5-113
27-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:17-110
28
29    <!-- API 33+ -->
30    <!-- Foreground Service: need foregroundServiceType & permission: FOREGROUND_SERVICE & FOREGROUND_SERVICE_SPECIAL_USE -->
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:26:5-77
31-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:26:22-74
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
32-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:27:5-89
32-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:27:22-86
33
34    <!-- For remote config -->
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:7:5-79
35-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:7:22-76
36    <uses-permission android:name="android.permission.INTERNET" />
36-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:8:5-67
36-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:8:22-64
37    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
37-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
37-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
38
39    <permission
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
40        android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
44
45    <application
45-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:29:5-165:19
46        android:name="com.example.androidtraining.app.MainApp"
46-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:30:9-36
47        android:allowBackup="true"
47-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:31:9-35
48        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
48-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
49        android:dataExtractionRules="@xml/data_extraction_rules"
49-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:32:9-65
50        android:debuggable="true"
51        android:extractNativeLibs="false"
52        android:fullBackupContent="@xml/backup_rules"
52-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:33:9-54
53        android:icon="@mipmap/ic_launcher"
53-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:34:9-43
54        android:label="@string/app_name"
54-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:35:9-41
55        android:roundIcon="@mipmap/ic_launcher_round"
55-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:36:9-54
56        android:supportsRtl="true"
56-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:37:9-35
57        android:testOnly="true"
58        android:theme="@style/Theme.AndroidTraining" >
58-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:38:9-53
59        <activity
59-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:41:9-50:20
60            android:name="com.example.androidtraining.MainActivity"
60-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:13-41
61            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
61-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:43:13-88
62            android:exported="true" >
62-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:13-36
63            <intent-filter>
63-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:45:13-49:29
64                <action android:name="android.intent.action.MAIN" />
64-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:46:17-69
64-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:46:25-66
65
66                <category android:name="android.intent.category.LAUNCHER" />
66-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:48:17-77
66-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:48:27-74
67            </intent-filter>
68        </activity>
69        <activity
69-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:52:9-66:20
70            android:name="com.example.androidtraining.course.CourseActivity"
70-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:53:13-50
71            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
71-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:54:13-88
72            android:exported="true"
72-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:55:13-36
73            android:launchMode="singleInstance" >
73-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:56:13-48
74
75            <!-- <intent-filter> -->
76            <!-- <action android:name="android.intent.action.MAIN" /> -->
77            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
78            <!-- </intent-filter> -->
79
80            <meta-data
80-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:63:13-65:36
81                android:name="android.app.lib_name"
81-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:64:17-52
82                android:value="" />
82-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:65:17-33
83        </activity>
84        <activity
84-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:68:9-71:40
85            android:name="com.example.androidtraining.course.alarmservice.SnoozeActivity"
85-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:69:13-63
86            android:exported="false"
86-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:71:13-37
87            android:launchMode="singleInstance" />
87-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:70:13-48
88        <activity
88-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:73:9-75:40
89            android:name="com.example.androidtraining.course.alarmservice.AlarmActivity"
89-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:74:13-62
90            android:exported="false" />
90-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:75:13-37
91        <activity
91-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:76:9-79:40
92            android:name="com.example.androidtraining.course.multithread.GalleryActivity"
92-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:77:13-63
93            android:exported="false"
93-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:79:13-37
94            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
94-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:78:13-69
95        <activity
95-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:80:9-83:40
96            android:name="com.example.androidtraining.course.multithread.ImageDetailActivity"
96-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:81:13-67
97            android:exported="false"
97-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:83:13-37
98            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
98-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:82:13-69
99        <activity
99-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:84:9-86:40
100            android:name="com.example.androidtraining.course.viewlayout.ViewLayoutActivity"
100-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:85:13-65
101            android:exported="false" />
101-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:86:13-37
102        <activity
102-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:88:9-90:39
103            android:name="com.example.androidtraining.course.activityfragment.ExampleActivity"
103-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:89:13-68
104            android:exported="true" />
104-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:90:13-36
105        <activity
105-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:91:9-93:40
106            android:name="com.example.androidtraining.course.adapter.AdapterActivity"
106-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:92:13-59
107            android:exported="false" />
107-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:93:13-37
108        <activity
108-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:94:9-96:40
109            android:name="com.example.androidtraining.course.broadcastreceiver.BroadcastActivity"
109-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:95:13-71
110            android:exported="false" />
110-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:96:13-37
111        <activity
111-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:97:9-99:40
112            android:name="com.example.androidtraining.course.notification.NotificationActivity"
112-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:98:13-69
113            android:exported="false" />
113-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:99:13-37
114        <activity
114-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:100:9-102:40
115            android:name="com.example.androidtraining.course.service.ServiceActivity"
115-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:101:13-59
116            android:exported="false" />
116-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:102:13-37
117        <activity
117-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:103:9-105:40
118            android:name="com.example.androidtraining.course.shareprefs.SharePrefsActivity"
118-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:104:13-65
119            android:exported="false" />
119-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:105:13-37
120        <activity
120-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:106:9-108:40
121            android:name="com.example.androidtraining.course.database.DatabaseActivity"
121-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:107:13-61
122            android:exported="false" />
122-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:108:13-37
123        <activity
123-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:109:9-111:40
124            android:name="com.example.androidtraining.course.threadhandler.ThreadActivity"
124-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:110:13-64
125            android:exported="false" />
125-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:111:13-37
126
127        <receiver
127-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:9-127:20
128            android:name="com.example.androidtraining.course.broadcastreceiver.StaticBroadcastReceiver"
128-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:13-77
129            android:enabled="true"
129-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:115:13-35
130            android:exported="true" >
130-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:116:13-36
131            <intent-filter>
131-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:117:13-126:29
132
133                <!-- system action -->
134                <action android:name="android.intent.action.AIRPLANE_MODE" />
134-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:119:17-78
134-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:119:25-75
135                <action android:name="android.intent.action.SCREEN_ON" />
135-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:120:17-74
135-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:120:25-71
136                <action android:name="android.intent.action.SCREEN_OFF" />
136-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:121:17-75
136-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:121:25-72
137                <!-- user define action -->
138                <action android:name="com.example.androidtraining.USER_ACTION" />
138-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:17-82
138-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:25-79
139                <action android:name="com.example.androidtraining.NEW_ACTION" />
139-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:124:17-81
139-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:124:25-78
140                <action android:name="com.example.androidtraining.LOCAL_ACTION" />
140-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:125:17-83
140-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:125:25-80
141            </intent-filter>
142        </receiver>
143        <receiver
143-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:129:9-136:20
144            android:name="com.example.androidtraining.course.alarmservice.AlarmReceiver"
144-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:130:13-62
145            android:exported="true" >
145-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:131:13-36
146            <intent-filter>
146-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:132:13-135:29
147                <action android:name="com.example.androidtraining.ALARM_ACTION" />
147-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:133:17-83
147-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:133:25-80
148                <action android:name="android.intent.action.BOOT_COMPLETED" />
148-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:134:17-79
148-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:134:25-76
149            </intent-filter>
150        </receiver>
151
152        <service android:name="com.example.androidtraining.course.service.BackgroundService" />
152-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:138:9-69
152-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:138:18-66
153        <service
153-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:140:9-142:58
154            android:name="com.example.androidtraining.course.service.ForegroundService"
154-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:141:13-61
155            android:foregroundServiceType="specialUse" />
155-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:142:13-55
156        <service
156-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:144:9-146:39
157            android:name="com.example.androidtraining.course.service.BoundedServiceLocal"
157-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:145:13-63
158            android:exported="true" />
158-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:146:13-36
159        <service
159-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:147:9-150:41
160            android:name="com.example.androidtraining.course.service.BoundedServiceMessenger"
160-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:148:13-67
161            android:exported="true"
161-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:149:13-36
162            android:process=":remote" />
162-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:150:13-38
163        <service
163-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:151:9-154:41
164            android:name="com.example.androidtraining.course.service.BoundedServiceAIDLBasic"
164-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:152:13-67
165            android:exported="true"
165-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:153:13-36
166            android:process=":remote" />
166-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:154:13-38
167        <service
167-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:155:9-157:39
168            android:name="com.example.androidtraining.course.service.BoundedServiceAidlObject"
168-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:156:13-68
169            android:exported="true" />
169-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:157:13-36
170
171        <provider
171-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:159:9-164:115
172            android:name="com.example.androidtraining.course.database.AppDbContentProvider"
172-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:160:13-65
173            android:authorities="com.example.androidtraining.provider.AppDbContentProvider"
173-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:161:13-92
174            android:enabled="true"
174-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:162:13-35
175            android:exported="true"
175-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:163:13-36
176            android:permission="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />
176-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:164:13-112
177
178        <service
178-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:11:9-17:19
179            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
179-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:12:13-91
180            android:directBootAware="true"
180-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
181            android:exported="false" >
181-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:13:13-37
182            <meta-data
182-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:14:13-16:85
183                android:name="com.google.firebase.components:com.google.mlkit.vision.objects.defaults.internal.DefaultObjectsRegistrar"
183-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:15:17-136
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.mlkit:object-detection:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\c0488df5fb22807dc935e249df35ba40\transformed\jetified-object-detection-17.0.1\AndroidManifest.xml:16:17-82
185            <meta-data
185-->[com.google.mlkit:object-detection-custom:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\3885020533ec105df569ae87cca7ef96\transformed\jetified-object-detection-custom-17.0.1\AndroidManifest.xml:11:13-13:85
186                android:name="com.google.firebase.components:com.google.mlkit.vision.objects.custom.internal.CustomObjectsRegistrar"
186-->[com.google.mlkit:object-detection-custom:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\3885020533ec105df569ae87cca7ef96\transformed\jetified-object-detection-custom-17.0.1\AndroidManifest.xml:12:17-133
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.mlkit:object-detection-custom:17.0.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\3885020533ec105df569ae87cca7ef96\transformed\jetified-object-detection-custom-17.0.1\AndroidManifest.xml:13:17-82
188            <meta-data
188-->[com.google.mlkit:image-labeling-custom:17.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\c7f08c00ab3d06fec488db60fcc03ce8\transformed\jetified-image-labeling-custom-17.0.3\AndroidManifest.xml:11:13-13:85
189                android:name="com.google.firebase.components:com.google.mlkit.vision.label.custom.internal.CustomLabelRegistrar"
189-->[com.google.mlkit:image-labeling-custom:17.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\c7f08c00ab3d06fec488db60fcc03ce8\transformed\jetified-image-labeling-custom-17.0.3\AndroidManifest.xml:12:17-129
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.mlkit:image-labeling-custom:17.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\c7f08c00ab3d06fec488db60fcc03ce8\transformed\jetified-image-labeling-custom-17.0.3\AndroidManifest.xml:13:17-82
191            <meta-data
191-->[com.google.mlkit:vision-internal-vkp:18.2.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\5d28d288be7e2c3b1a25b6573dc4ac61\transformed\jetified-vision-internal-vkp-18.2.3\AndroidManifest.xml:14:13-16:85
192                android:name="com.google.firebase.components:com.google.mlkit.vision.vkp.VkpRegistrar"
192-->[com.google.mlkit:vision-internal-vkp:18.2.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\5d28d288be7e2c3b1a25b6573dc4ac61\transformed\jetified-vision-internal-vkp-18.2.3\AndroidManifest.xml:15:17-103
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.mlkit:vision-internal-vkp:18.2.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\5d28d288be7e2c3b1a25b6573dc4ac61\transformed\jetified-vision-internal-vkp-18.2.3\AndroidManifest.xml:16:17-82
194            <meta-data
194-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ece6cc358cb820c3ee51d8ff156815f0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
195                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
195-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ece6cc358cb820c3ee51d8ff156815f0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ece6cc358cb820c3ee51d8ff156815f0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
197            <meta-data
197-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
198                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
198-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
200        </service>
201
202        <provider
202-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
203            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
203-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
204            android:authorities="com.example.androidtraining.mlkitinitprovider"
204-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
205            android:exported="false"
205-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
206            android:initOrder="99" />
206-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\235d377c2f376e6f82957c321ff27e39\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
207
208        <service
208-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
209            android:name="com.google.firebase.components.ComponentDiscoveryService"
209-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
210            android:directBootAware="true"
210-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
211            android:exported="false" >
211-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
212            <meta-data
212-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
213                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
213-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
215            <meta-data
215-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
216                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
218            <meta-data
218-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
219                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
219-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
221            <meta-data
221-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
222                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
222-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
223                android:value="com.google.firebase.components.ComponentRegistrar" />
223-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
224            <meta-data
224-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
225                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
225-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
226                android:value="com.google.firebase.components.ComponentRegistrar" />
226-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
227            <meta-data
227-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
228                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
228-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
229                android:value="com.google.firebase.components.ComponentRegistrar" />
229-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
230            <meta-data
230-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
231                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
231-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
232                android:value="com.google.firebase.components.ComponentRegistrar" />
232-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
233        </service>
234
235        <activity
235-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
236            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
236-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
237            android:excludeFromRecents="true"
237-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
238            android:exported="true"
238-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
239            android:launchMode="singleTask"
239-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
240            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
240-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
241            <intent-filter>
241-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
242                <action android:name="android.intent.action.VIEW" />
242-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
242-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
243
244                <category android:name="android.intent.category.DEFAULT" />
244-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
244-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
245                <category android:name="android.intent.category.BROWSABLE" />
245-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
245-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
246
247                <data
247-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
248                    android:host="firebase.auth"
248-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
249                    android:path="/"
249-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
250                    android:scheme="genericidp" />
250-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
251            </intent-filter>
252        </activity>
253        <activity
253-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
254            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
254-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
255            android:excludeFromRecents="true"
255-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
256            android:exported="true"
256-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
257            android:launchMode="singleTask"
257-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
258            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
258-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
259            <intent-filter>
259-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
260                <action android:name="android.intent.action.VIEW" />
260-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
260-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
261
262                <category android:name="android.intent.category.DEFAULT" />
262-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
262-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
263                <category android:name="android.intent.category.BROWSABLE" />
263-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
263-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
264
265                <data
265-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
266                    android:host="firebase.auth"
266-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
267                    android:path="/"
267-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
268                    android:scheme="recaptcha" />
268-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
269            </intent-filter>
270        </activity>
271
272        <service
272-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
273            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
273-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
274            android:enabled="true"
274-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
275            android:exported="false" >
275-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
276            <meta-data
276-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
277                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
277-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
278                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
278-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
279        </service>
280
281        <activity
281-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
282            android:name="androidx.credentials.playservices.HiddenActivity"
282-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
283            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
283-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
284            android:enabled="true"
284-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
285            android:exported="false"
285-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
286            android:fitsSystemWindows="true"
286-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
287            android:theme="@style/Theme.Hidden" >
287-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
288        </activity>
289        <activity
289-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
290            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
290-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
291            android:excludeFromRecents="true"
291-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
292            android:exported="false"
292-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
293            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
293-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
294        <!--
295            Service handling Google Sign-In user revocation. For apps that do not integrate with
296            Google Sign-In, this service will never be started.
297        -->
298        <service
298-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
299            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
299-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
300            android:exported="true"
300-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
301            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
301-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
302            android:visibleToInstantApps="true" />
302-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
303
304        <activity
304-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\2aa10d9fde787503bb7891509092037b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
305            android:name="com.google.android.gms.common.api.GoogleApiActivity"
305-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\2aa10d9fde787503bb7891509092037b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
306            android:exported="false"
306-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\2aa10d9fde787503bb7891509092037b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
307            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
307-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\2aa10d9fde787503bb7891509092037b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
308
309        <provider
309-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
310            android:name="com.google.firebase.provider.FirebaseInitProvider"
310-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
311            android:authorities="com.example.androidtraining.firebaseinitprovider"
311-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
312            android:directBootAware="true"
312-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
313            android:exported="false"
313-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
314            android:initOrder="100" />
314-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
315
316        <service
316-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:24:9-28:63
317            android:name="androidx.room.MultiInstanceInvalidationService"
317-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:25:13-74
318            android:directBootAware="true"
318-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:26:13-43
319            android:exported="false" />
319-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:27:13-37
320
321        <provider
321-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
322            android:name="androidx.startup.InitializationProvider"
322-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
323            android:authorities="com.example.androidtraining.androidx-startup"
323-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
324            android:exported="false" >
324-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
325            <meta-data
325-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
326                android:name="androidx.emoji2.text.EmojiCompatInitializer"
326-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
327                android:value="androidx.startup" />
327-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
328            <meta-data
328-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
329                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
329-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
330                android:value="androidx.startup" />
330-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
331            <meta-data
331-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
332                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
332-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
333                android:value="androidx.startup" />
333-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
334        </provider>
335
336        <meta-data
336-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
337            android:name="com.google.android.gms.version"
337-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
338            android:value="@integer/google_play_services_version" />
338-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
339
340        <receiver
340-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
341            android:name="androidx.profileinstaller.ProfileInstallReceiver"
341-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
342            android:directBootAware="false"
342-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
343            android:enabled="true"
343-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
344            android:exported="true"
344-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
345            android:permission="android.permission.DUMP" >
345-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
346            <intent-filter>
346-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
347                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
347-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
347-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
348            </intent-filter>
349            <intent-filter>
349-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
350                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
350-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
350-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
351            </intent-filter>
352            <intent-filter>
352-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
353                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
353-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
353-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
354            </intent-filter>
355            <intent-filter>
355-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
356                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
356-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
356-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
357            </intent-filter>
358        </receiver>
359
360        <service
360-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\8508e5d13d57b1a409717f17c0b955ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
361            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
361-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\8508e5d13d57b1a409717f17c0b955ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
362            android:exported="false" >
362-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\8508e5d13d57b1a409717f17c0b955ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
363            <meta-data
363-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\8508e5d13d57b1a409717f17c0b955ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
364                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
364-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\8508e5d13d57b1a409717f17c0b955ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
365                android:value="cct" />
365-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\8508e5d13d57b1a409717f17c0b955ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
366        </service>
367        <service
367-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11\transforms\518df6bfd4aebe51a95b10f4bc4d0757\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
368            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
368-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11\transforms\518df6bfd4aebe51a95b10f4bc4d0757\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
369            android:exported="false"
369-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11\transforms\518df6bfd4aebe51a95b10f4bc4d0757\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
370            android:permission="android.permission.BIND_JOB_SERVICE" >
370-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11\transforms\518df6bfd4aebe51a95b10f4bc4d0757\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
371        </service>
372
373        <receiver
373-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11\transforms\518df6bfd4aebe51a95b10f4bc4d0757\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
374            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
374-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11\transforms\518df6bfd4aebe51a95b10f4bc4d0757\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
375            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
375-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11\transforms\518df6bfd4aebe51a95b10f4bc4d0757\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
376        <activity
376-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
377            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
377-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
378            android:exported="false"
378-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
379            android:stateNotNeeded="true"
379-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
380            android:theme="@style/Theme.PlayCore.Transparent" />
380-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
381    </application>
382
383</manifest>
