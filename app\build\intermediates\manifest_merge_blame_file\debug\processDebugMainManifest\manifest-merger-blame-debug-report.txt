1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.androidtraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Storage permissions: API < 30 -->
11-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:5-77
11-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:5-80
12-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Storage permissions: API < 30 - MANAGE_EXTERNAL_STORAGE & request "All files access" permission -->
13-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:5-81
13-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:8:5-10:40
14-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:9:9-66
15
16    <!-- Alarm service -->
17    <uses-permission
17-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:5-14:38
18        android:name="android.permission.SCHEDULE_EXACT_ALARM"
18-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:13:22-76
19        android:maxSdkVersion="32" />
19-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:14:9-35
20    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
20-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:15:5-74
20-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:15:22-71
21    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
21-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:16:5-78
21-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:16:22-75
22
23    <permission android:name="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />
23-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:5-113
23-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:18:17-110
24
25    <!-- API 33+ -->
26    <!-- Foreground Service: need foregroundServiceType & permission: FOREGROUND_SERVICE & FOREGROUND_SERVICE_SPECIAL_USE -->
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
27-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:5-77
27-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:22:22-74
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
28-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:23:5-89
28-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:23:22-86
29    <uses-permission android:name="android.permission.INTERNET" />
29-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
29-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:22-64
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
30-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
31    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
31-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
31-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\21d6ab48d648853d8be9b38596fe12f9\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
32
33    <permission
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.example.androidtraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:25:5-158:19
40        android:name="com.example.androidtraining.app.MainApp"
40-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:26:9-36
41        android:allowBackup="true"
41-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:27:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3b1f9487fa18ad75d94f820ca6c18c8a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:28:9-65
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:fullBackupContent="@xml/backup_rules"
46-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:29:9-54
47        android:icon="@mipmap/ic_launcher"
47-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:30:9-43
48        android:label="@string/app_name"
48-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:31:9-41
49        android:roundIcon="@mipmap/ic_launcher_round"
49-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:32:9-54
50        android:supportsRtl="true"
50-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:33:9-35
51        android:testOnly="true"
52        android:theme="@style/Theme.AndroidTraining" >
52-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:34:9-53
53        <activity
53-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:37:9-46:20
54            android:name="com.example.androidtraining.MainActivity"
54-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:38:13-41
55            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
55-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:39:13-88
56            android:exported="true" >
56-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:40:13-36
57            <intent-filter>
57-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:41:13-45:29
58                <action android:name="android.intent.action.MAIN" />
58-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:17-69
58-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:42:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:17-77
60-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:44:27-74
61            </intent-filter>
62        </activity>
63        <activity
63-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:48:9-62:20
64            android:name="com.example.androidtraining.course.CourseActivity"
64-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:49:13-50
65            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
65-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:50:13-88
66            android:exported="true"
66-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:51:13-36
67            android:launchMode="singleInstance" >
67-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:52:13-48
68
69            <!-- <intent-filter> -->
70            <!-- <action android:name="android.intent.action.MAIN" /> -->
71            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
72            <!-- </intent-filter> -->
73
74            <meta-data
74-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:59:13-61:36
75                android:name="android.app.lib_name"
75-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:60:17-52
76                android:value="" />
76-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:61:17-33
77        </activity>
78        <activity
78-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:64:9-67:40
79            android:name="com.example.androidtraining.course.alarmservice.SnoozeActivity"
79-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:65:13-63
80            android:exported="false"
80-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:67:13-37
81            android:launchMode="singleInstance" />
81-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:66:13-48
82        <activity
82-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:69:9-71:40
83            android:name="com.example.androidtraining.course.alarmservice.AlarmActivity"
83-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:70:13-62
84            android:exported="false" />
84-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:71:13-37
85        <activity
85-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:72:9-75:40
86            android:name="com.example.androidtraining.course.multithread.GalleryActivity"
86-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:73:13-63
87            android:exported="false"
87-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:75:13-37
88            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
88-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:74:13-69
89        <activity
89-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:77:9-79:40
90            android:name="com.example.androidtraining.course.viewlayout.ViewLayoutActivity"
90-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:78:13-65
91            android:exported="false" />
91-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:79:13-37
92        <activity
92-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:81:9-83:39
93            android:name="com.example.androidtraining.course.activityfragment.ExampleActivity"
93-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:82:13-68
94            android:exported="true" />
94-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:83:13-36
95        <activity
95-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:84:9-86:40
96            android:name="com.example.androidtraining.course.adapter.AdapterActivity"
96-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:85:13-59
97            android:exported="false" />
97-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:86:13-37
98        <activity
98-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:87:9-89:40
99            android:name="com.example.androidtraining.course.broadcastreceiver.BroadcastActivity"
99-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:88:13-71
100            android:exported="false" />
100-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:89:13-37
101        <activity
101-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:90:9-92:40
102            android:name="com.example.androidtraining.course.notification.NotificationActivity"
102-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:91:13-69
103            android:exported="false" />
103-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:92:13-37
104        <activity
104-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:93:9-95:40
105            android:name="com.example.androidtraining.course.service.ServiceActivity"
105-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:94:13-59
106            android:exported="false" />
106-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:95:13-37
107        <activity
107-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:96:9-98:40
108            android:name="com.example.androidtraining.course.shareprefs.SharePrefsActivity"
108-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:97:13-65
109            android:exported="false" />
109-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:98:13-37
110        <activity
110-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:99:9-101:40
111            android:name="com.example.androidtraining.course.database.DatabaseActivity"
111-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:100:13-61
112            android:exported="false" />
112-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:101:13-37
113        <activity
113-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:102:9-104:40
114            android:name="com.example.androidtraining.course.threadhandler.ThreadActivity"
114-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:103:13-64
115            android:exported="false" />
115-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:104:13-37
116
117        <receiver
117-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:106:9-120:20
118            android:name="com.example.androidtraining.course.broadcastreceiver.StaticBroadcastReceiver"
118-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:107:13-77
119            android:enabled="true"
119-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:108:13-35
120            android:exported="true" >
120-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:109:13-36
121            <intent-filter>
121-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:110:13-119:29
122
123                <!-- system action -->
124                <action android:name="android.intent.action.AIRPLANE_MODE" />
124-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:112:17-78
124-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:112:25-75
125                <action android:name="android.intent.action.SCREEN_ON" />
125-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:17-74
125-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:113:25-71
126                <action android:name="android.intent.action.SCREEN_OFF" />
126-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:17-75
126-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:114:25-72
127                <!-- user define action -->
128                <action android:name="com.example.androidtraining.USER_ACTION" />
128-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:116:17-82
128-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:116:25-79
129                <action android:name="com.example.androidtraining.NEW_ACTION" />
129-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:117:17-81
129-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:117:25-78
130                <action android:name="com.example.androidtraining.LOCAL_ACTION" />
130-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:118:17-83
130-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:118:25-80
131            </intent-filter>
132        </receiver>
133        <receiver
133-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:122:9-129:20
134            android:name="com.example.androidtraining.course.alarmservice.AlarmReceiver"
134-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:123:13-62
135            android:exported="true" >
135-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:124:13-36
136            <intent-filter>
136-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:125:13-128:29
137                <action android:name="com.example.androidtraining.ALARM_ACTION" />
137-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:126:17-83
137-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:126:25-80
138                <action android:name="android.intent.action.BOOT_COMPLETED" />
138-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:127:17-79
138-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:127:25-76
139            </intent-filter>
140        </receiver>
141
142        <service android:name="com.example.androidtraining.course.service.BackgroundService" />
142-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:131:9-69
142-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:131:18-66
143        <service
143-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:133:9-135:58
144            android:name="com.example.androidtraining.course.service.ForegroundService"
144-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:134:13-61
145            android:foregroundServiceType="specialUse" />
145-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:135:13-55
146        <service
146-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:137:9-139:39
147            android:name="com.example.androidtraining.course.service.BoundedServiceLocal"
147-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:138:13-63
148            android:exported="true" />
148-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:139:13-36
149        <service
149-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:140:9-143:41
150            android:name="com.example.androidtraining.course.service.BoundedServiceMessenger"
150-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:141:13-67
151            android:exported="true"
151-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:142:13-36
152            android:process=":remote" />
152-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:143:13-38
153        <service
153-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:144:9-147:41
154            android:name="com.example.androidtraining.course.service.BoundedServiceAIDLBasic"
154-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:145:13-67
155            android:exported="true"
155-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:146:13-36
156            android:process=":remote" />
156-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:147:13-38
157        <service
157-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:148:9-150:39
158            android:name="com.example.androidtraining.course.service.BoundedServiceAidlObject"
158-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:149:13-68
159            android:exported="true" />
159-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:150:13-36
160
161        <provider
161-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:152:9-157:115
162            android:name="com.example.androidtraining.course.database.AppDbContentProvider"
162-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:153:13-65
163            android:authorities="com.example.androidtraining.provider.AppDbContentProvider"
163-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:154:13-92
164            android:enabled="true"
164-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:155:13-35
165            android:exported="true"
165-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:156:13-36
166            android:permission="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />
166-->D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:157:13-112
167
168        <service
168-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
169            android:name="com.google.firebase.components.ComponentDiscoveryService"
169-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
170            android:directBootAware="true"
170-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
171            android:exported="false" >
171-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
172            <meta-data
172-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
173                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
173-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ec8999c05c84629a6ddd9bdc53c60c77\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
175            <meta-data
175-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
176                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
176-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
178            <meta-data
178-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
179                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
179-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\0e31b3e1e4f6ebf5fefea1162fa2ed9c\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
181            <meta-data
181-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
182                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
182-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
184            <meta-data
184-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
185                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
185-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\ab9fe868ab64c099db67b63070ecfa4e\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
187            <meta-data
187-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
188                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
188-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\4342437c0df1dcab5ebea2e8b76aaae4\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
190            <meta-data
190-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
191                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
191-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
193        </service>
194
195        <activity
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
196            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
197            android:excludeFromRecents="true"
197-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
198            android:exported="true"
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
199            android:launchMode="singleTask"
199-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
200            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
200-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
201            <intent-filter>
201-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
202                <action android:name="android.intent.action.VIEW" />
202-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
202-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
203
204                <category android:name="android.intent.category.DEFAULT" />
204-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
204-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
205                <category android:name="android.intent.category.BROWSABLE" />
205-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
205-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
206
207                <data
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
208                    android:host="firebase.auth"
208-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
209                    android:path="/"
209-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
210                    android:scheme="genericidp" />
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
211            </intent-filter>
212        </activity>
213        <activity
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
214            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
215            android:excludeFromRecents="true"
215-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
216            android:exported="true"
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
217            android:launchMode="singleTask"
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
218            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
218-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
219            <intent-filter>
219-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
220                <action android:name="android.intent.action.VIEW" />
220-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
220-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
221
222                <category android:name="android.intent.category.DEFAULT" />
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
223                <category android:name="android.intent.category.BROWSABLE" />
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
224
225                <data
225-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
226                    android:host="firebase.auth"
226-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
227                    android:path="/"
227-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
228                    android:scheme="recaptcha" />
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11\transforms\ca58516c5ce99a3506e86dd8b41d7f9b\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
229            </intent-filter>
230        </activity>
231
232        <service
232-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
233            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
233-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
234            android:enabled="true"
234-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
235            android:exported="false" >
235-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
236            <meta-data
236-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
237                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
237-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
238                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
238-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
239        </service>
240
241        <activity
241-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
242            android:name="androidx.credentials.playservices.HiddenActivity"
242-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
243            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
243-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
244            android:enabled="true"
244-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
245            android:exported="false"
245-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
246            android:fitsSystemWindows="true"
246-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
247            android:theme="@style/Theme.Hidden" >
247-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11\transforms\d0b1a5acd10238845af96f34eeb48e42\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
248        </activity>
249        <activity
249-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
250            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
250-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
251            android:excludeFromRecents="true"
251-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
252            android:exported="false"
252-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
253            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
253-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
254        <!--
255            Service handling Google Sign-In user revocation. For apps that do not integrate with
256            Google Sign-In, this service will never be started.
257        -->
258        <service
258-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
259            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
259-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
260            android:exported="true"
260-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
261            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
261-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
262            android:visibleToInstantApps="true" />
262-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\9790152a47768f0f21896ee79a74cfb9\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
263
264        <provider
264-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
265            android:name="com.google.firebase.provider.FirebaseInitProvider"
265-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
266            android:authorities="com.example.androidtraining.firebaseinitprovider"
266-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
267            android:directBootAware="true"
267-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
268            android:exported="false"
268-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
269            android:initOrder="100" />
269-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\e85eeefff7ebd52565d9d0ba7ee29f53\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
270
271        <activity
271-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
272            android:name="com.google.android.gms.common.api.GoogleApiActivity"
272-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
273            android:exported="false"
273-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
274            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
274-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\3d88b529cd1e40150c2d2dd3336cc0f9\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
275
276        <service
276-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:24:9-28:63
277            android:name="androidx.room.MultiInstanceInvalidationService"
277-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:25:13-74
278            android:directBootAware="true"
278-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:26:13-43
279            android:exported="false" />
279-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\db7d5ad04d785118a63c7246e794829d\transformed\jetified-room-runtime-release\AndroidManifest.xml:27:13-37
280
281        <provider
281-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
282            android:name="androidx.startup.InitializationProvider"
282-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
283            android:authorities="com.example.androidtraining.androidx-startup"
283-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
284            android:exported="false" >
284-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
285            <meta-data
285-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
286                android:name="androidx.emoji2.text.EmojiCompatInitializer"
286-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
287                android:value="androidx.startup" />
287-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\158a6d054cbdc76eed971da94e712cef\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
288            <meta-data
288-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
289                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
289-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
290                android:value="androidx.startup" />
290-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11\transforms\3a106b182eb1d798bd32cfc2c8131903\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
291            <meta-data
291-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
292                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
292-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
293                android:value="androidx.startup" />
293-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
294        </provider>
295
296        <meta-data
296-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
297            android:name="com.google.android.gms.version"
297-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
298            android:value="@integer/google_play_services_version" />
298-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7ee81ce4615b9e8929af9df6afeffe39\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
299
300        <receiver
300-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
301            android:name="androidx.profileinstaller.ProfileInstallReceiver"
301-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
302            android:directBootAware="false"
302-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
303            android:enabled="true"
303-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
304            android:exported="true"
304-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
305            android:permission="android.permission.DUMP" >
305-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
306            <intent-filter>
306-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
307                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
307-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
307-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
308            </intent-filter>
309            <intent-filter>
309-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
310                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
311            </intent-filter>
312            <intent-filter>
312-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
313                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
313-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
313-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
314            </intent-filter>
315            <intent-filter>
315-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
316                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
316-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
316-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11\transforms\7a295d677ba9ef8fd2372a881f9d4d2c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
317            </intent-filter>
318        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
319        <activity
319-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
320            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
320-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
321            android:exported="false"
321-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
322            android:stateNotNeeded="true"
322-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
323            android:theme="@style/Theme.PlayCore.Transparent" />
323-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11\transforms\ff9a2ad6bc8622b62b2cfee5999b750e\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
324    </application>
325
326</manifest>
