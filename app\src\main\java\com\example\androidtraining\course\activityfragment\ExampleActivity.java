package com.example.androidtraining.course.activityfragment;

import androidx.activity.OnBackPressedCallback;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import android.app.ActivityManager;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.widget.Button;
import android.widget.EditText;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;

import java.util.Iterator;
import java.util.List;

public class ExampleActivity extends BaseActivity implements PassDataToFragmentInterface {
    private static final String TAG = ExampleActivity.class.getSimpleName();

    FragmentManager mFragmentManager;
    TopFragment mTopFragment;
    BottomFragment mBottomFragment;
    OneFragment mOneFragment;
    TwoFragment mTwoFragment;

    EditText mEditText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_example;
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate: ");

        mFragmentManager = getSupportFragmentManager();
        mTopFragment = new TopFragment();
        mBottomFragment = new BottomFragment();
//        mFragmentManager.beginTransaction().replace(R.id.fragment_top, mTopFragment, "TopFragment").commit();
//        mFragmentManager.beginTransaction().replace(R.id.fragment_bottom, mBottomFragment, "BottomFragment").commit();
        mFragmentManager.beginTransaction().replace(R.id.fragment_top, mTopFragment, "TopFragment").addToBackStack("TopFragment").commit();
        mFragmentManager.beginTransaction().replace(R.id.fragment_bottom, mBottomFragment, "BottomFragment").addToBackStack("BottomFragment").commit();

        mEditText = findViewById(R.id.edt_activity);
        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (mBottomFragment != null) mBottomFragment.updateText(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        // Initialize fragments lazily to prevent memory leaks
        // mOneFragment and mTwoFragment will be created when needed
        Button btn1 = findViewById(R.id.btn_show_fragment1);
        btn1.setOnClickListener(v -> {
            Logger.d(TAG, "onCreate: onclick Btn1");
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            // Use existing fragment instance to prevent memory leaks
            if (mOneFragment == null) {
                mOneFragment = new OneFragment();
            }
            ft.replace(R.id.fragment_container_activity, mOneFragment, "OneFragment");
            ft.addToBackStack("OneFragment"); // Add to backstack for proper navigation
            ft.commit();
            checkFragmentBackStack();
        });

        Button btn2 = findViewById(R.id.btn_show_fragment2);
        btn2.setOnClickListener(v -> {
            Logger.d(TAG, "onCreate: onclick Btn2");
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            // Use existing fragment instance to prevent memory leaks
            if (mTwoFragment == null) {
                mTwoFragment = new TwoFragment();
            }
            ft.replace(R.id.fragment_container_activity, mTwoFragment, "TwoFragment");
            ft.addToBackStack("TwoFragment"); // Add to backstack for proper navigation
            ft.commit();
            checkFragmentBackStack();
        });

//        registerForActivityResult();
        getOnBackPressedDispatcher().addCallback(mOnBackPressedCallback);
    }

    private OnBackPressedCallback mOnBackPressedCallback = new OnBackPressedCallback(false) {
        @Override
        public void handleOnBackPressed() {
            Logger.d(TAG, "handleOnBackPressed: " + isEnabled());
            if (isEnabled()) {

            } else {
                getOnBackPressedDispatcher().onBackPressed(); // call super.onBackPressed(); to finish activity
            }
        }
    };

//    @Override
//    public void onBackPressed() {
//        super.onBackPressed();
//        Logger.d(TAG, "onBackPressed: ");
//    }

    void checkFragmentBackStack() {
        int count = mFragmentManager.getBackStackEntryCount();
        Logger.d(TAG, "checkFragmentBackStack - count=" + count);
        for (int i = 0; i < count; i++) {
            FragmentManager.BackStackEntry backStackEntry = mFragmentManager.getBackStackEntryAt(i);
            Logger.d(TAG, "checkFragmentBackStack - idx= " + i + ", name - " + backStackEntry.getName() + " \t- "
                    + backStackEntry.getClass().getSimpleName() + ", id-" + backStackEntry.getId() + ", hashCode: "
                    + mFragmentManager.findFragmentByTag(backStackEntry.getName()));
        }
    }

    void checkActivityStack() {
        ActivityManager am = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> runningTaskInfoList = am.getRunningTasks(10);
//        am.getAppTasks();
        Logger.d(TAG, "checkActivityStack: runningTaskInfoList size: " + runningTaskInfoList.size());
        Iterator<ActivityManager.RunningTaskInfo> iterator = runningTaskInfoList.iterator();
        while (iterator.hasNext()) {
            ActivityManager.RunningTaskInfo runningTaskInfo = (ActivityManager.RunningTaskInfo) iterator.next();
            int id = runningTaskInfo.id;
            CharSequence desc = runningTaskInfo.description;
            int numOfActivity = runningTaskInfo.numActivities;
            String topActivity = runningTaskInfo.topActivity.getShortClassName();
            Logger.d(TAG, "checkActivityStack - id=" + id + ", desc=" + desc + ", numOfActivity=" + numOfActivity + ", topActivity=" + topActivity);
        }
    }

    @Override
    public void onTextChanged(String text) {
        if (mBottomFragment != null) mBottomFragment.updateText(text);
    }

    @Override
    protected void onStart() {
        super.onStart();
        Logger.d(TAG, "onStart: ");
    }

    @Override
    protected void onResume() {
        super.onResume();
        Logger.d(TAG, "onResume: ");
    }

    @Override
    protected void onPause() {
        super.onPause();
        Logger.d(TAG, "onPause: ");
    }

    @Override
    protected void onStop() {
        super.onStop();
        Logger.d(TAG, "onStop: ");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy: ");
    }
}