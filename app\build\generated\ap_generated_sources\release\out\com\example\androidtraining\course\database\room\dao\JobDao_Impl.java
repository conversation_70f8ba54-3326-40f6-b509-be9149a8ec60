package com.example.androidtraining.course.database.room.dao;

import androidx.annotation.NonNull;
import androidx.room.EntityDeleteOrUpdateAdapter;
import androidx.room.EntityInsertAdapter;
import androidx.room.RoomDatabase;
import androidx.room.util.DBUtil;
import androidx.room.util.SQLiteStatementUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.SQLiteStatement;
import com.example.androidtraining.course.database.room.entities.Job;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation", "removal"})
public final class JobDao_Impl implements JobDao {
  private final RoomDatabase __db;

  private final EntityInsertAdapter<Job> __insertAdapterOfJob;

  private final EntityDeleteOrUpdateAdapter<Job> __deleteAdapterOfJob;

  private final EntityDeleteOrUpdateAdapter<Job> __updateAdapterOfJob;

  public JobDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertAdapterOfJob = new EntityInsertAdapter<Job>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `tasks` (`id`,`title`,`engineer_id`) VALUES (nullif(?, 0),?,?)";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final Job entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getTitle() == null) {
          statement.bindNull(2);
        } else {
          statement.bindText(2, entity.getTitle());
        }
        statement.bindLong(3, entity.getEngineerId());
      }
    };
    this.__deleteAdapterOfJob = new EntityDeleteOrUpdateAdapter<Job>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `tasks` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final Job entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfJob = new EntityDeleteOrUpdateAdapter<Job>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `tasks` SET `id` = ?,`title` = ?,`engineer_id` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final Job entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getTitle() == null) {
          statement.bindNull(2);
        } else {
          statement.bindText(2, entity.getTitle());
        }
        statement.bindLong(3, entity.getEngineerId());
        statement.bindLong(4, entity.getId());
      }
    };
  }

  @Override
  public long[] insertJobs(final List<Job> listTask) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      return __insertAdapterOfJob.insertAndReturnIdsArray(_connection, listTask);
    });
  }

  @Override
  public long insert(final Job task) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      return __insertAdapterOfJob.insertAndReturnId(_connection, task);
    });
  }

  @Override
  public int delete(final Job task) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      int _result = 0;
      _result += __deleteAdapterOfJob.handle(_connection, task);
      return _result;
    });
  }

  @Override
  public int update(final Job task) {
    return DBUtil.performBlocking(__db, false, true, (_connection) -> {
      int _result = 0;
      _result += __updateAdapterOfJob.handle(_connection, task);
      return _result;
    });
  }

  @Override
  public List<Job> getAllJobs() {
    final String _sql = "SELECT * FROM tasks";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfTitle = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "title");
        final int _columnIndexOfEngineerId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "engineer_id");
        final List<Job> _result = new ArrayList<Job>();
        while (_stmt.step()) {
          final Job _item;
          _item = new Job();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item.setId(_tmpId);
          final String _tmpTitle;
          if (_stmt.isNull(_columnIndexOfTitle)) {
            _tmpTitle = null;
          } else {
            _tmpTitle = _stmt.getText(_columnIndexOfTitle);
          }
          _item.setTitle(_tmpTitle);
          final int _tmpEngineerId;
          _tmpEngineerId = (int) (_stmt.getLong(_columnIndexOfEngineerId));
          _item.setEngineerId(_tmpEngineerId);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<Job> getListJobByIds(final int[] ids) {
    final StringBuilder _stringBuilder = new StringBuilder();
    _stringBuilder.append("SELECT * FROM tasks WHERE id IN (");
    final int _inputSize = ids == null ? 1 : ids.length;
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (ids == null) {
          _stmt.bindNull(_argIndex);
        } else {
          for (int _item : ids) {
            _stmt.bindLong(_argIndex, _item);
            _argIndex++;
          }
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfTitle = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "title");
        final int _columnIndexOfEngineerId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "engineer_id");
        final List<Job> _result = new ArrayList<Job>();
        while (_stmt.step()) {
          final Job _item_1;
          _item_1 = new Job();
          final int _tmpId;
          _tmpId = (int) (_stmt.getLong(_columnIndexOfId));
          _item_1.setId(_tmpId);
          final String _tmpTitle;
          if (_stmt.isNull(_columnIndexOfTitle)) {
            _tmpTitle = null;
          } else {
            _tmpTitle = _stmt.getText(_columnIndexOfTitle);
          }
          _item_1.setTitle(_tmpTitle);
          final int _tmpEngineerId;
          _tmpEngineerId = (int) (_stmt.getLong(_columnIndexOfEngineerId));
          _item_1.setEngineerId(_tmpEngineerId);
          _result.add(_item_1);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public void deleteAll() {
    final String _sql = "DELETE FROM tasks";
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
