package com.example.androidtraining.course.threadhandler;

import java.util.concurrent.Callable;

public class LongRunningTask implements Callable<OutputObject> {
    final String input;

    public LongRunningTask(String input) {
        this.input = input;
    }

    @Override
    public OutputObject call() throws Exception {
        // handle obj
        OutputObject object = new OutputObject();
        return object;
    }
}
