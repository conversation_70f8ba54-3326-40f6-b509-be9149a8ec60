package com.example.androidtraining.app;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.content.ComponentCallbacks2;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.androidtraining.utils.Logger;

import java.util.List;

public class AppLifecycleHandler implements Application.ActivityLifecycleCallbacks, ComponentCallbacks2 {
    public static final String TAG = AppLifecycleHandler.class.getSimpleName();

    private AppLifecycleInterface appLifecycleInterface;
    private static boolean isAppInForeground = false;

    public AppLifecycleHandler(AppLifecycleInterface lifecycleInterface) {
        appLifecycleInterface = lifecycleInterface;
    }

    // ActivityLifecycleCallbacks
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
//        Logger.d(TAG, "onActivityResumed: " + activity.getClass().getSimpleName() + " is resumed");
        if (!isAppInForeground) {
            isAppInForeground = true;
            if (appLifecycleInterface != null) appLifecycleInterface.onAppForegrounded();
        }
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {
    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
    }

    //    ComponentCallbacks2
    @Override
    public void onTrimMemory(int level) {
        Logger.d(TAG, "onTrimMemory: level=" + level);
        if (level == ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
            isAppInForeground = false;
            if (appLifecycleInterface != null) appLifecycleInterface.onAppBackgrounded();
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {

    }

    @Override
    public void onLowMemory() {

    }

    // way 2: check process
    public static boolean isAppRunning(@NonNull Context context) {
        boolean isAppRunning = false;
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        // since API 21, return only owner app
        List<ActivityManager.RunningAppProcessInfo> listRunningApp =  activityManager.getRunningAppProcesses();
        List<ActivityManager.RunningTaskInfo> listRunningTask = activityManager.getRunningTasks(100);
//        Logger.d(TAG, "isAppRunning: listRunningApp.size: " + listRunningApp.size() + ", listRunningTask.size: " + listRunningTask.size());
        for (ActivityManager.RunningAppProcessInfo runningApp : listRunningApp) {
//            Logger.d(TAG, "isAppRunning: runningApp.processName: " + runningApp.processName + ", importance=" + runningApp.importance);
//            ActivityManager.RunningAppProcessInfo.IMPORTANCE_BACKGROUND = 100, IMPORTANCE_BACKGROUND = IMPORTANCE_CACHED = 400
            if (runningApp.processName.equals(context.getPackageName())) {
                isAppRunning = true;
                break;
            }
        }
        //
        for (ActivityManager.RunningTaskInfo runningTask : listRunningTask) {
//            Logger.d(TAG, "isAppRunning: runningTask: " + runningTask.baseActivity.getClassName() + ", topActivity:" + runningTask.topActivity.getClassName());
            if (runningTask.baseActivity.getPackageName().equals(context.getPackageName()) || runningTask.topActivity.getPackageName().equals(context.getPackageName())) {
                isAppRunning = true;
                break;
            }
        }
        return isAppRunning;
    }

    public static boolean isAppInForeground() {
        return isAppInForeground;
    }
}
