package com.example.androidtraining.app;

import android.app.Application;
import android.content.res.Configuration;

import androidx.annotation.NonNull;

import com.example.androidtraining.utils.Logger;

public class MainApp extends Application implements AppLifecycleInterface {
    public static final String TAG = MainApp.class.getSimpleName();

    private AppLifecycleHandler appLifecycleHandler;

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.d(TAG, "onCreate");
        appLifecycleHandler = new AppLifecycleHandler(this);
        // must register
        registerActivityLifecycleCallbacks(appLifecycleHandler);
        registerComponentCallbacks(appLifecycleHandler);
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Logger.d(TAG, "onLowMemory");
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Logger.d(TAG, "onTrimMemory, level:" + level); // ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN:level 20
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        Logger.d(TAG, "onTerminate");
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Logger.d(TAG, "onConfigurationChanged");
    }

    @Override
    public void onAppForegrounded() {
        Logger.d(TAG, "onAppForegrounded");
    }

    @Override
    public void onAppBackgrounded() {
        Logger.d(TAG, "onAppBackgrounded");
    }
}
