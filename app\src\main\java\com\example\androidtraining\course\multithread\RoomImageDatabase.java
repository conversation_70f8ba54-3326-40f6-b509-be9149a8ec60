package com.example.androidtraining.course.multithread;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

@Database(
    entities = {RoomImage.class},
    version = 1,
    exportSchema = false
)
public abstract class RoomImageDatabase extends RoomDatabase {
    
    public abstract RoomImageDao roomImageDao();
    
    private static volatile RoomImageDatabase INSTANCE;
    private static final String DATABASE_NAME = "room_image_database";
    
    public static RoomImageDatabase getDatabase(final Context context) {
        if (INSTANCE == null) {
            synchronized (RoomImageDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            RoomImageDatabase.class,
                            DATABASE_NAME
                    )
                    .fallbackToDestructiveMigration() // For development only
                     .allowMainThreadQueries() // Don't use this in production
                    .build();
                }
            }
        }
        return INSTANCE;
    }
    
    // Migration example for future use
    static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Example migration - add new column
            // database.execSQL("ALTER TABLE room_images ADD COLUMN new_column TEXT");
        }
    };
    
    // Close database
    public static void closeDatabase() {
        if (INSTANCE != null && INSTANCE.isOpen()) {
            INSTANCE.close();
            INSTANCE = null;
        }
    }
}
