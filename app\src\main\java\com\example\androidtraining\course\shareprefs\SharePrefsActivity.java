package com.example.androidtraining.course.shareprefs;

import android.content.SharedPreferences;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import android.preference.PreferenceManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.R;

public class SharePrefsActivity extends BaseActivity {
    private static final String TAG = SharePrefsActivity.class.getSimpleName();
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        layoutId = R.layout.activity_share_prefs;
        super.onCreate(savedInstanceState);

        // default
        SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(this);
        // name
        SharedPreferences sharedPreferences = getSharedPreferences("my_prefs", MODE_PRIVATE);
        // activity
        SharedPreferences preference = getPreferences(MODE_PRIVATE);

        EditText edtValuePrefs = findViewById(R.id.edt_value_prefs);
        Button btnAddPrefs = findViewById(R.id.btn_add_prefs);
        Button btnGetPrefs = findViewById(R.id.btn_get_prefs);
        TextView tvValuePrefs = findViewById(R.id.tv_value_prefs);

        SharedPreferences editPreference = null;
        editPreference = defaultSharedPreferences;
        editPreference = sharedPreferences;
        editPreference = preference;
        SharedPreferences finalEditPreference = editPreference;
        btnAddPrefs.setOnClickListener(v -> {
            SharedPreferences.Editor editor = finalEditPreference.edit();
            editor.putString("default_value", edtValuePrefs.getText().toString());
            editor.apply();
        });
        btnGetPrefs.setOnClickListener(v -> {
            String value = finalEditPreference.getString("default_value", "null");
            tvValuePrefs.setText("Value: " + value);
        });
    }
}