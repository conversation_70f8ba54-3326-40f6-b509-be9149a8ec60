package com.example.androidtraining.course.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.example.androidtraining.IMsgObjectServiceInterface;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.course.model.MsgObject;


public class BoundedServiceAidlObject extends Service {
    private static final String TAG = BoundedServiceAidlObject.class.getSimpleName();

    private final IMsgObjectServiceInterface.Stub mBinder = new IMsgObjectServiceInterface.Stub() {

        @Override
        public void sendMsg(MsgObject msg) {
            Logger.d(TAG, "sendMsg: " + msg.toString());
        }

        @Override
        public void receiverMsg(MsgObject msg) {
            Logger.d(TAG, "receiverMsg: " + msg.toString());
        }

        @Override
        public MsgObject getMsg() {
            return new MsgObject(10, "New Message from Service");
        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Logger.d(TAG, "onBind: ");
        return mBinder;
    }
}
