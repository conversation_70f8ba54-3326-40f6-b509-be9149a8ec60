package com.example.androidtraining.course.database.sqlite;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import androidx.annotation.Nullable;

import com.example.androidtraining.course.database.DbConstants;
import com.example.androidtraining.utils.Logger;

public class SQLiteDbHelper extends SQLiteOpenHelper {
    String TAG = SQLiteDbHelper.class.getSimpleName();

    public SQLiteDbHelper(@Nullable Context context) {
        super(context, DbConstants.DB_NAME, null, DbConstants.DB_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        String command = "create table " + DbConstants.ENGINEER_TABLE + " (" + DbConstants.ENGINEER_ID_COL +
                " integer primary key autoincrement," + DbConstants.ENGINEER_NAME_COL + " text," +
                DbConstants.ENGINEER_GEN_COL + " integer," + DbConstants.ENGINEER_SINGLE_ID_COL + " text);";
        String command2 = "create table " + DbConstants.TASK_TABLE + " (" + DbConstants.TASK_ID_COL +
                " integer primary key autoincrement," + DbConstants.TASK_TITLE_COL + " text," +
                DbConstants.TASK_ENGINEER_ID_COL + " integer);";
        db.execSQL(command);
        db.execSQL(command2);
        Logger.d(TAG, "onCreate: done");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        String command = "drop table if exists " + DbConstants.ENGINEER_TABLE;
        String command2 = "drop table if exists " + DbConstants.TASK_TABLE;
        db.execSQL(command);
        db.execSQL(command2);
        // recreate the database
        Logger.d(TAG, "onUpgrade: done");
        onCreate(db);
    }
}
