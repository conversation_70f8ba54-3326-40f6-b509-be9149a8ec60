package com.example.androidtraining.course.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.example.androidtraining.utils.Logger;

public class BackgroundService extends Service {
    public static final String TAG = BackgroundService.class.getSimpleName();

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private int mCount = 0;
    private Thread mThread;
    private boolean mIsRunning = false;

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.d(TAG, "onStartCommand: intent = " + intent + " thread=" + Thread.currentThread().getName() + ", isRunning=" + mIsRunning);
        String mess = mIsRunning ? "BackgroundService is running" : "BackgroundService start running";
        Toast.makeText(this, mess, Toast.LENGTH_LONG).show();
        if (!mIsRunning) {
            mThread = new Thread(() -> {
                while (true) {
                    mCount += 1;
                    mIsRunning = true;
                    Logger.d(TAG, "count = " + mCount + " thread=" + Thread.currentThread().getName());
//                    Intent intent1 = new Intent(ACTION_SEND_COUNT");
//                    intent1.putExtra("count", mCount);
//                    sendBroadcast(intent1);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Logger.d(TAG, "thread is interrupted: break - " + e);
                        mIsRunning = false;
                        break;
                    }
                    if (mCount > 300) {
                        mIsRunning = false;
                        break;
                    }
                }
            });
            mThread.start();
        }
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        Logger.d(TAG, "onDestroy count = " + mCount);
        Toast.makeText(this, "Service is killed", Toast.LENGTH_LONG).show();
        if (mThread != null && mThread.isAlive()) {
            try {
                mThread.interrupt();
                Logger.d(TAG, "onDestroy interrupt thread");
            } catch (Exception e) {
                Logger.d(TAG, "onDestroy interrupt ex = " + e);
            }
        }
        super.onDestroy();
    }
}
