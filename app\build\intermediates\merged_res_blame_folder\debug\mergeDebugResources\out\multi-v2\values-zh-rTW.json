{"logs": [{"outputFile": "com.example.androidtraining.app-mergeDebugResources-48:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\3b1f9487fa18ad75d94f820ca6c18c8a\\transformed\\core-1.16.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "40,41,42,43,44,45,46,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3420,3512,3611,3705,3799,3892,3985,11261", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3507,3606,3700,3794,3887,3980,4076,11357"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\368b07b4e9b7912a6e22f44deed31ddf\\transformed\\browser-1.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6174,6449,6541,6642", "endColumns": "83,91,100,92", "endOffsets": "6253,6536,6637,6730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\60115c31cdc3e8b7d77a5b34ad2884ea\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,110", "endOffsets": "156,267"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2863,2969", "endColumns": "105,110", "endOffsets": "2964,3075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\7ee81ce4615b9e8929af9df6afeffe39\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5219", "endColumns": "102", "endOffsets": "5317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\3e3a03410c222d389eb5acdf62f3d148\\transformed\\material-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3144,3206,3273,3343,4081,4175,4282,6258,6309,6371,6735,6795,6855,6933,6994,7052,7108,7168,7226,7280,7365,7421,7479,7533,7598,7690,7764,7836,7918,7992,8069,8189,8252,8315,8414,8491,8565,8615,8666,8732,8795,8863,8934,9005,9066,9137,9204,9266,9353,9432,9497,9580,9665,9739,9803,9879,9927,10000,10064,10140,10218,10280,10344,10407,10473,10553,10631,10707,10786,10840,10895,11043,11118,11191", "endLines": "5,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "292,3139,3201,3268,3338,3415,4170,4277,4350,6304,6366,6444,6790,6850,6928,6989,7047,7103,7163,7221,7275,7360,7416,7474,7528,7593,7685,7759,7831,7913,7987,8064,8184,8247,8310,8409,8486,8560,8610,8661,8727,8790,8858,8929,9000,9061,9132,9199,9261,9348,9427,9492,9575,9660,9734,9798,9874,9922,9995,10059,10135,10213,10275,10339,10402,10468,10548,10626,10702,10781,10835,10890,10959,11113,11186,11256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\2aa10d9fde787503bb7891509092037b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4355,4456,4584,4699,4801,4908,5024,5124,5322,5432,5533,5662,5777,5879,5987,6043,6100", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "4451,4579,4694,4796,4903,5019,5119,5214,5427,5528,5657,5772,5874,5982,6038,6095,6169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11\\transforms\\e7f24da73f275a8a791d04076c131d9f\\transformed\\appcompat-1.7.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,10964", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,11038"}}]}]}