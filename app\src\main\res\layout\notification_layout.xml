<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/noti_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="5dp"
        android:gravity="start"
        android:text="Title"
        />

    <TextView
        android:id="@+id/noti_tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:ellipsize="end"
        tools:background="@color/design_default_color_secondary_variant"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/noti_tv_title"
        app:layout_constraintBottom_toTopOf="@+id/noti_footer"
        tools:text="Content of notification"
        />
    <LinearLayout
        android:id="@+id/noti_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_margin="5dp"
        android:weightSum="3">
        <Button
            android:id="@+id/noti_btn_start_activity"
            android:layout_weight="1"
            android:text="Activity"
            android:textSize="12dp"
            android:textAllCaps="false"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/noti_btn_send_broadcast"
            android:layout_weight="1"
            android:text="Broadcast"
            android:textSize="12dp"
            android:textAllCaps="false"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/noti_btn_start_service"
            android:layout_weight="1"
            android:text="Service"
            android:textSize="12dp"
            android:textAllCaps="false"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</LinearLayout>