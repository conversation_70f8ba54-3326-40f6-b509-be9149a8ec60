#Wed Jul 09 21:01:48 ICT 2025
com.example.androidtraining.app-main-52\:/drawable-xhdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-xhdpi_ic_arrow.png.flat
com.example.androidtraining.app-main-52\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.androidtraining.app-main-52\:/drawable/rounded_background.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_background.xml.flat
com.example.androidtraining.app-main-52\:/layout/view_layout_include.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_view_layout_include.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_launcher_background.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.androidtraining.app-main-52\:/drawable-xxhdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-xxhdpi_ic_arrow.png.flat
com.example.androidtraining.app-main-52\:/layout/activity_database.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_database.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-xhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.androidtraining.app-main-52\:/layout/file_row.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_file_row.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_search.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_alarm.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_alarm.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_sort.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_sort.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_gallery.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_gallery.xml.flat
com.example.androidtraining.app-main-52\:/drawable-mdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-mdpi_ic_next.png.flat
com.example.androidtraining.app-main-52\:/drawable/ic_favorite_filled.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_favorite_filled.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.androidtraining.app-main-52\:/mipmap-hdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.androidtraining.app-main-52\:/layout/activity_adapter.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_adapter.xml.flat
com.example.androidtraining.app-main-52\:/drawable-xhdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-xhdpi_ic_next.png.flat
com.example.androidtraining.app-main-52\:/layout/fragment_list_view.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_list_view.xml.flat
com.example.androidtraining.app-main-52\:/drawable/gradient_overlay.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_overlay.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_select_all.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_select_all.xml.flat
com.example.androidtraining.app-main-52\:/layout/fragment_two.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_two.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.androidtraining.app-main-52\:/drawable-anydpi/ic_arrow.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-anydpi_ic_arrow.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_custom.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_custom.xml.flat
com.example.androidtraining.app-main-52\:/drawable-hdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-hdpi_ic_arrow.png.flat
com.example.androidtraining.app-main-52\:/drawable/ic_delete.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.example.androidtraining.app-main-52\:/layout/item_gallery_image.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_gallery_image.xml.flat
com.example.androidtraining.app-pngs-45\:/drawable-anydpi-v24/ic_launcher_foreground.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat
com.example.androidtraining.app-main-52\:/xml/backup_rules.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_broadcast.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_broadcast.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.androidtraining.app-main-52\:/layout/activity_share_prefs.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_share_prefs.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_favorite_border.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_favorite_border.xml.flat
com.example.androidtraining.app-main-52\:/layout/fragment_recycle_view.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_recycle_view.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_refresh.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_refresh.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_filter.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_filter.xml.flat
com.example.androidtraining.app-main-52\:/drawable-xxhdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-xxhdpi_ic_next.png.flat
com.example.androidtraining.app-main-52\:/layout/activity_image_detail.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_image_detail.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_image_placeholder.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image_placeholder.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_example.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_example.xml.flat
com.example.androidtraining.app-main-52\:/layout/notification_layout.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_notification_layout.xml.flat
com.example.androidtraining.app-main-52\:/layout/item_list_song.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_list_song.xml.flat
com.example.androidtraining.app-main-52\:/menu/gallery_menu.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_gallery_menu.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.androidtraining.app-main-52\:/menu/image_detail_menu.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_image_detail_menu.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_snooze.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_snooze.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.androidtraining.app-main-52\:/layout/fragment_top.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_top.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-mdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.androidtraining.app-main-52\:/drawable-anydpi/ic_next.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-anydpi_ic_next.xml.flat
com.example.androidtraining.app-main-52\:/layout/fragment_one.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_one.xml.flat
com.example.androidtraining.app-main-52\:/layout/fragment_bottom.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_bottom.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_thread.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_thread.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_notification.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_notification.xml.flat
com.example.androidtraining.app-main-52\:/drawable/ic_share.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_share.xml.flat
com.example.androidtraining.app-main-52\:/drawable-hdpi/ic_next.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-hdpi_ic_next.png.flat
com.example.androidtraining.app-main-52\:/drawable-mdpi/ic_arrow.png=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-mdpi_ic_arrow.png.flat
com.example.androidtraining.app-main-52\:/layout/item_list_user.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_list_user.xml.flat
com.example.androidtraining.app-main-52\:/xml/data_extraction_rules.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_course.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_course.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_main.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_service.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_service.xml.flat
com.example.androidtraining.app-main-52\:/layout/activity_view_layout.xml=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_view_layout.xml.flat
com.example.androidtraining.app-main-52\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\AndroidTraining\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
