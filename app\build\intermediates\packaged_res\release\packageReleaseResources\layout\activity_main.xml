<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <Button
        android:id="@+id/btn_start_course"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Start Training Course"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/btn_margin_top"
        />

    <Button
        android:id="@+id/btn_start_xx"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Start XXX"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_start_course"
        android:layout_marginTop="@dimen/btn_margin_top"
        />

</androidx.constraintlayout.widget.ConstraintLayout>