<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.activityfragment.ExampleActivity">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_15"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.15"
        />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_40"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.40"
        />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_55"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.55"
        />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_top"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="10dp"
        android:background="@color/teal_200"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/guideline_15"
        />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="10dp"
        android:background="@color/purple_200"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fragment_top"
        app:layout_constraintBottom_toTopOf="@+id/guideline_40"
        />

    <EditText
        android:id="@+id/edt_activity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:textAlignment="center"
        android:hint="Edit text of Activity"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guideline_40"
        />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/guideline_55"
        android:text="Show fragment 1 or fragment 2 below"
        android:textSize="18dp"
        android:fontFamily="sans-serif-light bold"
        android:textAlignment="center"
        />

    <FrameLayout
        android:id="@+id/fragment_container_activity"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="10dp"
        android:background="@color/teal_200"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guideline_55"
        app:layout_constraintBottom_toTopOf="@+id/btn_show_fragment1" />

    <Button
        android:id="@+id/btn_show_fragment1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Show Fragment 1"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="10dp"
        />

    <Button
        android:id="@+id/btn_show_fragment2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Show Fragment 2"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="10dp"
        />

</androidx.constraintlayout.widget.ConstraintLayout>