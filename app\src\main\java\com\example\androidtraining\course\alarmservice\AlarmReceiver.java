package com.example.androidtraining.course.alarmservice;

import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.example.androidtraining.R;
import com.example.androidtraining.app.AppLifecycleHandler;
import com.example.androidtraining.utils.Logger;

import java.util.Calendar;

public class AlarmReceiver extends BroadcastReceiver {
    private static final String TAG = AlarmReceiver.class.getSimpleName();

    @Override
    public void onReceive(Context context, Intent intent) {
        // can not start new activity in broadcast receiver / service
        // so need check app is running or not to show notification or show snooze activity
        boolean isAppRunning = AppLifecycleHandler.isAppRunning(context);
        String action = intent.getAction();
        Logger.d(TAG, "onReceive: isAppRunning=" + isAppRunning + ", isAppInForeground=" + AppLifecycleHandler.isAppInForeground() + ", action=" + action);
        if (TextUtils.isEmpty(action)) return;
        switch (action) {
            case AlarmConstants.ACTION_ALARM:
                handleAlarmInfo(context, intent); // show notification
                break;
            case AlarmConstants.ACTION_BOOT_COMPLETED:
                setAllAlarmAgain(context); // set Alarm again after reboot
                break;
            default:
                break;
        }
    }

    private void handleAlarmInfo(@NonNull Context context, @NonNull Intent intent) {
        // get alarm info from intent and show notification
        int hour = intent.getIntExtra(AlarmConstants.ALARM_HOUR, 0);
        int minute = intent.getIntExtra(AlarmConstants.ALARM_MIN, 0);
        int repeatCount = intent.getIntExtra(AlarmConstants.ALARM_REPEAT_COUNT, 0);
        int repeatTime = intent.getIntExtra(AlarmConstants.ALARM_REPEAT_TIME, 0);
        int alarmId = intent.getIntExtra(AlarmConstants.ALARM_ID, 0);
        Logger.d(TAG, "handleAlarmInfo: alarmInfo, hour: " + hour + ", minute: " + minute + ", repeatCount=" + repeatCount + ", alarmId:" + alarmId);

        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        Notification.Builder builder;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // create chanel
            NotificationChannel channel = new NotificationChannel("channel_id", "channel_name", NotificationManager.IMPORTANCE_DEFAULT);
            notificationManager.createNotificationChannel(channel);
            // create builder
            builder = new Notification.Builder(context, "channel_id");
        } else {
            builder = new Notification.Builder(context);
        }

        builder.setContentTitle("Alarm");
        String contentText = "Time to wake up! - " + hour + ":";
        contentText += (minute <  10 ? "0" + minute : "" + minute);
        if (repeatCount > 1) contentText += "\nRepeating " + repeatCount + " times every " + repeatTime + " minutes";
        builder.setContentText(contentText);
        builder.setSmallIcon(R.drawable.ic_launcher_background);
        builder.setAutoCancel(true);
        // add pending intent
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, new Intent(context, SnoozeActivity.class), PendingIntent.FLAG_IMMUTABLE);
        builder.setContentIntent(pendingIntent);

        // show notification
        notificationManager.notify(1, builder.build());  // id is unique for each notification

        if (AppLifecycleHandler.isAppInForeground() || AppLifecycleHandler.isAppRunning(context)) {
            // show snooze activity: only work when app is in foreground
            Intent actIntent = new Intent(context, SnoozeActivity.class);  // create an intent to start the activity
//            Logger.d(TAG, "handleAlarmInfo: startActivity, intent=" + actIntent);
            actIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(actIntent);
        }
        // schedule alarm if repeat count is more than 1
        if (repeatCount > 1) {
            repeatCount -= 1;
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            Calendar calendar = Calendar.getInstance();
//            calendar.setTimeInMillis(System.currentTimeMillis());
            if (minute + repeatTime >= 60) hour += 1;
            minute = (minute + repeatTime) % 60;
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Logger.d(TAG, "handleAlarmInfo: schedule alarm: hour: " + hour + ", minute: " + minute + ", repeatCount=" + repeatCount + ", alarmId=" + alarmId * 100 + repeatCount);
            Intent intent1 = new Intent(context, AlarmReceiver.class);
            intent1.setAction(AlarmConstants.ACTION_ALARM);
            intent1.putExtra(AlarmConstants.ALARM_HOUR, hour);
            intent1.putExtra(AlarmConstants.ALARM_MIN, minute);
            intent1.putExtra(AlarmConstants.ALARM_REPEAT_COUNT, repeatCount);
            intent1.putExtra(AlarmConstants.ALARM_REPEAT_TIME, repeatTime);
            intent1.putExtra(AlarmConstants.ALARM_ID, alarmId);
            PendingIntent alarmIntent = PendingIntent.getBroadcast(context, alarmId * 100 + repeatCount, intent1,
                    PendingIntent.FLAG_IMMUTABLE| PendingIntent.FLAG_CANCEL_CURRENT);
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), alarmIntent);
        }
    }

    private void setAllAlarmAgain(@NonNull Context context) {

    }
}
