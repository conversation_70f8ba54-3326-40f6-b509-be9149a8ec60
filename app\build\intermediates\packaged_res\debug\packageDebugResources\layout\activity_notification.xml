<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.notification.NotificationActivity">

    <Button
        android:id="@+id/btn_create_notification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Make notification"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <Button
        android:id="@+id/btn_create_remote_notification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Make Custom notification"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_create_notification"
        app:layout_constraintBottom_toBottomOf="parent"
        />


</androidx.constraintlayout.widget.ConstraintLayout>