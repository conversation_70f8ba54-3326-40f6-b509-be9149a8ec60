package com.example.androidtraining.course.database;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.course.database.room.entities.Job;
import com.example.androidtraining.course.database.sqlite.SQLiteDbHelper;
import com.example.androidtraining.course.database.sqlite.model.Engineer;
import com.example.androidtraining.course.database.room.AppDatabaseRoom;
import com.example.androidtraining.course.database.room.UserAdapter;
import com.example.androidtraining.course.database.room.dao.JobDao;
import com.example.androidtraining.course.database.room.dao.UserDao;
import com.example.androidtraining.course.database.room.entities.User;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseError;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.ValueEventListener;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class DatabaseActivity extends BaseActivity implements UserAdapter.OnItemClickedListener {
    private static final String TAG = DatabaseActivity.class.getSimpleName();

    UserDao userDao;
    JobDao jobDao;

    List<User> listUser = new ArrayList<>();
    List<Job> listJob = new ArrayList<>();
    RecyclerView userRecyclerView;
    UserAdapter userAdapter;
    RecyclerView.SmoothScroller smoothScroller;
    LinearLayoutManager layoutManager;

    SQLiteDatabase mSQLiteDb;
    SQLiteDatabase appDbSQLite;

    static final String AUTHORITIES = "com.example.androidtraining.database.sqlite.EngineerContentProvider";
    static final String C_URI_ENGINEER = "engineer";
    static final Uri uri = Uri.parse("content://" + AUTHORITIES + "/" + C_URI_ENGINEER);

    final int MSG_UPDATE = 10;
    Handler mHandler = new Handler(Looper.getMainLooper()) {
        @SuppressLint("NotifyDataSetChanged")
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == MSG_UPDATE) {
                userAdapter.notifyDataSetChanged();
            }
        }
    };

    @SuppressLint("Range")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_database;
        super.onCreate(savedInstanceState);

        Logger.d(TAG, "onCreate: start=");

        ///////////////// Room Database /////////////////////////////////////////////////////////////
        userAdapter = new UserAdapter();
        userAdapter.setClickedListener(this);
        userRecyclerView = findViewById(R.id.user_recycle_view);
        layoutManager = new LinearLayoutManager(this);
        layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        userRecyclerView.setLayoutManager(layoutManager);
        userRecyclerView.addItemDecoration(new DividerItemDecoration(this, LinearLayoutManager.VERTICAL));
        userRecyclerView.setAdapter(userAdapter);
        smoothScroller = new LinearSmoothScroller(this) {
            @Override
            protected int getVerticalSnapPreference() {
                return LinearSmoothScroller.SNAP_TO_START;
            }
        };

        AppDatabaseRoom appDbRoom = AppDatabaseRoom.getDatabase(this);
        userDao = appDbRoom.userDao();
        jobDao = appDbRoom.jobDao();

        new Thread(() -> {
            listUser = userDao.getAllUsers();
            if (listUser.isEmpty() || TextUtils.isEmpty(listUser.get(0).getSingleId())) {
                insertUserExample();
            }
            userAdapter.setListUser(listUser);
//            userAdapter.notifyDataSetChanged();
            mHandler.sendEmptyMessage(MSG_UPDATE);
            listJob = jobDao.getAllJobs();
            Logger.d(TAG, "onCreate: listTask, size=" + listJob.size() + " - " + Arrays.toString(listJob.toArray()));
            if (listJob.isEmpty()) {
                insertJobExample();
            }
        }).start();

        ///////////////// SQLite Database /////////////////////////////////////////////////////////////
        SQLiteOpenHelper sqLiteOpenHelper = new SQLiteDbHelper(this);
        appDbSQLite = sqLiteOpenHelper.getWritableDatabase();
        // Query all
        Cursor cursor = appDbSQLite.query(DbConstants.ENGINEER_TABLE, null, null, null, null, null, null);
        if (cursor != null) {
            Logger.d(TAG, "onCreate: listEngineers, size=" + cursor.getCount());
            if (cursor.getCount() > 0) {
                while (cursor.moveToNext()) {
                    int id = cursor.getInt(cursor.getColumnIndex(DbConstants.ENGINEER_ID_COL));
                    String name = cursor.getString(cursor.getColumnIndex(DbConstants.ENGINEER_NAME_COL));
                    int gen = cursor.getInt(cursor.getColumnIndex(DbConstants.ENGINEER_GEN_COL));
                    String singleId = cursor.getString(cursor.getColumnIndex(DbConstants.ENGINEER_SINGLE_ID_COL));
                    Engineer engineer = new Engineer(id, name, gen, singleId);
                    Logger.d(TAG, "onCreate: engineers=" + engineer);
                }
            } else {
                insertEngineerExample();
            }
            cursor.close();
        }

        // Firebase
        String email = "<EMAIL>";
        String password = "Ny0890sys*";
        SharedPreferences preference = getPreferences(MODE_PRIVATE);
        boolean isNew = preference.getBoolean("isNew", true);
        checkFirebaseAuth(email, password, isNew);
        if (isNew) {
            SharedPreferences.Editor editor = preference.edit();
            editor.putBoolean("isNew", false);
            editor.apply();
        }

        checkFirebaseRealtimeDb();
    }

    private void insertUserExample() {
        for (int i = 0; i < 50; i++) {
            listUser.add(new User(i,
                    "Nguyen Minh Ngoc - " + i,
                    1000 + i,
                    "09839856" + i));
        }
        userDao.insertUsers(listUser);
    }

    private void insertJobExample() {
        Random random = new Random();
        for (int i = 0; i < 20; i++) {
            int userId = random.nextInt(50);
            listJob.add(new Job(i, "Task " + i, userId));
        }
        jobDao.insertJobs(listJob);
    }

    private void insertEngineerExample() {
        for (int i = 0; i < 50; i++) {
            ContentValues cv = new ContentValues();
            cv.put(DbConstants.ENGINEER_ID_COL, i);
            cv.put(DbConstants.ENGINEER_NAME_COL, "Nguyen Minh Ngoc - " + i);
            cv.put(DbConstants.ENGINEER_GEN_COL, 1000 + i);
            cv.put(DbConstants.ENGINEER_SINGLE_ID_COL, "09839856" + i);
            appDbSQLite.insert(DbConstants.ENGINEER_TABLE, null, cv);
//            appDbSQLite.update(DbConstants.ENGINEER_TABLE, cv, "id=?", new String[]{"1"});
        }

        Random random = new Random();
        for (int i = 0; i < 20; i++) {
            int engineerId = random.nextInt(50);
            ContentValues cv = new ContentValues();
            cv.put(DbConstants.TASK_ID_COL, i);
            cv.put(DbConstants.TASK_TITLE_COL, "Task " + i);
            cv.put(DbConstants.TASK_ENGINEER_ID_COL, engineerId);
            appDbSQLite.insert(DbConstants.TASK_TABLE, null, cv);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void onItemClick(int pos) {
        Logger.d(TAG, "onItemClick: pos=" + pos);
        Toast.makeText(this, "onItemClick: pos=" + pos, Toast.LENGTH_SHORT).show();
        smoothScroller.setTargetPosition(pos);
        layoutManager.startSmoothScroll(smoothScroller);
    }

    private void checkFirebaseRealtimeDb() {
        // login to firebase realtime db before
//        FirebaseAuth firebaseAuth = FirebaseAuth.getInstance();
//        firebaseAuth.signOut();

        String dbUrl = "https://apptest-53581-default-rtdb.asia-southeast1.firebasedatabase.app";
        FirebaseDatabase firebaseDatabase = FirebaseDatabase.getInstance(dbUrl);
        // DB name = package name of app
        DatabaseReference database = firebaseDatabase.getReference(getResources().getString(R.string.app_name));
        Logger.d(TAG, "checkFirebaseRealtimeDb - " + database.getKey() + " - " + database.toString());
        // write
        // db name - table name - record
        String userTable = "users";
        String jobTable = "jobs"; // jobs
        String userJobsTable = "users-jobs"; // jobs of user
        DatabaseReference userTableRef = database.child(userTable);
//        DatabaseReference userRecordRef = userTableRef.push(); // auto generate id -> id is String, not int
        DatabaseReference userRecordRef = userTableRef.child("1"); // record id = 1
        String userId = userRecordRef.getKey();
        Logger.d(TAG, "checkFirebaseRealtimeDb: recordUser - id - " + userId); // auto generate id
        User user = new User("Nguyen Minh Ngoc", 31, "Ha Noi");
        userRecordRef.setValue(user)
                .addOnCompleteListener(task -> Logger.d(TAG, "setValue: " + task.isSuccessful()))
                .addOnFailureListener(e -> Logger.d(TAG, "setValue: fail - " + e.getMessage()))
                .addOnSuccessListener(unused -> Logger.d(TAG, "setValue: success"));

        // listen changed & read data from realtime db
        userRecordRef.addValueEventListener(new ValueEventListener() {
            @Override
            public void onDataChange(@NonNull DataSnapshot snapshot) {
                Logger.d(TAG, "onDataChange: " + snapshot.getValue());
            }

            @Override
            public void onCancelled(@NonNull DatabaseError error) {
                Logger.d(TAG, "onCancelled: " + error.getMessage());
            }
        });

        // set record by child
//        recordUser = table.child("2"); // record id = 2
//        recordUser.child("name").setValue("Nguyen Minh Ngoc 2");
//        recordUser.child("age").setValue("31");
//        recordUser.child("address").setValue("Ha Noi 2");

        // set record by push()
//        userRecord = userTableRef.push();
        Job job = new Job("Task 1", Integer.valueOf(userId));
        Map<String, Object> jobValues = job.toMap();

        DatabaseReference jobTableRef = database.child(jobTable);
//        DatabaseReference jobRecordRef = jobTableRef.push();
        DatabaseReference jobRecordRef = jobTableRef.child("1");
        String jobId = jobRecordRef.getKey();

        Map<String, Object> childUpdates = new HashMap<>();
        childUpdates.put("/" + jobTable + "/" + jobId, jobValues);
        childUpdates.put("/" + userJobsTable + "/" + userId + "/" + jobId, jobValues);
        database.updateChildren(childUpdates);

        jobRecordRef.addValueEventListener(new ValueEventListener() {
            @Override
            public void onDataChange(@NonNull DataSnapshot snapshot) {
                Logger.d(TAG, "onDataChange: " + snapshot.getValue());
            }

            @Override
            public void onCancelled(@NonNull DatabaseError error) {
                Logger.d(TAG, "onCancelled: " + error.getMessage());
            }
        });
    }

    private void checkFirebaseAuth(String email, String password, boolean isNew) {
        FirebaseAuth firebaseAuth = FirebaseAuth.getInstance();
        FirebaseUser firebaseUser = firebaseAuth.getCurrentUser();
        Log.d(TAG, "checkFirebaseAuth: " + (firebaseUser != null ? firebaseUser.getEmail() : "null") + " email: " + email);
        if (firebaseUser == null) {
            if (isNew) { // create new user
                firebaseAuth.createUserWithEmailAndPassword(email, password).addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser user = firebaseAuth.getCurrentUser();
                        Log.d(TAG, "createUserWithEmailAndPassword: success - user: " + user.toString());
                    } else {
                        Log.d(TAG, "createUserWithEmailAndPassword: fail - " + task.getException());
                    }
                });
            } else { // sign in with user/pass
                firebaseAuth.signInWithEmailAndPassword(email, password).addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser user = firebaseAuth.getCurrentUser();
                        Log.d(TAG, "signInWithEmailAndPassword: success, user: " + user.toString());
                    } else {
                        Log.d(TAG, "signInWithEmailAndPassword: fail - " + task.getException());
                    }
                });
            }
        }
    }
}

//FireBase:
//        1. Create Firebase project from FireBase console & add App
//	- FireBase project -> add App: android/apple/web/unity/flutter
//	- add App Android: Register app: appId, appName
//	- download google-services.json
//	- Copy google-services.json to app-level
//	- add lib to build.gradle: 	// To make the values in your google-services.json config file accessible to Firebase SDKs, you need the Google services Gradle plugin
//        + project-level:
//        - plugins { id 'com.google.gms.google-services' version '4.4.3' apply false }
//		+ app-level:
//        - plugins { id 'com.google.gms.google-services' }
//			- add dependencies:
//// Import the Firebase BoM: // When using the BoM, you don't specify versions in Firebase library dependencies
//implementation(platform("com.google.firebase:firebase-bom:33.16.0"))
//
//// Add the dependency for the Firebase SDK for Google Analytics
//implementation("com.google.firebase:firebase-analytics")
//
//// TODO: Add the dependencies for any other Firebase products you want to use
//// See https://firebase.google.com/docs/android/setup#available-libraries
//// For example, add the dependencies for Firebase Authentication and Cloud Firestore
//implementation("com.google.firebase:firebase-auth")
//implementation("com.google.firebase:firebase-firestore")
//
//2. FireBase Authentication
//	- FireBase console -> Project settings -> FireBase Authentication -> Get starts -> Enable Sign-in providers: Email/Password
//		+ Email link (passwordless sign-in): need more config
//
//	- FirebaseAuth firebaseAuth = FirebaseAuth.getInstance();		FirebaseUser firebaseUser = firebaseAuth.getCurrentUser();
//	- firebaseAuth.createUserWithEmailAndPassword(email, password).addOnCompleteListener(task -> {});
//        - firebaseAuth.signInWithEmailAndPassword(email, password).addOnCompleteListener(task -> {});
//        - Logout:
//
//        3. FireBase Realtime Database:
//        - Sign-in before
//	- FireBase console -> Project settings -> FireBase Realtime Database -> Create DB -> enable Rules:
//        - add lib: BOM vs implementation("com.google.firebase:firebase-database")
//
//	- FirebaseDatabase firebaseDatabase = FirebaseDatabase.getInstance("dbUrl"); // https://apptest-53581-default-rtdb.asia-southeast1.firebasedatabase.app/
//		+ for databases in us-central1: not need url
//	- DatabaseReference databaseReference = firebaseDatabase.getReference("message"); // create key: message in databases -> https://apptest-53581-default-rtdb.asia-southeast1.firebasedatabase.app/message
//	- databaseReference.child("hello").setValue("Hello World");		// message {"hello": "Hello World"}
//	- databaseReference.addValueEventListener(new ValueEventListener() {
//    @Override
//    public void onDataChange(@NonNull DataSnapshot snapshot) {
//        Log.d(TAG, "onDataChange: " + snapshot.getValue());
//    }
//
//    @Override
//    public void onCancelled(@NonNull DatabaseError error) {
//        Log.d(TAG, "onCancelled: " + error.getMessage());
//    }
//});
//
//        - Data type: num, string, boolean, List<Object>, Map<String, Object>
//		+ write: 		setValue() - 			mDatabase.child("users").child(userId).setValue(user)					mDatabase.child("users").child(userId).child("username").setValue(name)
//		+ delete:		removeValue()
//		+ Observer: 	addValueEventListener
//		+ read once:	get() - 				mDatabase.child("users").child(userId).get().addOnCompleteListener()	addListenerForSingleValueEvent
//		+ push & update many object:
//        - // Create new post at paths: 		/posts/$postid 			and			 /user-posts/$userid/$postid
//String postId = mDatabase.child("posts").push().getKey(); 	// create new post then get new postId
//Post post = new Post(userId, username, title, body);
//Map<String, Object> postValues = post.toMap();
//
//Map<String, Object> childUpdates = new HashMap<>();
//			childUpdates.put("/posts/" + postId, postValues);
//			childUpdates.put("/user-posts/" + userId + "/" + postId, postValues);
//			mDatabase.updateChildren(childUpdates);
//
//		+ use Transaction: when concurrent modifications -> postRef.runTransaction(new Transaction.Handler() {});
//        + Atomic server-side increments:					updates.put("posts/"+key+"/starCount", ServerValue.increment(1)); -> mDatabase.updateChildren(updates);
//
//		+ List data:	databaseReference.addChildEventListener(new ChildEventListener() {});
//        - Sort:		Query myTopPostsQuery = databaseReference.child("user-posts").child(myUserId).orderByChild("starCount");		// orderByKey() / orderByValue()
//						myTopPostsQuery.addChildEventListener(new ChildEventListener() {});
//
//        + Enabling Offline Capabilities on Android: https://firebase.google.com/docs/database/android/offline-capabilities

