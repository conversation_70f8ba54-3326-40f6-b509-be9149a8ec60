package com.example.androidtraining.course.multithread;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "room_images")
public class RoomImage {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    @ColumnInfo(name = "name")
    private String name;
    
    @ColumnInfo(name = "path")
    private String path;
    
    @ColumnInfo(name = "size")
    private long size;
    
    @ColumnInfo(name = "width")
    private int width;
    
    @ColumnInfo(name = "height")
    private int height;
    
    @ColumnInfo(name = "date_taken")
    private long dateTaken;
    
    @ColumnInfo(name = "date_modified")
    private long dateModified;
    
    @ColumnInfo(name = "is_liked")
    private boolean isLiked;
    
    @ColumnInfo(name = "thumbnail_path")
    private String thumbnailPath;
    
    @ColumnInfo(name = "mime_type")
    private String mimeType;
    
    @ColumnInfo(name = "detected_objects")
    private String detectedObjects; // JSON string of detected objects
    
    @ColumnInfo(name = "is_processed")
    private boolean isProcessed; // Whether object detection has been run
    
    // Constructors
    public RoomImage() {}
    
    public RoomImage(String name, String path, long size, int width, int height, 
                    long dateTaken, long dateModified, String mimeType) {
        this.name = name;
        this.path = path;
        this.size = size;
        this.width = width;
        this.height = height;
        this.dateTaken = dateTaken;
        this.dateModified = dateModified;
        this.mimeType = mimeType;
        this.isLiked = false;
        this.isProcessed = false;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public long getSize() {
        return size;
    }
    
    public void setSize(long size) {
        this.size = size;
    }
    
    public int getWidth() {
        return width;
    }
    
    public void setWidth(int width) {
        this.width = width;
    }
    
    public int getHeight() {
        return height;
    }
    
    public void setHeight(int height) {
        this.height = height;
    }
    
    public long getDateTaken() {
        return dateTaken;
    }
    
    public void setDateTaken(long dateTaken) {
        this.dateTaken = dateTaken;
    }
    
    public long getDateModified() {
        return dateModified;
    }
    
    public void setDateModified(long dateModified) {
        this.dateModified = dateModified;
    }
    
    public boolean isLiked() {
        return isLiked;
    }
    
    public void setLiked(boolean liked) {
        isLiked = liked;
    }
    
    public String getThumbnailPath() {
        return thumbnailPath;
    }
    
    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }
    
    public String getMimeType() {
        return mimeType;
    }
    
    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }
    
    public String getDetectedObjects() {
        return detectedObjects;
    }
    
    public void setDetectedObjects(String detectedObjects) {
        this.detectedObjects = detectedObjects;
    }
    
    public boolean isProcessed() {
        return isProcessed;
    }
    
    public void setProcessed(boolean processed) {
        isProcessed = processed;
    }
    
    // Helper methods
    public String getFormattedSize() {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
        return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
    }
    
    public String getDimensions() {
        return width + " x " + height;
    }
    
    @Override
    public String toString() {
        return "RoomImage{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", path='" + path + '\'' +
                ", size=" + size +
                ", width=" + width +
                ", height=" + height +
                ", dateTaken=" + dateTaken +
                ", dateModified=" + dateModified +
                ", isLiked=" + isLiked +
                ", thumbnailPath='" + thumbnailPath + '\'' +
                ", mimeType='" + mimeType + '\'' +
                ", detectedObjects='" + detectedObjects + '\'' +
                ", isProcessed=" + isProcessed +
                '}';
    }
}
