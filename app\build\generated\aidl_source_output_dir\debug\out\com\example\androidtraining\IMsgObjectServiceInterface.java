/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.example.androidtraining;
public interface IMsgObjectServiceInterface extends android.os.IInterface
{
  /** Default implementation for IMsgObjectServiceInterface. */
  public static class Default implements com.example.androidtraining.IMsgObjectServiceInterface
  {
    @Override public void sendMsg(com.example.androidtraining.course.model.MsgObject msg) throws android.os.RemoteException
    {
    }
    @Override public void receiverMsg(com.example.androidtraining.course.model.MsgObject msg) throws android.os.RemoteException
    {
    }
    @Override public com.example.androidtraining.course.model.MsgObject getMsg() throws android.os.RemoteException
    {
      return null;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.example.androidtraining.IMsgObjectServiceInterface
  {
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.example.androidtraining.IMsgObjectServiceInterface interface,
     * generating a proxy if needed.
     */
    public static com.example.androidtraining.IMsgObjectServiceInterface asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.example.androidtraining.IMsgObjectServiceInterface))) {
        return ((com.example.androidtraining.IMsgObjectServiceInterface)iin);
      }
      return new com.example.androidtraining.IMsgObjectServiceInterface.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      if (code >= android.os.IBinder.FIRST_CALL_TRANSACTION && code <= android.os.IBinder.LAST_CALL_TRANSACTION) {
        data.enforceInterface(descriptor);
      }
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
      }
      switch (code)
      {
        case TRANSACTION_sendMsg:
        {
          com.example.androidtraining.course.model.MsgObject _arg0;
          _arg0 = _Parcel.readTypedObject(data, com.example.androidtraining.course.model.MsgObject.CREATOR);
          this.sendMsg(_arg0);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_receiverMsg:
        {
          com.example.androidtraining.course.model.MsgObject _arg0;
          _arg0 = new com.example.androidtraining.course.model.MsgObject();
          this.receiverMsg(_arg0);
          reply.writeNoException();
          _Parcel.writeTypedObject(reply, _arg0, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
          break;
        }
        case TRANSACTION_getMsg:
        {
          com.example.androidtraining.course.model.MsgObject _result = this.getMsg();
          reply.writeNoException();
          _Parcel.writeTypedObject(reply, _result, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
          break;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
      return true;
    }
    private static class Proxy implements com.example.androidtraining.IMsgObjectServiceInterface
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void sendMsg(com.example.androidtraining.course.model.MsgObject msg) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _Parcel.writeTypedObject(_data, msg, 0);
          boolean _status = mRemote.transact(Stub.TRANSACTION_sendMsg, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void receiverMsg(com.example.androidtraining.course.model.MsgObject msg) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_receiverMsg, _data, _reply, 0);
          _reply.readException();
          if ((0!=_reply.readInt())) {
            msg.readFromParcel(_reply);
          }
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public com.example.androidtraining.course.model.MsgObject getMsg() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        com.example.androidtraining.course.model.MsgObject _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getMsg, _data, _reply, 0);
          _reply.readException();
          _result = _Parcel.readTypedObject(_reply, com.example.androidtraining.course.model.MsgObject.CREATOR);
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
    }
    static final int TRANSACTION_sendMsg = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_receiverMsg = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_getMsg = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
  }
  public static final java.lang.String DESCRIPTOR = "com.example.androidtraining.IMsgObjectServiceInterface";
  public void sendMsg(com.example.androidtraining.course.model.MsgObject msg) throws android.os.RemoteException;
  public void receiverMsg(com.example.androidtraining.course.model.MsgObject msg) throws android.os.RemoteException;
  public com.example.androidtraining.course.model.MsgObject getMsg() throws android.os.RemoteException;
  /** @hide */
  static class _Parcel {
    static private <T> T readTypedObject(
        android.os.Parcel parcel,
        android.os.Parcelable.Creator<T> c) {
      if (parcel.readInt() != 0) {
          return c.createFromParcel(parcel);
      } else {
          return null;
      }
    }
    static private <T extends android.os.Parcelable> void writeTypedObject(
        android.os.Parcel parcel, T value, int parcelableFlags) {
      if (value != null) {
        parcel.writeInt(1);
        value.writeToParcel(parcel, parcelableFlags);
      } else {
        parcel.writeInt(0);
      }
    }
  }
}
