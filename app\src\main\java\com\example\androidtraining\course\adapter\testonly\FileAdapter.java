package com.example.androidtraining.course.adapter.testonly;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.androidtraining.R;
import com.google.android.material.imageview.ShapeableImageView;

import java.util.ArrayList;
import java.util.List;

public class FileAdapter extends RecyclerView.Adapter<FileAdapter.ViewHolder>{
    List<FileName> list = new ArrayList<>();
    private Context context;

    FileAdapter(Context context, List<FileName> l) {
        list = l;
        this.context = context;
    }

    public void setList(List<FileName> l) {
        list = l;
    }
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.file_row, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        FileName fileName = list.get(position);
        holder.iconView.setId(fileName.getId());
        holder.fileNameView.setText(fileName.getFileNme());
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        ShapeableImageView iconView;
        TextView fileNameView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.listItemIcon);
            fileNameView = itemView.findViewById(R.id.fileName);
        }
    }
}
