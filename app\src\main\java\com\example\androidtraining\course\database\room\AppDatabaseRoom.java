package com.example.androidtraining.course.database.room;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.example.androidtraining.course.database.DbConstants;
import com.example.androidtraining.course.database.room.dao.JobDao;
import com.example.androidtraining.course.database.room.dao.UserDao;
import com.example.androidtraining.course.database.room.entities.Job;
import com.example.androidtraining.course.database.room.entities.User;

@Database(entities = {User.class, Job.class}, version = DbConstants.ROOM_DB_VERSION)
public abstract class AppDatabaseRoom extends RoomDatabase {
    public abstract UserDao userDao();
    public abstract JobDao jobDao();

    private static AppDatabaseRoom mInstance;
    public static AppDatabaseRoom getDatabase(final Context context) {
        synchronized (AppDatabaseRoom.class) {
            if (mInstance == null) {
                mInstance = Room.databaseBuilder(context.getApplicationContext(),
                        AppDatabaseRoom.class, DbConstants.ROOM_DB_NAME)
                        .fallbackToDestructiveMigration() // for upgrade database
                         .allowMainThreadQueries() // REMOVED: This is dangerous and can cause ANR
                        .build();
            }
            return mInstance;
        }
    }
}
