<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Storage permissions: API < 30 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Storage permissions: API < 30 - MANAGE_EXTERNAL_STORAGE & request "All files access" permission -->
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />

    <!--    Alarm service-->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />

    <permission android:name="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />

<!--    API 33+  -->
<!--    Foreground Service: need foregroundServiceType & permission: FOREGROUND_SERVICE & FOREGROUND_SERVICE_SPECIAL_USE  -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

    <application
        android:name=".app.MainApp"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AndroidTraining"
        tools:targetApi="31">

        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".course.CourseActivity"
            android:configChanges="orientation|screenLayout|layoutDirection|screenSize"
            android:exported="true"
            android:launchMode="singleInstance">

            <!-- <intent-filter> -->
            <!-- <action android:name="android.intent.action.MAIN" /> -->
            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
            <!-- </intent-filter> -->

            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <activity
            android:name=".course.alarmservice.SnoozeActivity"
            android:launchMode="singleInstance"
            android:exported="false" />

        <activity
            android:name=".course.alarmservice.AlarmActivity"
            android:exported="false" />

        <activity
            android:name=".course.viewlayout.ViewLayoutActivity"
            android:exported="false" />

        <activity
            android:name=".course.activityfragment.ExampleActivity"
            android:exported="true" />
        <activity
            android:name=".course.adapter.AdapterActivity"
            android:exported="false" />
        <activity
            android:name=".course.broadcastreceiver.BroadcastActivity"
            android:exported="false" />
        <activity
            android:name=".course.notification.NotificationActivity"
            android:exported="false" />
        <activity
            android:name=".course.service.ServiceActivity"
            android:exported="false" />
        <activity
            android:name=".course.shareprefs.SharePrefsActivity"
            android:exported="false" />
        <activity
            android:name=".course.database.DatabaseActivity"
            android:exported="false" />
        <activity
            android:name=".course.threadhandler.ThreadActivity"
            android:exported="false" />

        <receiver
            android:name=".course.broadcastreceiver.StaticBroadcastReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <!-- system action -->
                <action android:name="android.intent.action.AIRPLANE_MODE" />
                <action android:name="android.intent.action.SCREEN_ON" />
                <action android:name="android.intent.action.SCREEN_OFF" />
                <!-- user define action -->
                <action android:name="com.example.androidtraining.USER_ACTION" />
                <action android:name="com.example.androidtraining.NEW_ACTION" />
                <action android:name="com.example.androidtraining.LOCAL_ACTION" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".course.alarmservice.AlarmReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.example.androidtraining.ALARM_ACTION" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <service android:name=".course.service.BackgroundService" />

        <service
            android:name=".course.service.ForegroundService"
            android:foregroundServiceType="specialUse" />

        <service
            android:name=".course.service.BoundedServiceLocal"
            android:exported="true" />
        <service
            android:name=".course.service.BoundedServiceMessenger"
            android:exported="true"
            android:process=":remote" />
        <service
            android:name=".course.service.BoundedServiceAIDLBasic"
            android:exported="true"
            android:process=":remote" />
        <service
            android:name=".course.service.BoundedServiceAidlObject"
            android:exported="true" />

        <provider
            android:name=".course.database.AppDbContentProvider"
            android:authorities="com.example.androidtraining.provider.AppDbContentProvider"
            android:enabled="true"
            android:exported="true"
            android:permission="com.example.androidtraining.provider.AppDbContentProvider.permission.READWRITE" />
    </application>

</manifest>