package com.example.androidtraining.course.service;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.IBinder;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.example.androidtraining.utils.Logger;

import java.lang.ref.WeakReference;

public class BoundedServiceLocal extends Service {
    private static final String TAG = BoundedServiceLocal.class.getSimpleName();
    public class LocalBinder extends Binder { // Binder implement IBinder
        BoundedServiceLocal getService() {
            return BoundedServiceLocal.this;
        }
    }
    private final IBinder mBinder = new LocalBinder();
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Logger.d(TAG, "onBind");
        // TODO: Return the communication channel to the service.
        return mBinder;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.d(TAG, "onCreate thread=" + Thread.currentThread().getName());
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.d(TAG, "onStartCommand");
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public boolean onUnbind(Intent intent) {
        Logger.d(TAG, "onUnbind");
        if (mThread != null && mThread.isAlive()) {
            try {
                mThread.interrupt();
            } catch (Exception e) {
                Logger.d(TAG, "onUnbind, ex=" + e);
            }
            mIsRunning = false;
        }
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy, thread=" + Thread.currentThread().getName());
    }

    private Thread mThread = null;
    private WeakReference<ServiceCallback> mServiceCallbackRef; // to avoid Leak memory
    private int mCount = 0;
    private boolean mIsRunning = false;

    public void makeCount() {
        Toast.makeText(this, "BoundedService is running", Toast.LENGTH_LONG).show();
        Logger.d(TAG, "count = " + mCount + " thread=" + Thread.currentThread().getName() + " isRunning=" + mIsRunning);
        if (mIsRunning) return;

        // TODO
        mThread = new Thread(() -> {
            mIsRunning = true;
            for (int i = 0; i < 300; i++) { // 20 seconds
                mCount++;
                Logger.d(TAG, "count = " + mCount + " thread=" + Thread.currentThread().getName() + " isRunning=" + mIsRunning);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                // send counting to activity - using WeakReference to prevent memory leaks
                ServiceCallback callback = mServiceCallbackRef != null ? mServiceCallbackRef.get() : null;
                if (callback != null) callback.onCounting(mCount);
                if (!mIsRunning) break;
            }
            // send count done to activity - using WeakReference to prevent memory leaks
            ServiceCallback callback = mServiceCallbackRef != null ? mServiceCallbackRef.get() : null;
            if (callback != null) callback.onCountDone(mCount);
        });
        mThread.start();
    }

    public void setServiceCallback(ServiceCallback mServiceCallback) {
        this.mServiceCallbackRef = mServiceCallback != null ? new WeakReference<>(mServiceCallback) : null;
    }

    public interface ServiceCallback {
        void onCounting(int count);
        void onCountDone(int count);
    }
}
