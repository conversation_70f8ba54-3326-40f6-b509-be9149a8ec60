package com.example.androidtraining.course.notification;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.RemoteViews;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.NotificationCompat;

import com.example.androidtraining.R;

public class CustomActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_custom);

        Button btnDefault = findViewById(R.id.btn_noti_default);
        Button btnCustom = findViewById(R.id.btn_noti_custom);

        btnDefault.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    showDefaultNotification();
                }
            }
        });

        btnCustom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    showCustomNotification();
                }
            }
        });
    }

    String CHANEL_ID = "My chanel";
    String CHANEL_NAME = "MY_NOTIFICATION";
    int NOTI_ID = 1;
    @RequiresApi(api = Build.VERSION_CODES.O)
    private void showDefaultNotification() {
        // 0. Create channel
        NotificationChannel channel = new NotificationChannel(CHANEL_ID, CHANEL_NAME, NotificationManager.IMPORTANCE_DEFAULT);
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        notificationManager.createNotificationChannel(channel);
        // 1. Builder -> item + action
        Intent intent = new Intent(this, NotificationActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANEL_ID);
        builder.setSmallIcon(R.drawable.ic_launcher_background)
//                .setLargeIcon(BitmapFactory.decodeResource(getResources(), R.drawable.ic_launcher_background))
                .setContentTitle("Default Notification")
                .setContentText("This is a default notification")
                .setContentIntent(pendingIntent); // action when user tap notification
        // 2. Create noti = Builder.build()
        Notification notification = builder.build();
        // 3. show noti = NotificationManager
        notificationManager.notify(NOTI_ID, notification);
    }
    @RequiresApi(api = Build.VERSION_CODES.O)
    private void showCustomNotification() {
        // 0. Create channel
        NotificationChannel channel = new NotificationChannel(CHANEL_ID, CHANEL_NAME, NotificationManager.IMPORTANCE_DEFAULT);
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        notificationManager.createNotificationChannel(channel);
        // 1. Builder -> item + action
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0,
                new Intent(this, NotificationActivity.class), PendingIntent.FLAG_IMMUTABLE);

        RemoteViews remoteView = new RemoteViews(getPackageName(), R.layout.notification_layout);
        remoteView.setOnClickPendingIntent(R.id.btn_activity, pendingIntent);
        remoteView.setTextViewText(R.id.noti_tv_content, "This is a custom notification");
        remoteView.setTextViewText(R.id.noti_tv_title, "Custom Notification title");

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANEL_ID);
        builder.setSmallIcon(R.drawable.ic_launcher_background)
//                .setContentTitle("Custom Notification")
//                .setContentText("This is a custom notification")
//                .setContent(remoteView);
//                .setCustomContentView(remoteView);
                .setCustomBigContentView(remoteView);

        Notification notification = builder.build();
        // 3. show noti = NotificationManager
        notificationManager.notify(NOTI_ID, notification);
    }

}
