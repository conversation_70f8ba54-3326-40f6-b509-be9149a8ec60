<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.viewlayout.ViewLayoutActivity">

    <!--    GuideLine   -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_h_60"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.6"
        />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_v_40"
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.4" />

    <Button
        android:id="@+id/btn1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Button 11111 1111111 11111"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <!--    Barrier   -->
    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="right"
        app:constraint_referenced_ids="btn1"
        />

    <Button
        android:id="@+id/btn2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Button 222222"
        app:layout_constraintStart_toEndOf="@id/gl_v_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/gl_h_60"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:background="@color/design_default_color_secondary"
        app:layout_constraintStart_toEndOf="@id/gl_v_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="50dp"
        />

    <Button
        android:id="@+id/btn3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Button 3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/barrier1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <!--    Chain   -->
    <Button
        android:id="@+id/but1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="But 1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/but2"
        app:layout_constraintTop_toBottomOf="@id/btn1"
        app:layout_constraintBottom_toTopOf="@id/gl_h_60"
        app:layout_constraintHorizontal_chainStyle="spread"
        />

    <Button
        android:id="@+id/but2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="But 2"
        app:layout_constraintStart_toEndOf="@+id/but1"
        app:layout_constraintEnd_toStartOf="@+id/but3"
        app:layout_constraintTop_toBottomOf="@id/btn1"
        app:layout_constraintBottom_toTopOf="@id/gl_h_60"
        />

    <Button
        android:id="@+id/but3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="But 3"
        app:layout_constraintStart_toEndOf="@+id/but2"
        app:layout_constraintEnd_toStartOf="@id/but7"
        app:layout_constraintTop_toBottomOf="@id/btn1"
        app:layout_constraintBottom_toTopOf="@id/gl_h_60"
        />

    <Button
        android:id="@+id/but7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="But 7"
        app:layout_constraintStart_toEndOf="@+id/but3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn1"
        app:layout_constraintBottom_toTopOf="@id/gl_h_60"
        />

    <!--    Chain with weight: need define group   -->
    <Button
        android:id="@+id/but4"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="But 4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/but5"
        app:layout_constraintTop_toBottomOf="@id/gl_h_60"
        app:layout_constraintHorizontal_weight="1"
        />

    <Button
        android:id="@+id/but5"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="But 5"
        android:backgroundTint="@color/design_default_color_error"
        app:layout_constraintStart_toEndOf="@+id/but4"
        app:layout_constraintEnd_toStartOf="@+id/but6"
        app:layout_constraintTop_toBottomOf="@id/gl_h_60"
        app:layout_constraintHorizontal_weight="2"
        />

    <Button
        android:id="@+id/but6"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="But 6"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/but5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gl_h_60"
        />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="but4,but5,but6" />

<!--    FrameLayout: using layout_gravity to modify layout -->
    <FrameLayout
        android:id="@+id/frame_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/material_dynamic_neutral40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn3"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn1">

        <Button
            android:id="@+id/btn_frame"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Button frame"
            android:layout_gravity="bottom|center"
            tools:ignore="DuplicateIds" />

        <TextView
            android:id="@+id/tv_frame"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Frame Layout here, background grey"
            android:textColor="@color/design_default_color_error"
            android:layout_gravity="center_horizontal|center_vertical"
            />

        <TextView
            tools:text="Item of text view frame"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/ic_arrow"
            android:drawableStart="@drawable/ic_arrow"
            android:gravity="center_horizontal"
            android:padding="16dp"
            />
    </FrameLayout>

    <!--    Include layout -->
<!--    <include layout="@layout/view_layout_include" />-->
    <include layout="@layout/view_layout_include"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/but5"
        app:layout_constraintBottom_toBottomOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>