package com.example.androidtraining.course.service;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.IRemoteServiceInterface;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;
import com.example.androidtraining.course.model.MsgObject;

public class ServiceActivity extends BaseActivity implements BoundedServiceLocal.ServiceCallback {
    private static final String TAG = ServiceActivity.class.getSimpleName();

    private TextView mCountText;
    BroadcastReceiver countDownBroadcastReceiver = new BroadcastReceiver() {
        @SuppressLint("SetTextI18n")
        @Override
        public void onReceive(Context context, Intent intent) {
            int count = intent.getIntExtra("count", -1);
            Logger.d(TAG, "onReceive: count=" + count + ", thread=" + Thread.currentThread().getName());
            mCountText.setText("Count value is: " + count);
        }
    };

    // Bounded service local
    private BoundedServiceLocal mBoundedServiceLocal;
    private boolean mIsBoundBoundedServiceLocal = false;
    // Defines callbacks for service binding
    private final ServiceConnection mBoundServiceLocalConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Logger.d(TAG, "onServiceConnected: mBoundServiceConnection");
            try {
                BoundedServiceLocal.LocalBinder myBinder = (BoundedServiceLocal.LocalBinder) service;
                mBoundedServiceLocal = myBinder.getService();
                mBoundedServiceLocal.setServiceCallback(ServiceActivity.this);
                mIsBoundBoundedServiceLocal = true;
                // ************************* TO DO *************************
                mBoundedServiceLocal.makeCount(); // public method
            } catch (Exception e) {
                Logger.d(TAG, "onServiceConnected: mBoundServiceConnection, cast binder ex=" + e);
                mIsBoundBoundedServiceLocal = false;
            }
        }
        @Override
        public void onServiceDisconnected(ComponentName name) {
            Logger.d(TAG, "onServiceDisconnected: mBoundServiceConnection");
            mBoundedServiceLocal = null;
            mIsBoundBoundedServiceLocal = false;
        }
    };
    ///////////////////////////////////////////////////////////////////////////////

    // Bounded service implement messenger
    /*
    4. Clients use the IBinder to instantiate the Messenger (that references the service's Handler),
            which the client uses to send Message objects to the service.
    5. The service receives each Message in its Handler—specifically, in the handleMessage() method
    */
    public static final int MSG_REPLY_MESSAGE_FROM_SERVICE = 21;
    public static final int MSG_REPLY_TASK_FROM_SERVICE = 22;
    private final Handler clientHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            Logger.d(TAG, "handleMessage: what=" + msg.what);
            switch (msg.what) {
                case MSG_REPLY_MESSAGE_FROM_SERVICE:
                    Toast.makeText(ServiceActivity.this, "reply message from service: " + msg.arg1, Toast.LENGTH_SHORT).show();
                    break;
                case MSG_REPLY_TASK_FROM_SERVICE:
                    Toast.makeText(ServiceActivity.this, "reply task from service: " + msg.arg1, Toast.LENGTH_SHORT).show();
                    break;
                default:
                    super.handleMessage(msg);
            }
        }
    };
    private final Messenger mClientMessengerReply = new Messenger(clientHandler);
    private Messenger mClientMessenger = null;
    private boolean mIsBoundServiceMessenger = false;
    private final ServiceConnection mBoundServiceMessengerConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Logger.d(TAG, "onServiceConnected: mBoundedServiceMessenger");
            mClientMessenger = new Messenger(service);
            mIsBoundServiceMessenger = true;
            // TODO: sayHello to service, can use mClientMessenger in other methods
            try {
                Message message = new Message();
                message.what = BoundedServiceMessenger.MSG_SAY_HELLO; // same for MSG_GOOD_BYE_FROM_OTHER_APP
                message.replyTo = mClientMessengerReply;
                Bundle bundle = new Bundle();
                bundle.putString("name", "AndroidTraining");
                MsgObject msg = new MsgObject(1, "User say hello messenger");
                bundle.putParcelable("msg", msg);
                message.setData(bundle);
                mClientMessenger.send(message);
                Logger.d(TAG, "onServiceConnected: send(message)");
            } catch (Exception | Error e ) {
                Logger.d(TAG, "onServiceConnected: mBoundedServiceMessenger, send message ex=" + e);
            }
        }
        @Override
        public void onServiceDisconnected(ComponentName name) {
            Logger.d(TAG, "onServiceDisconnected: mBoundedServiceMessenger");
            mClientMessenger = null;
            mIsBoundServiceMessenger = false;
        }
    };
    ///////////////////////////////////////////////////////////////////////////////

    // Bounded service implement AIDL
    private IRemoteServiceInterface mIRemoteServiceAidl;
    private boolean mIsBoundServiceAIDL = false;
    private final ServiceConnection mBoundServiceAIDLConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Logger.d(TAG, "onServiceConnected: mBoundServiceAIDLConnection");
            mIRemoteServiceAidl = IRemoteServiceInterface.Stub.asInterface(service);
            mIsBoundServiceAIDL = true;
            try {
                int servicePid = mIRemoteServiceAidl.getPid();
                String pkgName = mIRemoteServiceAidl.getPackageName();
                Bundle bundle = new Bundle();
                MsgObject msg = new MsgObject(2, "User say hello aidl");
                bundle.putParcelable("msg", msg);
                mIRemoteServiceAidl.sendMessageBundle(bundle);
                Logger.d(TAG, "onServiceConnected: mBoundServiceAIDLConnection, servicePid=" + servicePid + ", pkgName=" + pkgName);
            } catch (RemoteException e) {
                Logger.d(TAG, "onServiceConnected: mBoundServiceAIDLConnection, get servicePid ex=" + e);
            }
        }
        @Override
        public void onServiceDisconnected(ComponentName name) {
            Logger.d(TAG, "onServiceDisconnected: mBoundServiceAIDLConnection");
            mIRemoteServiceAidl = null;
            mIsBoundServiceAIDL = false;
        }
    };
    ///////////////////////////////////////////////////////////////////////////////

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @SuppressLint("SetTextI18n")
        @Override
        public void handleMessage(@NonNull Message msg) {
            Logger.d(TAG, "handleMessage: what=" + msg.what + "count=" + msg.arg1);
            int count = msg.arg1;
            switch (msg.what) {
                case MSG_COUNTING:
                    mCountText.setText("Count value is: " + count);
                    break;
                case MSG_COUNT_DONE:
                    Toast.makeText(ServiceActivity.this, "count done, count = " + count, Toast.LENGTH_SHORT).show();
                    break;
                default:
                    break;
            }
        }
    };

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    @RequiresApi(api = Build.VERSION_CODES.P)
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_service;
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate: currentThread=" + Thread.currentThread().getName());

        mCountText = findViewById(R.id.tv_count);
        // 1. unbounded service: count down done -> send broad cast to pass data
        Button btnStartUnboundedService = findViewById(R.id.btn_start_unbounded_service);
        btnStartUnboundedService.setOnClickListener(v -> {
            Logger.d(TAG, "btnStartUnboundedService: startService=");
            Intent intentService = new Intent(ServiceActivity.this, ForegroundService.class);
            startService(intentService);
            v.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
        });

        Button btnStopUnboundedService = findViewById(R.id.btn_stop_unbounded_service);
        btnStopUnboundedService.setOnClickListener(v -> {
            Logger.d(TAG, "btnStopUnboundedService: stopService=");
            stopService(new Intent(ServiceActivity.this, ForegroundService.class));
        });

        IntentFilter intentFilter = new IntentFilter(ForegroundService.ACTION_SEND_COUNT);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(countDownBroadcastReceiver, intentFilter, RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(countDownBroadcastReceiver, intentFilter);
        }

        // 2. bounded service local: count down done -> use call back to pass data
        Button startBoundedServiceLocal = findViewById(R.id.btn_start_bounded_service_local);
        startBoundedServiceLocal.setOnClickListener(v -> {
            Logger.d(TAG, "startBoundedServiceLocal: bindService=");
            Intent intent = new Intent(ServiceActivity.this, BoundedServiceLocal.class);
            bindService(intent, mBoundServiceLocalConnection, Context.BIND_AUTO_CREATE);
        });

        Button stopBoundedServiceLocal = findViewById(R.id.btn_stop_bounded_service_local);
        stopBoundedServiceLocal.setOnClickListener(v -> {
            Logger.d(TAG, "stopBoundedServiceLocal: unbindService, isBound=" + mIsBoundBoundedServiceLocal);
            if (mIsBoundBoundedServiceLocal) unbindService(mBoundServiceLocalConnection);
        });

        // 3. bounded service messenger
        Button startBoundedServiceMessenger = findViewById(R.id.btn_start_bounded_service_messenger);
        startBoundedServiceMessenger.setOnClickListener(v -> {
            Logger.d(TAG, "startBoundedServiceMessenger: bindService=");
            Intent intent = new Intent(ServiceActivity.this, BoundedServiceMessenger.class);
            bindService(intent, mBoundServiceMessengerConnection, Context.BIND_AUTO_CREATE);
        });

        Button stopBoundedServiceMessenger = findViewById(R.id.btn_stop_bounded_service_messenger);
        stopBoundedServiceMessenger.setOnClickListener(v -> {
            Logger.d(TAG, "stopBoundedServiceMessenger: unbindService, isBound=" + mIsBoundServiceMessenger);
            if (mIsBoundServiceMessenger) unbindService(mBoundServiceMessengerConnection);
        });

        // 4. bounded service AIDL
        Button startBoundedServiceAidl = findViewById(R.id.btn_start_bounded_service_aidl);
        startBoundedServiceAidl.setOnClickListener(v -> {
            Logger.d(TAG, "startBoundedServiceAidl: bindService=");
            Intent intent = new Intent(ServiceActivity.this, BoundedServiceAIDLBasic.class);
            bindService(intent, mBoundServiceAIDLConnection, Context.BIND_AUTO_CREATE);
        });

        Button stopBoundedServiceAidl = findViewById(R.id.btn_stop_bounded_service_aidl);
        stopBoundedServiceAidl.setOnClickListener(v -> {
            Logger.d(TAG, "stopBoundedServiceAidl: unbindService, isBoundServiceAIDL=" + mIsBoundServiceAIDL);
            if (mIsBoundServiceAIDL) unbindService(mBoundServiceAIDLConnection);
        });
    }


    private static final int MSG_COUNTING = 1;
    private static final int MSG_COUNT_DONE = 2;
    @Override
    public void onCounting(int count) { // same thread of new thread in service
        Logger.d(TAG, "onCounting, count = " + count + " thread=" + Thread.currentThread().getName());
        Message message = new Message();
        message.what = MSG_COUNTING;
        message.arg1 = count;
        mHandler.sendMessage(message);
    }

    @Override
    public void onCountDone(int count) {
        Logger.d(TAG, "onCountDone, count = " + count + " thread=" + Thread.currentThread().getName());
        Message message = new Message();
        message.what = MSG_COUNT_DONE;
        message.arg1 = count;
        mHandler.sendMessage(message);
    }

    @Override
    protected void onDestroy() {
        Logger.d(TAG, "onDestroy");
        unregisterReceiver(countDownBroadcastReceiver);
        if (mBoundedServiceLocal != null) mBoundedServiceLocal.setServiceCallback(null);
        if (mIsBoundBoundedServiceLocal) unbindService(mBoundServiceLocalConnection);
        super.onDestroy();
    }
}