package com.example.androidtraining.course.service;

import android.app.Service;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Messenger;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.course.model.MsgObject;

public class BoundedServiceMessenger extends Service {
/*
    1. The service implements a Handler that receives a callback for each call from a client.
    2. The service uses the Handler to create a Messenger object (which is a reference to the Handler).
    3. The Messenger creates an IBinder that the service returns to clients from onBind().
    4. Clients use the IBinder to instantiate the Messenger (that references the service's Handler),
    which the client uses to send Message objects to the service.
    5. The service receives each Message in its Handler—specifically, in the handleMessage() method.
*/

    private static final String TAG = BoundedServiceMessenger.class.getSimpleName();
    public static final int MSG_SAY_HELLO = 1;
    public static final int MSG_GOOD_BYE_FROM_OTHER_APP = 3;
    public static final int MSG_REPLY_MESSAGE_TO_OTHER_APP = 21;
    public static final int MSG_REPLY_TASK_TO_OTHER_APP = 22;

    /*
    1. The service implements a Handler that receives a callback for each call from a client.
    2. The service uses the Handler to create a Messenger object (which is a reference to the Handler).
    3. The Messenger creates an IBinder that the service returns to clients from onBind().
     */
    private static Messenger mServerMessenger;
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Logger.d(TAG, "onBind");
        mServerMessenger = new Messenger(mServerHandler);
        return mServerMessenger.getBinder();
    }
    private final Handler mServerHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            Logger.d(TAG, "handleMessage, what=" + msg.what + ", thread=" + Thread.currentThread().getName());
            switch (msg.what) {
                case MSG_SAY_HELLO:
                    Toast.makeText(BoundedServiceMessenger.this, "Hello from service", Toast.LENGTH_SHORT).show();
                    Bundle bundle = msg.getData();
                    bundle.setClassLoader(getClass().getClassLoader());
                    String name = bundle.getString("name");
                    MsgObject mess = bundle.getParcelable("msg");
                    Logger.d(TAG, "handleMessage, MSG_SAY_HELLO: name=" + name + ", mess=" + mess);
                    if (msg.replyTo != null) {
                        try {
                            Message replyMess = Message.obtain(null, MSG_REPLY_MESSAGE_TO_OTHER_APP, 0, 0);
                            mess = new MsgObject(5, "Reply from Service");
                            bundle = new Bundle();
                            bundle.putParcelable("mess", mess);
                            replyMess.setData(bundle);
                            msg.replyTo.send(replyMess);
                        } catch (Exception e) {
                            Logger.e(TAG, "handleMessage, MSG_SAY_HELLO: replyTo.send error: " + e);
                        }
                    }
                    break;
                case MSG_GOOD_BYE_FROM_OTHER_APP: // send MSG_REPLY_TASK_FROM_SERVICE
                    Toast.makeText(BoundedServiceMessenger.this, "Say goodbye from other app", Toast.LENGTH_SHORT).show();
                    break;
                default:
                    super.handleMessage(msg);
            }
        }
    };
    //////////////////////////////////////////////////////////////////////////////

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.d(TAG, "onCreate");
    }

    @Override
    public boolean onUnbind(Intent intent) {
        Logger.d(TAG, "onUnbind");
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy");
    }
}
