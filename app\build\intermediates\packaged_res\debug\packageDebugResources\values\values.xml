<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#000000</color>
    <color name="gradient_end">#80000000</color>
    <color name="gradient_start">#00000000</color>
    <color name="light_gray">#F5F5F5</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="red">#F44336</color>
    <color name="selection_overlay">#4D2196F3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="transparent">#00000000</color>
    <color name="white">#FFFFFF</color>
    <dimen name="btn_margin_top">20dp</dimen>
    <string name="app_name">AndroidTraining</string>
    <string name="firebase_database_url" translatable="false">https://apptest-53581-default-rtdb.asia-southeast1.firebasedatabase.app</string>
    <string name="gcm_defaultSenderId" translatable="false">855608031391</string>
    <string name="google_api_key" translatable="false">AIzaSyCOU_JbOl7u7EwoYLjHFw-cUa7WrO-ghq0</string>
    <string name="google_app_id" translatable="false">1:855608031391:android:57c3c9ff8f83cd5735caaa</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyCOU_JbOl7u7EwoYLjHFw-cUa7WrO-ghq0</string>
    <string name="google_storage_bucket" translatable="false">apptest-53581.firebasestorage.app</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="project_id" translatable="false">apptest-53581</string>
    <style name="Theme.AndroidTraining" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>