package com.example.androidtraining.course.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.androidtraining.R;

import java.util.List;

public class SongAdapterRecycleView extends RecyclerView.Adapter<SongViewHolder> {
    Context mContext;
    List<MySong> mListSong;
    SongItemClickListenerInterface mItemClickListener;
    public SongAdapterRecycleView(Context context, List<MySong> listSong) {
        mContext = context;
        mListSong= listSong;
    }
    @NonNull
    @Override
    public SongViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).
                inflate(R.layout.item_list_song, parent, false);
        return new SongViewHolder(view);
    }
    @Override
    public int getItemCount() {
        return mListSong.size();
    }
    @Override
    public void onBindViewHolder(@NonNull SongViewHolder holder, @SuppressLint("RecyclerView") int position) {
        MySong song = mListSong.get(position);
        holder.songIcon.setImageResource(song.getResId());
        holder.songName.setText(song.getName());
        holder.songAuthor.setText(song.getAuthor());
        holder.songTime.setText(song.getTime());
        holder.songIcon.setOnClickListener(v -> {
            if (mItemClickListener != null) mItemClickListener.onItemClick(v, position);
        });
        holder.nextSongIcon.setOnClickListener(v -> {
            if (mItemClickListener != null) mItemClickListener.onNextSongClick(v, position);
        });
        holder.itemView.setOnLongClickListener(v -> {
            if (mItemClickListener != null) {
                mItemClickListener.onItemLongClick(v, position);
                return true;
            }
            return false;
        });
    }
    public void setItemClickListener(SongItemClickListenerInterface listener) {
        mItemClickListener = listener;
    }
}

class SongViewHolder extends RecyclerView.ViewHolder {
    ImageView songIcon, nextSongIcon;
    TextView songName, songAuthor, songTime;
    View itemView;
    public SongViewHolder(@NonNull View view) {
        super(view);
        songIcon = view.findViewById(R.id.img_song_icon);
        songName = view.findViewById(R.id.tv_song_name);
        songAuthor = view.findViewById(R.id.tv_song_author);
        songTime = view.findViewById(R.id.tv_song_time);
        nextSongIcon = view.findViewById(R.id.img_song_next);
        itemView = view.findViewById(R.id.item_song_view);
    }
}

interface SongItemClickListenerInterface {
    void onItemClick(View view, int position);
    void onItemLongClick(View view, int position);
    void onNextSongClick(View view, int position);
}


