package com.example.androidtraining.course.model;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

public class Cards implements Parcelable {
    int code;
    String msg;

    public Cards() {
        code = 0;
        msg = "";
    }

    public Cards(Parcel in) {
        readFromParcel(in);
    }

    public void readFromParcel(Parcel in) {
        code = in.readInt();
        msg = in.readString();
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeInt(code);
        dest.writeString(msg);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Cards> CREATOR = new Creator<Cards>() {
        @Override
        public Cards createFromParcel(Parcel in) {
            return new Cards(in);
        }

        @Override
        public Cards[] newArray(int size) {
            return new Cards[size];
        }
    };
}
