com.example.androidtraining.app-jetified-sqlite-release-0 C:\Users\<USER>\.gradle\caches\8.9\transforms\030f01bde32e862e7b260d5da82b40c5\transformed\jetified-sqlite-release\res
com.example.androidtraining.app-jetified-credentials-play-services-auth-1.2.0-rc01-1 C:\Users\<USER>\.gradle\caches\8.9\transforms\055202483e5b3fb3d26c4d011b292e0c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.androidtraining.app-lifecycle-runtime-2.6.2-2 C:\Users\<USER>\.gradle\caches\8.9\transforms\05e45d105d77df015656767c8af01e47\transformed\lifecycle-runtime-2.6.2\res
com.example.androidtraining.app-jetified-play-services-basement-18.4.0-3 C:\Users\<USER>\.gradle\caches\8.9\transforms\080c78dea5e8cf5cc3e1cd266282ad7e\transformed\jetified-play-services-basement-18.4.0\res
com.example.androidtraining.app-core-1.16.0-4 C:\Users\<USER>\.gradle\caches\8.9\transforms\095e20247a4fac85d303d824da9b477b\transformed\core-1.16.0\res
com.example.androidtraining.app-constraintlayout-2.2.1-5 C:\Users\<USER>\.gradle\caches\8.9\transforms\15aaf3da08b3d512d9178f82413ab096\transformed\constraintlayout-2.2.1\res
com.example.androidtraining.app-transition-1.5.0-6 C:\Users\<USER>\.gradle\caches\8.9\transforms\167d8eff42670dcdd87125be87ce63f2\transformed\transition-1.5.0\res
com.example.androidtraining.app-jetified-core-common-2.0.3-7 C:\Users\<USER>\.gradle\caches\8.9\transforms\2160d4f6b58cf79ed05103e1c9204437\transformed\jetified-core-common-2.0.3\res
com.example.androidtraining.app-lifecycle-viewmodel-2.6.2-8 C:\Users\<USER>\.gradle\caches\8.9\transforms\22a23d5782e115a58cea869d8460fccf\transformed\lifecycle-viewmodel-2.6.2\res
com.example.androidtraining.app-jetified-tracing-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.9\transforms\5c727366b9fa147a4c2c3bee96b96386\transformed\jetified-tracing-1.2.0\res
com.example.androidtraining.app-appcompat-1.7.1-10 C:\Users\<USER>\.gradle\caches\8.9\transforms\5c8c625da1180ada1c0b706ea323ef75\transformed\appcompat-1.7.1\res
com.example.androidtraining.app-jetified-lifecycle-viewmodel-ktx-2.6.2-11 C:\Users\<USER>\.gradle\caches\8.9\transforms\6644bc5edde4101dc36f6297e0524f9f\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\res
com.example.androidtraining.app-jetified-savedstate-ktx-1.2.1-12 C:\Users\<USER>\.gradle\caches\8.9\transforms\6efa72cbfe9c982438830af458d24635\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.androidtraining.app-jetified-emoji2-1.3.0-13 C:\Users\<USER>\.gradle\caches\8.9\transforms\70fb0731d49f0f40a2e251129eafd12c\transformed\jetified-emoji2-1.3.0\res
com.example.androidtraining.app-coordinatorlayout-1.1.0-14 C:\Users\<USER>\.gradle\caches\8.9\transforms\74200d43e4c723e0fd0c582d011a17ee\transformed\coordinatorlayout-1.1.0\res
com.example.androidtraining.app-jetified-viewpager2-1.0.0-15 C:\Users\<USER>\.gradle\caches\8.9\transforms\75bfe2f3899e60cfbc41330ad2e58d16\transformed\jetified-viewpager2-1.0.0\res
com.example.androidtraining.app-browser-1.4.0-16 C:\Users\<USER>\.gradle\caches\8.9\transforms\7b93779cc1673c359b2ee116079c1e88\transformed\browser-1.4.0\res
com.example.androidtraining.app-jetified-credentials-1.2.0-rc01-17 C:\Users\<USER>\.gradle\caches\8.9\transforms\8364b6ea8f6721f3b43ad2063df88cca\transformed\jetified-credentials-1.2.0-rc01\res
com.example.androidtraining.app-jetified-appcompat-resources-1.7.1-18 C:\Users\<USER>\.gradle\caches\8.9\transforms\860cab9d1e947c673753f0856748b0cf\transformed\jetified-appcompat-resources-1.7.1\res
com.example.androidtraining.app-jetified-activity-ktx-1.10.1-19 C:\Users\<USER>\.gradle\caches\8.9\transforms\8635127085201a3ca7d5ffceac78f405\transformed\jetified-activity-ktx-1.10.1\res
com.example.androidtraining.app-jetified-lifecycle-viewmodel-savedstate-2.6.2-20 C:\Users\<USER>\.gradle\caches\8.9\transforms\902381dd44b2dd95da110287cd344738\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\res
com.example.androidtraining.app-jetified-startup-runtime-1.1.1-21 C:\Users\<USER>\.gradle\caches\8.9\transforms\93890d9b0e1d22f1358591dcfbe288d7\transformed\jetified-startup-runtime-1.1.1\res
com.example.androidtraining.app-jetified-play-services-auth-20.7.0-22 C:\Users\<USER>\.gradle\caches\8.9\transforms\95b6837682eac330ae0a12132ad12a8f\transformed\jetified-play-services-auth-20.7.0\res
com.example.androidtraining.app-core-runtime-2.2.0-23 C:\Users\<USER>\.gradle\caches\8.9\transforms\9c3e2c4c870a81cf00992a93b5c94cd5\transformed\core-runtime-2.2.0\res
com.example.androidtraining.app-jetified-profileinstaller-1.4.0-24 C:\Users\<USER>\.gradle\caches\8.9\transforms\9e39773335d66a0a729e86e5d7333903\transformed\jetified-profileinstaller-1.4.0\res
com.example.androidtraining.app-jetified-annotation-experimental-1.4.1-25 C:\Users\<USER>\.gradle\caches\8.9\transforms\a1d1989a44c7d4b3849aaf3397d9c24f\transformed\jetified-annotation-experimental-1.4.1\res
com.example.androidtraining.app-jetified-room-runtime-release-26 C:\Users\<USER>\.gradle\caches\8.9\transforms\a827a7569734c096f6c51754843b6f79\transformed\jetified-room-runtime-release\res
com.example.androidtraining.app-recyclerview-1.1.0-27 C:\Users\<USER>\.gradle\caches\8.9\transforms\b5dddf2d9118e04d8071a5b8bdc9308f\transformed\recyclerview-1.1.0\res
com.example.androidtraining.app-jetified-savedstate-1.2.1-28 C:\Users\<USER>\.gradle\caches\8.9\transforms\b5f2623d35bfa0053a836107faab3e3c\transformed\jetified-savedstate-1.2.1\res
com.example.androidtraining.app-drawerlayout-1.1.1-29 C:\Users\<USER>\.gradle\caches\8.9\transforms\b8bf90a461fcdcd51d6afc2bbb42af87\transformed\drawerlayout-1.1.1\res
com.example.androidtraining.app-jetified-core-viewtree-1.0.0-30 C:\Users\<USER>\.gradle\caches\8.9\transforms\ba3a64b289d2e42777b62991745afd02\transformed\jetified-core-viewtree-1.0.0\res
com.example.androidtraining.app-jetified-lifecycle-process-2.6.2-31 C:\Users\<USER>\.gradle\caches\8.9\transforms\bfc8dafba1ab47ce24cc0ddd91aa7655\transformed\jetified-lifecycle-process-2.6.2\res
com.example.androidtraining.app-cardview-1.0.0-32 C:\Users\<USER>\.gradle\caches\8.9\transforms\bfff2de8d1c74f4ead3f5a24e42559e5\transformed\cardview-1.0.0\res
com.example.androidtraining.app-fragment-1.5.4-33 C:\Users\<USER>\.gradle\caches\8.9\transforms\c0ed5c1d950961effbfd7cf38a02d505\transformed\fragment-1.5.4\res
com.example.androidtraining.app-jetified-activity-1.10.1-34 C:\Users\<USER>\.gradle\caches\8.9\transforms\c7103564c96379cf73d6e4d1b07b6f0c\transformed\jetified-activity-1.10.1\res
com.example.androidtraining.app-jetified-play-services-base-18.1.0-35 C:\Users\<USER>\.gradle\caches\8.9\transforms\cc9cb779b45efc5b219ad065a80ba44f\transformed\jetified-play-services-base-18.1.0\res
com.example.androidtraining.app-lifecycle-livedata-2.6.2-36 C:\Users\<USER>\.gradle\caches\8.9\transforms\d1945ff853801908739fdd6fb0bac51c\transformed\lifecycle-livedata-2.6.2\res
com.example.androidtraining.app-lifecycle-livedata-core-2.6.2-37 C:\Users\<USER>\.gradle\caches\8.9\transforms\d444e2a74dfac958e2f51a5169550d23\transformed\lifecycle-livedata-core-2.6.2\res
com.example.androidtraining.app-jetified-core-ktx-1.16.0-38 C:\Users\<USER>\.gradle\caches\8.9\transforms\d48b79e3702a445c6ef4c97626749e8c\transformed\jetified-core-ktx-1.16.0\res
com.example.androidtraining.app-jetified-sqlite-framework-release-39 C:\Users\<USER>\.gradle\caches\8.9\transforms\d7647d3466ef1c5b60ca7feeddc51b9f\transformed\jetified-sqlite-framework-release\res
com.example.androidtraining.app-jetified-emoji2-views-helper-1.3.0-40 C:\Users\<USER>\.gradle\caches\8.9\transforms\de75c04b76aea53f7ca7b79b6726161d\transformed\jetified-emoji2-views-helper-1.3.0\res
com.example.androidtraining.app-jetified-firebase-common-21.0.0-41 C:\Users\<USER>\.gradle\caches\8.9\transforms\ebaf7669bef1dc814ae55543e68f6c0f\transformed\jetified-firebase-common-21.0.0\res
com.example.androidtraining.app-jetified-lifecycle-runtime-ktx-2.6.2-42 C:\Users\<USER>\.gradle\caches\8.9\transforms\f92ec96ec54a022a41ba38d2fc641dec\transformed\jetified-lifecycle-runtime-ktx-2.6.2\res
com.example.androidtraining.app-material-1.12.0-43 C:\Users\<USER>\.gradle\caches\8.9\transforms\f94bbac8af5cebd3e2fad304c9f7117d\transformed\material-1.12.0\res
com.example.androidtraining.app-pngs-44 D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\pngs\release
com.example.androidtraining.app-res-45 D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\processReleaseGoogleServices
com.example.androidtraining.app-resValues-46 D:\AndroidStudioProjects\AndroidTraining\app\build\generated\res\resValues\release
com.example.androidtraining.app-packageReleaseResources-47 D:\AndroidStudioProjects\AndroidTraining\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.example.androidtraining.app-packageReleaseResources-48 D:\AndroidStudioProjects\AndroidTraining\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.example.androidtraining.app-release-49 D:\AndroidStudioProjects\AndroidTraining\app\build\intermediates\merged_res\release\mergeReleaseResources
com.example.androidtraining.app-main-50 D:\AndroidStudioProjects\AndroidTraining\app\src\main\res
com.example.androidtraining.app-release-51 D:\AndroidStudioProjects\AndroidTraining\app\src\release\res
