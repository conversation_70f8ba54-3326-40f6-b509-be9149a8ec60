/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.example.androidtraining;
public interface ICardsSeviceInterface extends android.os.IInterface
{
  /** Default implementation for ICardsSeviceInterface. */
  public static class Default implements com.example.androidtraining.ICardsSeviceInterface
  {
    @Override public com.example.androidtraining.course.model.Cards getCards() throws android.os.RemoteException
    {
      return null;
    }
    @Override public void sendCards(com.example.androidtraining.course.model.Cards cards) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.example.androidtraining.ICardsSeviceInterface
  {
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.example.androidtraining.ICardsSeviceInterface interface,
     * generating a proxy if needed.
     */
    public static com.example.androidtraining.ICardsSeviceInterface asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.example.androidtraining.ICardsSeviceInterface))) {
        return ((com.example.androidtraining.ICardsSeviceInterface)iin);
      }
      return new com.example.androidtraining.ICardsSeviceInterface.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      if (code >= android.os.IBinder.FIRST_CALL_TRANSACTION && code <= android.os.IBinder.LAST_CALL_TRANSACTION) {
        data.enforceInterface(descriptor);
      }
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
      }
      switch (code)
      {
        case TRANSACTION_getCards:
        {
          com.example.androidtraining.course.model.Cards _result = this.getCards();
          reply.writeNoException();
          _Parcel.writeTypedObject(reply, _result, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
          break;
        }
        case TRANSACTION_sendCards:
        {
          com.example.androidtraining.course.model.Cards _arg0;
          _arg0 = _Parcel.readTypedObject(data, com.example.androidtraining.course.model.Cards.CREATOR);
          this.sendCards(_arg0);
          reply.writeNoException();
          break;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
      return true;
    }
    private static class Proxy implements com.example.androidtraining.ICardsSeviceInterface
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public com.example.androidtraining.course.model.Cards getCards() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        com.example.androidtraining.course.model.Cards _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getCards, _data, _reply, 0);
          _reply.readException();
          _result = _Parcel.readTypedObject(_reply, com.example.androidtraining.course.model.Cards.CREATOR);
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public void sendCards(com.example.androidtraining.course.model.Cards cards) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _Parcel.writeTypedObject(_data, cards, 0);
          boolean _status = mRemote.transact(Stub.TRANSACTION_sendCards, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
    }
    static final int TRANSACTION_getCards = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_sendCards = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
  }
  public static final java.lang.String DESCRIPTOR = "com.example.androidtraining.ICardsSeviceInterface";
  public com.example.androidtraining.course.model.Cards getCards() throws android.os.RemoteException;
  public void sendCards(com.example.androidtraining.course.model.Cards cards) throws android.os.RemoteException;
  /** @hide */
  static class _Parcel {
    static private <T> T readTypedObject(
        android.os.Parcel parcel,
        android.os.Parcelable.Creator<T> c) {
      if (parcel.readInt() != 0) {
          return c.createFromParcel(parcel);
      } else {
          return null;
      }
    }
    static private <T extends android.os.Parcelable> void writeTypedObject(
        android.os.Parcel parcel, T value, int parcelableFlags) {
      if (value != null) {
        parcel.writeInt(1);
        value.writeToParcel(parcel, parcelableFlags);
      } else {
        parcel.writeInt(0);
      }
    }
  }
}
