D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\file_row.xml:31: Warning: Attribute textFontWeight is only used in API level 28 and higher (current min is 23) [UnusedAttribute]
            android:textFontWeight="200"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml:45: Warning: Attribute tooltipText is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
        android:tooltipText="Name"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml:57: Warning: Attribute tooltipText is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
        android:tooltipText="Phone"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedAttribute":
   This check finds attributes set in XML files that were introduced in a
   version newer than the oldest version targeted by your application (with
   the minSdkVersion attribute).

   This is not an error; the application will simply ignore the attribute.
   However, if the attribute is important to the appearance or functionality
   of your application, you should consider finding an alternative way to
   achieve the same result with only available attributes, and then you can
   optionally create a copy of the layout in a layout-vNN folder which will be
   used on API NN or higher where you can take advantage of the newer
   attribute.

   Note: This check does not only apply to attributes. For example, some tags
   can be unused too, such as the new <tag> element in layouts introduced in
   API 21.

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:78: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="18dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:99: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="12dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:110: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="12dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:15: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="18dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_snooze.xml:14: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
         android:textSize="24dp"
         ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:28: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="20dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:40: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="20dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:65: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="20dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:78: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="20dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\file_row.xml:33: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
            android:textSize="24dp"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_one.xml:12: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="30dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_two.xml:11: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="30dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml:41: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="16dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml:52: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="12dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml:63: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="12dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml:33: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="16dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml:44: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="12dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml:56: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
        android:textSize="12dp"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:48: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
            android:textSize="12dp"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:59: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
            android:textSize="12dp"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:70: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
            android:textSize="12dp"
            ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SpUsage":
   When setting text sizes, you should normally use sp, or "scale-independent
   pixels". This is like the dp unit, but it is also scaled by the user's font
   size preference. It is recommend you use this unit when specifying font
   sizes, so they will be adjusted for both the screen density and the user's
   preference.

   There are cases where you might need to use dp; typically this happens when
   the text is in a container with a specific dp-size. This will prevent the
   text from spilling outside the container. Note however that this means that
   the user's font size settings are not respected, so consider adjusting the
   layout itself to be more flexible.

   https://developer.android.com/training/multiscreen/screendensities.html

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:197: Warning: Use app:drawableEndCompat instead of android:drawableEnd [UseCompatTextViewDrawableXml from androidx.appcompat]
            android:drawableEnd="@drawable/ic_arrow"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:198: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
            android:drawableStart="@drawable/ic_arrow"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseCompatTextViewDrawableXml":
   TextView uses android: compound drawable attributes instead of app: ones

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\service\ForegroundService.java:71: Error: The intent action com.example.androidtraining.SERVICE_SEND_COUNT (used to send a broadcast) matches the intent filter of a non-exported receiver, registered via a call to Context.registerReceiver, or similar. If you are trying to invoke this specific receiver via the action then you should use Intent.setPackage(<APPLICATION_ID>). [UnsafeImplicitIntentLaunch]
                    Intent intent1 = new Intent(ACTION_SEND_COUNT);
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnsafeImplicitIntentLaunch":
   This intent matches a non-exported component within the same app. In many
   cases, the app developer could instead use an explicit Intent to send
   messages to their internal components, ensuring that the messages are
   safely delivered without exposure to malicious apps on the device. Using
   such implicit intents will result in a crash in an upcoming version of
   Android.

D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:118: Warning: Exported receiver does not require permission [ExportedReceiver]
        <receiver
         ~~~~~~~~

   Explanation for issues of type "ExportedReceiver":
   Exported receivers (receivers which either set exported=true or contain an
   intent-filter and do not specify exported=false) should define a permission
   that an entity must have in order to launch the receiver or bind to it.
   Without this, any application can use this receiver.

   https://goo.gle/ExportedReceiver

D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:133: Warning: Exported service does not require permission [ExportedService]
        <service
         ~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:136: Warning: Exported service does not require permission [ExportedService]
        <service
         ~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:140: Warning: Exported service does not require permission [ExportedService]
        <service
         ~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml:144: Warning: Exported service does not require permission [ExportedService]
        <service
         ~~~~~~~

   Explanation for issues of type "ExportedService":
   Exported services (services which either set exported=true or contain an
   intent-filter and do not specify exported=false) should define a permission
   that an entity must have in order to launch the service or bind to it.
   Without this, any application can use this service.

   https://goo.gle/ExportedService

D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\MainActivity.java:96: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\MainActivity.java:135: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values-v23: Warning: This folder configuration (v23) is unnecessary; minSdkVersion is 23. Merge all the resources in this folder into values. [ObsoleteSdkInt]

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java:246: Warning: This AsyncTask class should be static or leaks might occur (com.example.androidtraining.course.threadhandler.ThreadActivity.MyAsyncTask) [StaticFieldLeak]
    class MyAsyncTask extends AsyncTask<String, Integer, Result> {
          ~~~~~~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_recycle_view.xml:6: Warning: Possible overdraw: Root element paints background @color/purple_200 with a theme that also paints a background (inferred theme is @style/Theme.AndroidTraining) [Overdraw]
    android:background="@color/purple_200"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\view_layout_include.xml:5: Warning: Possible overdraw: Root element paints background @color/design_default_color_secondary with a theme that also paints a background (inferred theme is @style/Theme.AndroidTraining) [Overdraw]
    android:background="@color/design_default_color_secondary"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values\strings.xml:4: Warning: The resource R.string.hello_blank_fragment appears to be unused [UnusedResources]
    <string name="hello_blank_fragment">Hello blank fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xxhdpi\ic_arrow.png: Warning: The following images appear both as density independent .xml files and as bitmap files: srcmainresdrawable-anydpiic_arrow.xml, srcmainresdrawable-hdpiic_arrow.png [IconXmlAndPng]
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xxhdpi\ic_next.png: Warning: The following images appear both as density independent .xml files and as bitmap files: srcmainresdrawable-anydpiic_next.xml, srcmainresdrawable-hdpiic_next.png [IconXmlAndPng]

   Explanation for issues of type "IconXmlAndPng":
   If a drawable resource appears as an .xml file in the drawable/ folder,
   it's usually not intentional for it to also appear as a bitmap using the
   same name; generally you expect the drawable XML file to define states and
   each state has a corresponding drawable bitmap.

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml:2: Warning: The application adaptive icon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml:2: Warning: The application adaptive roundIcon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "MonochromeLauncherIcon":
   If android:roundIcon and android:icon are both in your manifest, you must
   either remove the reference to android:roundIcon if it is not needed; or,
   supply the monochrome icon in the drawable defined by the android:roundIcon
   and android:icon attribute.

   For example, if android:roundIcon and android:icon are both in the
   manifest, a launcher might choose to use android:roundIcon over
   android:icon to display the adaptive app icon. Therefore, your themed
   application iconwill not show if your monochrome attribute is not also
   specified in android:roundIcon.

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:44: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:55: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:66: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:59: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml:19: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml:19: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~

   Explanation for issues of type "TextFields":
   Providing an inputType attribute on a text field improves usability because
   depending on the data to be input, optimized keyboards can be shown to the
   user (such as just digits and parentheses for a phone number). 

   The lint detector also looks at the id of the view, and if the id offers a
   hint of the purpose of the field (for example, the id contains the phrase
   phone or email), then lint will also ensure that the inputType contains the
   corresponding type attributes.

   If you really want to keep the text field generic, you can suppress this
   warning by setting inputType="text".

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:59: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml:19: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml:19: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml:28: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml:71: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml:19: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
    <EditText
     ~~~~~~~~

   Explanation for issues of type "LabelFor":
   Editable text fields should provide an android:hint or, provided your
   minSdkVersion is at least 17, they may be referenced by a view with a
   android:labelFor attribute.

   When using android:labelFor, be sure to provide an android:text or an
   android:contentDescription.

   If your view is labeled but by a label in a different layout which includes
   this one, just suppress this warning from lint.

D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\shareprefs\SharePrefsActivity.java:48: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            tvValuePrefs.setText("Value: " + value);
                                 ~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\shareprefs\SharePrefsActivity.java:48: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvValuePrefs.setText("Value: " + value);
                                 ~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java:174: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                            tvThreadCount.setText("" + finalI);
                                                  ~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java:210: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvAsyncCount.setText("Starting...");
                                 ~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java:221: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    tvAsyncCount.setText("Counting: " + progress);
                                         ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java:221: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    tvAsyncCount.setText("Counting: " + progress);
                                         ~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java:239: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                tvAsyncCount.setText(result.msg + ": result = " + result.code);
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java:239: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                tvAsyncCount.setText(result.msg + ": result = " + result.code);
                                                  ~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\database\room\UserAdapter.java:67: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            tvId.setText(user.getId() + "");
                         ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_adapter.xml:24: Warning: Hardcoded string "Show List view", should use @string resource [HardcodedText]
        android:text="Show List view"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_adapter.xml:33: Warning: Hardcoded string "Show Recycle view", should use @string resource [HardcodedText]
        android:text="Show Recycle view"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_alarm.xml:18: Warning: Hardcoded string "Alarm after 1 min - not repeating", should use @string resource [HardcodedText]
        android:text="Alarm after 1 min - not repeating"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_alarm.xml:29: Warning: Hardcoded string "Alarm after 2 min - repeat 3 times for each 1 min", should use @string resource [HardcodedText]
        android:text="Alarm after 2 min - repeat 3 times for each 1 min"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_broadcast.xml:14: Warning: Hardcoded string "Send User action", should use @string resource [HardcodedText]
        android:text="Send User action"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_broadcast.xml:25: Warning: Hardcoded string "Send New action", should use @string resource [HardcodedText]
        android:text="Send New action"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_broadcast.xml:36: Warning: Hardcoded string "Send Local action", should use @string resource [HardcodedText]
        android:text="Send Local action"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:22: Warning: Hardcoded string "Test Activity", should use @string resource [HardcodedText]
        android:text="Test Activity"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:32: Warning: Hardcoded string "Test View Layout", should use @string resource [HardcodedText]
        android:text="Test View Layout"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:42: Warning: Hardcoded string "Test Adapter", should use @string resource [HardcodedText]
        android:text="Test Adapter"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:52: Warning: Hardcoded string "Test Broadcast receiver", should use @string resource [HardcodedText]
        android:text="Test Broadcast receiver"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:62: Warning: Hardcoded string "Test Notification", should use @string resource [HardcodedText]
        android:text="Test Notification"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:72: Warning: Hardcoded string "Test Service", should use @string resource [HardcodedText]
        android:text="Test Service"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:82: Warning: Hardcoded string "Test SharePreference", should use @string resource [HardcodedText]
        android:text="Test SharePreference"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:92: Warning: Hardcoded string "Test Database", should use @string resource [HardcodedText]
        android:text="Test Database"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:102: Warning: Hardcoded string "Test Thread - Handler", should use @string resource [HardcodedText]
        android:text="Test Thread - Handler"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml:112: Warning: Hardcoded string "Test Alarm Service", should use @string resource [HardcodedText]
        android:text="Test Alarm Service"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_custom.xml:12: Warning: Hardcoded string "Button default", should use @string resource [HardcodedText]
        android:text="Button default"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_custom.xml:22: Warning: Hardcoded string "Button cusstom", should use @string resource [HardcodedText]
        android:text="Button cusstom"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:65: Warning: Hardcoded string "Edit text of Activity", should use @string resource [HardcodedText]
        android:hint="Edit text of Activity"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:77: Warning: Hardcoded string "Show fragment 1 or fragment 2 below", should use @string resource [HardcodedText]
        android:text="Show fragment 1 or fragment 2 below"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:98: Warning: Hardcoded string "Show Fragment 1", should use @string resource [HardcodedText]
        android:text="Show Fragment 1"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml:109: Warning: Hardcoded string "Show Fragment 2", should use @string resource [HardcodedText]
        android:text="Show Fragment 2"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_main.xml:14: Warning: Hardcoded string "Start Training Course", should use @string resource [HardcodedText]
        android:text="Start Training Course"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_main.xml:25: Warning: Hardcoded string "Start XXX", should use @string resource [HardcodedText]
        android:text="Start XXX"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_notification.xml:14: Warning: Hardcoded string "Make notification", should use @string resource [HardcodedText]
        android:text="Make notification"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_notification.xml:25: Warning: Hardcoded string "Make Custom notification", should use @string resource [HardcodedText]
        android:text="Make Custom notification"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:14: Warning: Hardcoded string "Count value is: ", should use @string resource [HardcodedText]
        android:text="Count value is: "
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:28: Warning: Hardcoded string "Start unbounded service", should use @string resource [HardcodedText]
        android:text="Start unbounded service"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:39: Warning: Hardcoded string "Stop unbounded service", should use @string resource [HardcodedText]
        android:text="Stop unbounded service"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:50: Warning: Hardcoded string "Start bounded service local", should use @string resource [HardcodedText]
        android:text="Start bounded service local"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:61: Warning: Hardcoded string "Stop bounded service local", should use @string resource [HardcodedText]
        android:text="Stop bounded service local"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:72: Warning: Hardcoded string "Start bounded service messenger", should use @string resource [HardcodedText]
        android:text="Start bounded service messenger"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:83: Warning: Hardcoded string "Stop bounded service messenger", should use @string resource [HardcodedText]
        android:text="Stop bounded service messenger"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:94: Warning: Hardcoded string "Start bounded service AIDL", should use @string resource [HardcodedText]
        android:text="Start bounded service AIDL"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml:105: Warning: Hardcoded string "Stop bounded service AIDL", should use @string resource [HardcodedText]
        android:text="Stop bounded service AIDL"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml:12: Warning: Hardcoded string "Add Prefs", should use @string resource [HardcodedText]
        android:text="Add Prefs"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml:34: Warning: Hardcoded string "Get Prefs", should use @string resource [HardcodedText]
        android:text="Get Prefs"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml:45: Warning: Hardcoded string "Value: ", should use @string resource [HardcodedText]
        android:text="Value: "
        ~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_snooze.xml:13: Warning: Hardcoded string "Snooze Activity", should use @string resource [HardcodedText]
        android:text="Snooze Activity"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:16: Warning: Hardcoded string "Test Thread", should use @string resource [HardcodedText]
        android:text="Test Thread"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:29: Warning: Hardcoded string "Thread count: ", should use @string resource [HardcodedText]
        android:text="Thread count: "
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:41: Warning: Hardcoded string "0", should use @string resource [HardcodedText]
        android:text="0"
        ~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:53: Warning: Hardcoded string "Test Async", should use @string resource [HardcodedText]
        android:text="Test Async"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:66: Warning: Hardcoded string "AsyncTask count: ", should use @string resource [HardcodedText]
        android:text="AsyncTask count: "
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:79: Warning: Hardcoded string "0", should use @string resource [HardcodedText]
        android:text="0"
        ~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:102: Warning: Hardcoded string "Test Executor", should use @string resource [HardcodedText]
        android:text="Test Executor"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml:113: Warning: Hardcoded string "Test Future Concurrent", should use @string resource [HardcodedText]
        android:text="Test Future Concurrent"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:30: Warning: Hardcoded string "Button 11111 1111111 11111", should use @string resource [HardcodedText]
        android:text="Button 11111 1111111 11111"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:49: Warning: Hardcoded string "Button 222222", should use @string resource [HardcodedText]
        android:text="Button 222222"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:70: Warning: Hardcoded string "Button 3", should use @string resource [HardcodedText]
        android:text="Button 3"
        ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:82: Warning: Hardcoded string "But 1", should use @string resource [HardcodedText]
        android:text="But 1"
        ~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:94: Warning: Hardcoded string "But 2", should use @string resource [HardcodedText]
        android:text="But 2"
        ~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:105: Warning: Hardcoded string "But 3", should use @string resource [HardcodedText]
        android:text="But 3"
        ~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:116: Warning: Hardcoded string "But 7", should use @string resource [HardcodedText]
        android:text="But 7"
        ~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:128: Warning: Hardcoded string "But 4", should use @string resource [HardcodedText]
        android:text="But 4"
        ~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:139: Warning: Hardcoded string "But 5", should use @string resource [HardcodedText]
        android:text="But 5"
        ~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:151: Warning: Hardcoded string "But 6", should use @string resource [HardcodedText]
        android:text="But 6"
        ~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:180: Warning: Hardcoded string "Button frame", should use @string resource [HardcodedText]
            android:text="Button frame"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml:188: Warning: Hardcoded string "Frame Layout here, background grey", should use @string resource [HardcodedText]
            android:text="Frame Layout here, background grey"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\file_row.xml:32: Warning: Hardcoded string "Name goes here", should use @string resource [HardcodedText]
            android:hint="Name goes here"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_bottom.xml:10: Warning: Hardcoded string "Bottom Fragment", should use @string resource [HardcodedText]
        android:text="Bottom Fragment"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_bottom.xml:23: Warning: Hardcoded string "Content of EditText:", should use @string resource [HardcodedText]
        android:text="Content of EditText:"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_bottom.xml:35: Warning: Hardcoded string "Content is shown here", should use @string resource [HardcodedText]
        android:hint="Content is shown here"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_one.xml:11: Warning: Hardcoded string "Fragment 1-1-1", should use @string resource [HardcodedText]
        android:text="Fragment 1-1-1"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml:10: Warning: Hardcoded string "Top Fragment", should use @string resource [HardcodedText]
        android:text="Top Fragment"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml:23: Warning: Hardcoded string "Edit text here", should use @string resource [HardcodedText]
        android:hint="Edit text here"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_two.xml:10: Warning: Hardcoded string "Fragment 2-2-2", should use @string resource [HardcodedText]
        android:text="Fragment 2-2-2"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:18: Warning: Hardcoded string "Title", should use @string resource [HardcodedText]
        android:text="Title"
        ~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:47: Warning: Hardcoded string "Activity", should use @string resource [HardcodedText]
            android:text="Activity"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:58: Warning: Hardcoded string "Broadcast", should use @string resource [HardcodedText]
            android:text="Broadcast"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml:69: Warning: Hardcoded string "Service", should use @string resource [HardcodedText]
            android:text="Service"
            ~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\view_layout_include.xml:12: Warning: Hardcoded string "include layout here", should use @string resource [HardcodedText]
        android:text="include layout here"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\view_layout_include.xml:23: Warning: Hardcoded string "Button include", should use @string resource [HardcodedText]
        android:text="Button include"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

1 errors, 137 warnings
