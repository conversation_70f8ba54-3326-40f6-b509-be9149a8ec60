package com.example.androidtraining.course.activityfragment;


import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.R;

public class TopFragment extends Fragment {
    public final String TAG = TopFragment.class.getSimpleName();
    PassDataToFragmentInterface mListenerInterface;
    EditText mEditText;

    @Override
    public void onAttach(@NonNull Context context) { // activity context is passed here
        Logger.d(TAG, "onAttach: ");
        super.onAttach(context);
        if (context instanceof PassDataToFragmentInterface) {
            mListenerInterface = (PassDataToFragmentInterface) context;
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate: ");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Logger.d(TAG, "onCreateView: ");
        View view = inflater.inflate(R.layout.fragment_top, container, false);

        mEditText = view.findViewById(R.id.edt_top_fragment);
        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (mListenerInterface != null) mListenerInterface.onTextChanged(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        return view;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        Logger.d(TAG, "onActivityCreated: ");
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Logger.d(TAG, "onViewCreated: ");
    }

    @Override
    public void onStart() {
        super.onStart();
        Logger.d(TAG, "onStart: ");
    }

    @Override
    public void onResume() {
        super.onResume();
        Logger.d(TAG, "onResume: ");
    }

    @Override
    public void onPause() {
        super.onPause();
        Logger.d(TAG, "onPause: ");
    }

    @Override
    public void onStop() {
        super.onStop();
        Logger.d(TAG, "onStop: ");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Logger.d(TAG, "onDestroyView: ");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy: ");
    }

    @Override
    public void onDetach() {
        super.onDetach();
        Logger.d(TAG, "onDetach: ");
    }
}
