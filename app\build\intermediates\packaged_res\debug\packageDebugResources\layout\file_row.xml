<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="20dp"
    app:cardElevation="8dp"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:padding="10dp"
        android:layout_height="match_parent">
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/listItemIcon"
            android:layout_width="80dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0.5"
            app:layout_constraintHorizontal_bias="0"
            android:layout_marginStart="20dp"
            android:layout_height="80dp"/>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/fileName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif"
            android:textFontWeight="200"
            android:hint="Name goes here"
            android:textSize="24dp"
            app:layout_constraintBottom_toBottomOf="@+id/listItemIcon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.1"
            app:layout_constraintStart_toEndOf="@+id/listItemIcon"
            app:layout_constraintTop_toTopOf="@+id/listItemIcon"
            app:layout_constraintVertical_bias="0" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>