package com.example.androidtraining.course;

import androidx.annotation.NonNull;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.widget.Button;

import com.example.androidtraining.BaseActivity;
import com.example.androidtraining.R;
import com.example.androidtraining.course.activityfragment.ExampleActivity;
import com.example.androidtraining.course.adapter.AdapterActivity;
import com.example.androidtraining.course.alarmservice.AlarmActivity;
import com.example.androidtraining.course.broadcastreceiver.BroadcastActivity;
import com.example.androidtraining.course.database.DatabaseActivity;
import com.example.androidtraining.course.notification.NotificationActivity;
import com.example.androidtraining.course.service.ServiceActivity;
import com.example.androidtraining.course.shareprefs.SharePrefsActivity;
import com.example.androidtraining.course.threadhandler.ThreadActivity;
import com.example.androidtraining.utils.Logger;
import com.example.androidtraining.course.viewlayout.ViewLayoutActivity;

public class CourseActivity extends BaseActivity {
    final String TAG = CourseActivity.class.getSimpleName();
    private Context mContext;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        layoutId = R.layout.activity_course;
        super.onCreate(savedInstanceState);

        Logger.d(TAG, "onCreate");
        mContext = this;

        Button btnActivity = findViewById(R.id.btn_activity);
        btnActivity.setOnClickListener(v -> startActivity(new Intent(mContext, ExampleActivity.class)));

        Button btnViewLayout = findViewById(R.id.btn_view_Layout);
        btnViewLayout.setOnClickListener(v -> startActivity(new Intent(mContext, ViewLayoutActivity.class)));

        Button btnAdapter = findViewById(R.id.btn_adapter);
        btnAdapter.setOnClickListener(v -> startActivity(new Intent(mContext, AdapterActivity.class)));

        Button btnBroadcast = findViewById(R.id.btn_broadcast_receiver);
        btnBroadcast.setOnClickListener(v -> startActivity(new Intent(mContext, BroadcastActivity.class)));

        Button btnNotification = findViewById(R.id.btn_notification);
        btnNotification.setOnClickListener(v -> startActivity(new Intent(mContext, NotificationActivity.class)));

        Button btnService = findViewById(R.id.btn_service);
        btnService.setOnClickListener(v -> startActivity(new Intent(mContext, ServiceActivity.class)));

        Button btnSharePrefs = findViewById(R.id.btn_share_prefs);
        btnSharePrefs.setOnClickListener(v -> startActivity(new Intent(mContext, SharePrefsActivity.class)));

        Button btnDatabase = findViewById(R.id.btn_database);
        btnDatabase.setOnClickListener(v -> startActivity(new Intent(mContext, DatabaseActivity.class)));

        Button btnThread = findViewById(R.id.btn_thread_handler);
        btnThread.setOnClickListener(v -> startActivity(new Intent(mContext, ThreadActivity.class)));

        Button btnAlarm = findViewById(R.id.btn_alarm_service);
        btnAlarm.setOnClickListener(v -> startActivity(new Intent(mContext, AlarmActivity.class)));

        // request storage permission
        // API >= 30: MANAGE_EXTERNAL_STORAGE & "All files access" -
//        requestStoragePermission();
    }

//    private void requestStoragePermission() {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R /*API 30*/) {
//            Logger.d(TAG, "requestStoragePermission, API >= 30: isExternalStorageManager() - " + Environment.isExternalStorageManager());
//            if (!Environment.isExternalStorageManager()) {
//                Snackbar.make(findViewById(android.R.id.content), "Permission needed!", Snackbar.LENGTH_INDEFINITE)
//                        .setAction("Settings", v -> {
//                            try {
//                                Uri uri = Uri.parse("package:" + BuildConfig.APPLICATION_ID);
//                                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION, uri);
//                                startActivityForResult(intent, 200);
//                            } catch (Exception ex) {
//                                Intent intent = new Intent();
//                                intent.setAction(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
//                                startActivityForResult(intent, 200);
//                            }
//                        })
//                        .show();
//            }
//        } else {
//            boolean hasAllStoragePermissions = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
//                    && ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
//            Logger.d(TAG, "requestStoragePermission, API < 30: hasAllStoragePermissions=" + hasAllStoragePermissions);
//            if (!hasAllStoragePermissions) {
//                requestPermissions(new String[] {Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE}, 100);
////                requestPermissions();
//            }
//        }
//    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Logger.d(TAG, "onConfigurationChanged - newConfig=" + newConfig);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        Logger.d(TAG, "onRestart");
    }

    @Override
    protected void onStart() {
        super.onStart();
        Logger.d(TAG, "onStart");
    }

    @Override
    protected void onResume() {
        super.onResume();
        Logger.d(TAG, "onResume");
    }

    @Override
    protected void onPause() {
        super.onPause();
        Logger.d(TAG, "onPause");
    }

    @Override
    protected void onStop() {
        super.onStop();
        Logger.d(TAG, "onStop");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Logger.d(TAG, "onDestroy");
    }
}