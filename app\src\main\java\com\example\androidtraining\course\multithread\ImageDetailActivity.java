package com.example.androidtraining.course.multithread;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.androidtraining.R;
import com.example.androidtraining.utils.Logger;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ImageDetailActivity extends AppCompatActivity {
    private static final String TAG = ImageDetailActivity.class.getSimpleName();
    public static final String EXTRA_IMAGE_ID = "image_id";
    
    private ImageView imageView;
    private TextView textName, textPath, textSize, textDimensions;
    private TextView textDateTaken, textDateModified, textMimeType;
    private TextView textDetectedObjects;
    private ProgressBar progressBar;
    private Toolbar toolbar;
    
    private RoomImage currentImage;
    private RoomImageDao imageDao;
    private ExecutorService executorService;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_image_detail);
        
        initViews();
        setupToolbar();
        
        imageDao = RoomImageDatabase.getDatabase(this).roomImageDao();
        executorService = Executors.newSingleThreadExecutor();
        
        int imageId = getIntent().getIntExtra(EXTRA_IMAGE_ID, -1);
        if (imageId != -1) {
            loadImageDetails(imageId);
        } else {
            Toast.makeText(this, "Invalid image ID", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    private void initViews() {
        imageView = findViewById(R.id.image_view_detail);
        textName = findViewById(R.id.text_name);
        textPath = findViewById(R.id.text_path);
        textSize = findViewById(R.id.text_size);
        textDimensions = findViewById(R.id.text_dimensions);
        textDateTaken = findViewById(R.id.text_date_taken);
        textDateModified = findViewById(R.id.text_date_modified);
        textMimeType = findViewById(R.id.text_mime_type);
        textDetectedObjects = findViewById(R.id.text_detected_objects);
        progressBar = findViewById(R.id.progress_bar_detail);
        toolbar = findViewById(R.id.toolbar_detail);
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Image Details");
        }
    }
    
    private void loadImageDetails(int imageId) {
        progressBar.setVisibility(View.VISIBLE);
        
        executorService.execute(() -> {
            try {
                currentImage = imageDao.getImageById(imageId);
                
                runOnUiThread(() -> {
                    if (currentImage != null) {
                        displayImageDetails();
                        loadFullImage();
                    } else {
                        Toast.makeText(this, "Image not found", Toast.LENGTH_SHORT).show();
                        finish();
                    }
                });
                
            } catch (Exception e) {
                Logger.e(TAG, "Error loading image details: " + e.getMessage());
                runOnUiThread(() -> {
                    Toast.makeText(this, "Error loading image details", Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        });
    }
    
    private void displayImageDetails() {
        if (currentImage == null) return;
        
        textName.setText(currentImage.getName());
        textPath.setText(currentImage.getPath());
        textSize.setText(currentImage.getFormattedSize());
        textDimensions.setText(currentImage.getDimensions());
        textMimeType.setText(currentImage.getMimeType());
        
        // Format dates
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy HH:mm:ss", Locale.getDefault());
        
        if (currentImage.getDateTaken() > 0) {
            textDateTaken.setText(dateFormat.format(new Date(currentImage.getDateTaken())));
        } else {
            textDateTaken.setText("Unknown");
        }
        
        if (currentImage.getDateModified() > 0) {
            textDateModified.setText(dateFormat.format(new Date(currentImage.getDateModified())));
        } else {
            textDateModified.setText("Unknown");
        }
        
        // Display detected objects
        if (currentImage.getDetectedObjects() != null && !currentImage.getDetectedObjects().isEmpty()) {
            textDetectedObjects.setText(currentImage.getDetectedObjects());
            textDetectedObjects.setVisibility(View.VISIBLE);
        } else {
            textDetectedObjects.setText("No objects detected");
            textDetectedObjects.setVisibility(View.VISIBLE);
        }
        
        // Update toolbar title
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(currentImage.getName());
        }
    }
    
    private void loadFullImage() {
        executorService.execute(() -> {
            try {
                Bitmap bitmap = null;
                
                // Try to load the full image
                File imageFile = new File(currentImage.getPath());
                if (imageFile.exists()) {
                    // Load with appropriate scaling to prevent OOM
                    BitmapFactory.Options options = new BitmapFactory.Options();
                    options.inJustDecodeBounds = true;
                    BitmapFactory.decodeFile(currentImage.getPath(), options);
                    
                    // Calculate sample size
                    int sampleSize = calculateSampleSize(options, 1024, 1024);
                    options.inSampleSize = sampleSize;
                    options.inJustDecodeBounds = false;
                    
                    bitmap = BitmapFactory.decodeFile(currentImage.getPath(), options);
                }
                
                final Bitmap finalBitmap = bitmap;
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    if (finalBitmap != null) {
                        imageView.setImageBitmap(finalBitmap);
                    } else {
                        imageView.setImageResource(R.drawable.ic_image_placeholder);
                        Toast.makeText(this, "Could not load image", Toast.LENGTH_SHORT).show();
                    }
                });
                
            } catch (Exception e) {
                Logger.e(TAG, "Error loading full image: " + e.getMessage());
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    imageView.setImageResource(R.drawable.ic_image_placeholder);
                });
            }
        });
    }
    
    private int calculateSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;
        
        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;
            
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        
        return inSampleSize;
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.image_detail_menu, menu);
        
        // Update like button state
        MenuItem likeItem = menu.findItem(R.id.action_like);
        if (likeItem != null && currentImage != null) {
            if (currentImage.isLiked()) {
                likeItem.setIcon(R.drawable.ic_favorite_filled);
                likeItem.setTitle("Unlike");
            } else {
                likeItem.setIcon(R.drawable.ic_favorite_border);
                likeItem.setTitle("Like");
            }
        }
        
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();
        
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_like) {
            toggleLike();
            return true;
        } else if (id == R.id.action_share) {
            shareImage();
            return true;
        } else if (id == R.id.action_delete) {
            deleteImage();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    private void toggleLike() {
        if (currentImage == null) return;
        
        executorService.execute(() -> {
            try {
                boolean newLikeStatus = !currentImage.isLiked();
                imageDao.updateLikeStatus(currentImage.getId(), newLikeStatus);
                currentImage.setLiked(newLikeStatus);
                
                runOnUiThread(() -> {
                    invalidateOptionsMenu(); // Refresh menu
                    String message = newLikeStatus ? "Added to favorites" : "Removed from favorites";
                    Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
                });
                
            } catch (Exception e) {
                Logger.e(TAG, "Error updating like status: " + e.getMessage());
                runOnUiThread(() -> {
                    Toast.makeText(this, "Error updating like status", Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void shareImage() {
        if (currentImage == null) return;
        
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("image/*");
        shareIntent.putExtra(Intent.EXTRA_STREAM, android.net.Uri.fromFile(new File(currentImage.getPath())));
        shareIntent.putExtra(Intent.EXTRA_TEXT, "Sharing: " + currentImage.getName());
        
        try {
            startActivity(Intent.createChooser(shareIntent, "Share Image"));
        } catch (Exception e) {
            Toast.makeText(this, "Error sharing image", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void deleteImage() {
        if (currentImage == null) return;
        
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Delete Image")
                .setMessage("Are you sure you want to delete this image?")
                .setPositiveButton("Delete", (dialog, which) -> {
                    performDelete();
                })
                .setNegativeButton("Cancel", null)
                .show();
    }
    
    private void performDelete() {
        progressBar.setVisibility(View.VISIBLE);
        
        executorService.execute(() -> {
            try {
                // Delete from database
                imageDao.deleteImage(currentImage);
                
                // Delete actual file
                File imageFile = new File(currentImage.getPath());
                if (imageFile.exists()) {
                    imageFile.delete();
                }
                
                // Delete thumbnail
                if (currentImage.getThumbnailPath() != null) {
                    File thumbnailFile = new File(currentImage.getThumbnailPath());
                    if (thumbnailFile.exists()) {
                        thumbnailFile.delete();
                    }
                }
                
                runOnUiThread(() -> {
                    Toast.makeText(this, "Image deleted", Toast.LENGTH_SHORT).show();
                    setResult(RESULT_OK); // Notify calling activity
                    finish();
                });
                
            } catch (Exception e) {
                Logger.e(TAG, "Error deleting image: " + e.getMessage());
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(this, "Error deleting image", Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
