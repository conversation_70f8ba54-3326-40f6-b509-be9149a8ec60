package com.example.androidtraining.course.database.room.dao;

import android.database.Cursor;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.RawQuery;
import androidx.room.Update;
import androidx.sqlite.db.SupportSQLiteQuery;

import com.example.androidtraining.course.database.room.entities.User;

import java.util.List;

@Dao
public interface  UserDao {
    // For DAO & local using
    @Query("SELECT * FROM engineers")
    List<User> getAllUsers();
    @Query("SELECT * FROM engineers WHERE id IN (:uIds)")
    List<User> getListUsersByIds(int[] uIds);
    @Query("SELECT * FROM engineers WHERE name = :name LIMIT 1")
    User findUserByName(String name);
    @Query("SELECT * FROM engineers WHERE id = :id")
    Cursor selectUserById(long id);

    @Insert(onConflict = OnConflictStrategy.REPLACE) // IGNORE
    long[] insertUsers(List<User> users);
    @Insert(onConflict = OnConflictStrategy.REPLACE) // IGNORE
    long insertUser(User user);

    @Update
    int updateUser(User user); // where id = user.id
    @Query("UPDATE engineers SET name=:name, single_id=:singleId WHERE name like :nameLike OR id=:id")
    void updateUser(String name, String singleId, String nameLike, int id);

    @Delete
    int deleteUser(User user); // where id = user.id
    @Delete
    int deleteUsers(List<User> users);
    @Query("DELETE FROM engineers WHERE id=:uid")
    int deleteUserById(String uid);

    // Using SQL command
    @RawQuery
    User updateUser(SupportSQLiteQuery query);
}
