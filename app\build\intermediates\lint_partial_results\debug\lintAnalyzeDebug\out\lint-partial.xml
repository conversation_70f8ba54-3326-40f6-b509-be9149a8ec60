<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.7.0" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="ProviderReadPermissionOnly">
            <map id="com.example.androidtraining.course.database.AppDbContentProvider">
                <entry
                    name="implementedWriteMethods"
                    string="{`insert`, `update`, `delete`}"/>
                <location id="location"
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/database/AppDbContentProvider.java"
                    line="31"
                    column="14"
                    startOffset="1154"
                    endLine="31"
                    endColumn="34"
                    endOffset="1174"/>
            </map>
    </map>
    <map id="ScheduleExactAlarm">
        <location id="0"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/alarmservice/AlarmActivity.java"
            line="116"
            column="13"
            startOffset="6011"
            endLine="116"
            endColumn="119"
            endOffset="6117"/>
        <location id="1"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/alarmservice/AlarmReceiver.java"
            line="109"
            column="13"
            startOffset="5806"
            endLine="109"
            endColumn="100"
            endOffset="5893"/>
        <entry
            name="ChecksExactAlarmPermission"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsRegisteredNonExported">
                <entry
                    name="com.example.androidtraining.SERVICE_SEND_COUNT (used to send a broadcast)"
                    boolean="true"/>
            </map>
            <map id="actionsSent">
                    <map id="com.example.androidtraining.SERVICE_SEND_COUNT (used to send a broadcast)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/service/ForegroundService.java"
                            line="71"
                            column="38"
                            startOffset="2926"
                            endLine="71"
                            endColumn="67"
                            endOffset="2955"/>
                    </map>
                    <map id="com.example.androidtraining.USER_ACTION (used to send a broadcast)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/androidtraining/course/broadcastreceiver/BroadcastActivity.java"
                            line="49"
                            column="29"
                            startOffset="2212"
                            endLine="49"
                            endColumn="52"
                            endOffset="2235"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.androidtraining.MainActivity"
                    boolean="true"/>
                <entry
                    name="com.example.androidtraining.course.CourseActivity"
                    boolean="true"/>
                <entry
                    name="com.example.androidtraining.course.activityfragment.ExampleActivity"
                    boolean="true"/>
                <entry
                    name="com.example.androidtraining.course.alarmservice.AlarmReceiver"
                    boolean="true"/>
                <entry
                    name="com.example.androidtraining.course.broadcastreceiver.StaticBroadcastReceiver"
                    boolean="true"/>
                <entry
                    name="com.example.androidtraining.course.service.BoundedServiceAIDLBasic"
                    boolean="true"/>
                <entry
                    name="com.example.androidtraining.course.service.BoundedServiceAidlObject"
                    boolean="true"/>
                <entry
                    name="com.example.androidtraining.course.service.BoundedServiceLocal"
                    boolean="true"/>
                <entry
                    name="com.example.androidtraining.course.service.BoundedServiceMessenger"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.string.hello_blank_fragment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="135"
            endLine="4"
            endColumn="40"
            endOffset="162"/>
        <entry
            name="model"
            string="attr[colorPrimaryVariant(R)],color[teal_200(U),purple_200(U),design_default_color_secondary(R),design_default_color_error(R),material_dynamic_neutral40(R),design_default_color_secondary_variant(R),purple_500(U),purple_700(U),teal_700(U),black(U),white(U)],dimen[btn_margin_top(U)],drawable[ic_launcher_background(U),ic_arrow(U),ic_next(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R)],id[main(U),fragment_container_adapter(U),btn_show_list_view(U),btn_show_recycle_view(U),btn_exact_not_repeat(U),btn_exact_repeat(U),btn_send_user_action(U),btn_send_new_action(U),btn_send_local_action(U),guideline_main_20(U),btn_activity(U),btn_view_Layout(U),btn_adapter(U),btn_broadcast_receiver(U),btn_notification(U),btn_service(U),btn_share_prefs(U),btn_database(U),btn_thread_handler(U),btn_alarm_service(U),btn_noti_default(U),btn_noti_custom(U),user_recycle_view(U),guideline_15(U),guideline_40(U),guideline_55(U),fragment_top(U),fragment_bottom(U),edt_activity(U),fragment_container_activity(U),btn_show_fragment1(U),btn_show_fragment2(U),btn_start_course(U),btn_start_xx(D),btn_create_notification(U),btn_create_remote_notification(U),tv_count(U),btn_start_unbounded_service(U),btn_stop_unbounded_service(U),btn_start_bounded_service_local(U),btn_stop_bounded_service_local(U),btn_start_bounded_service_messenger(U),btn_stop_bounded_service_messenger(U),btn_start_bounded_service_aidl(U),btn_stop_bounded_service_aidl(U),btn_add_prefs(U),edt_value_prefs(U),btn_get_prefs(U),tv_value_prefs(U),btn_test_thread(U),tv_thread_title(U),tv_thread_count(U),btn_test_async(U),tv_async_title(U),tv_count_async(U),seek_bar_async(U),btn_test_executor(U),btn_test_future_concurrent(U),gl_h_60(U),gl_v_40(U),btn1(U),barrier1(U),btn2(U),btn3(U),but1(U),but2(U),but3(U),but7(U),but4(U),but5(U),but6(U),group(D),frame_layout(D),btn_frame(U),tv_frame(U),listItemIcon(U),fileName(U),tv_title(U),tv_show_content(U),list_view_song(U),recycle_view_song(U),edt_top_fragment(U),item_song_view(U),guideline_item_vertical_30(U),guideline_item_vertical_90(U),img_song_icon(U),tv_song_name(U),tv_song_author(U),tv_song_time(U),img_song_next(U),item_user_view(D),guideline_item_user_vertical_15(U),guideline_item_user_vertical_70(U),tv_user_id(U),tv_user_name(U),tv_user_phone(U),noti_tv_title(U),noti_tv_content(U),noti_footer(U),noti_btn_start_activity(U),noti_btn_send_broadcast(U),noti_btn_start_service(U),tv_include(U),btn_include(U)],layout[activity_adapter(U),activity_alarm(U),activity_broadcast(U),activity_course(U),activity_custom(U),activity_database(U),activity_example(U),activity_main(U),activity_notification(U),activity_service(U),activity_share_prefs(U),activity_snooze(U),activity_thread(U),activity_view_layout(U),view_layout_include(U),file_row(U),fragment_bottom(U),fragment_list_view(U),fragment_one(U),fragment_recycle_view(U),fragment_top(U),fragment_two(U),item_list_song(U),item_list_user(U),notification_layout(U)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),hello_blank_fragment(D)],style[Theme_AndroidTraining(U),Theme_MaterialComponents_DayNight_DarkActionBar(R)],xml[data_extraction_rules(U),backup_rules(U)];10^11,7a^14,7b^c^16,7c^19,7d^c^1b^1c^1d^1e^1f^20^21^22^23^24,7e^26,80^1^29^2^2a^2c^2b^30,81^c^32,82^34,83^37^36^38^39^3a^3b^3c^3d,84^3f^41^40,86^43^44^46^47^4a,87^4c^4d^3^4f^53^4e^54^52^55^57^4^58^56^5^51^e^88,88^3^78,89^5d,8a^5f,8b^1,8d^2,90^66^65^68^f,91^6d^6e,92^74^72^6,93^d^10,94^d^10,97^98^7^8^b^1^9^a^0^2;;;"/>
    </map>

</incidents>
