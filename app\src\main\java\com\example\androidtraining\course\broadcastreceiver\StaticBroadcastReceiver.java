package com.example.androidtraining.course.broadcastreceiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.widget.Toast;

import com.example.androidtraining.utils.Logger;

public class StaticBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = StaticBroadcastReceiver.class.getSimpleName();
    @Override
    public void onReceive(Context context, Intent intent) {
        String extra = intent.getStringExtra("action");
        Logger.d(TAG, "StaticBroadcastReceiver - onReceiver: action = " + intent.getAction() + ", extra=" + extra);
//        Toast.makeText(context, TAG + " - onReceiver: " + extra, Toast.LENGTH_LONG).show();
    }
}
