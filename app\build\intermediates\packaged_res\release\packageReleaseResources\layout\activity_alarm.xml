<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".course.alarmservice.AlarmActivity">

    <Button
        android:id="@+id/btn_exact_not_repeat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/btn_margin_top"
        android:text="Alarm after 1 min - not repeating"
        />

    <Button
        android:id="@+id/btn_exact_repeat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_exact_not_repeat"
        android:layout_marginTop="@dimen/btn_margin_top"
        android:text="Alarm after 2 min - repeat 3 times for each 1 min"
        />

</androidx.constraintlayout.widget.ConstraintLayout>