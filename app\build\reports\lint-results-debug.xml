<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.7.0">

    <issue
        id="UnusedAttribute"
        severity="Warning"
        message="Attribute `textFontWeight` is only used in API level 28 and higher (current min is 23)"
        category="Correctness"
        priority="6"
        summary="Attribute unused on older versions"
        explanation="This check finds attributes set in XML files that were introduced in a version newer than the oldest version targeted by your application (with the `minSdkVersion` attribute).&#xA;&#xA;This is not an error; the application will simply ignore the attribute. However, if the attribute is important to the appearance or functionality of your application, you should consider finding an alternative way to achieve the same result with only available attributes, and then you can optionally create a copy of the layout in a layout-vNN folder which will be used on API NN or higher where you can take advantage of the newer attribute.&#xA;&#xA;Note: This check does not only apply to attributes. For example, some tags can be unused too, such as the new `&lt;tag>` element in layouts introduced in API 21."
        errorLine1="            android:textFontWeight=&quot;200&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\file_row.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedAttribute"
        severity="Warning"
        message="Attribute `tooltipText` is only used in API level 26 and higher (current min is 23)"
        category="Correctness"
        priority="6"
        summary="Attribute unused on older versions"
        explanation="This check finds attributes set in XML files that were introduced in a version newer than the oldest version targeted by your application (with the `minSdkVersion` attribute).&#xA;&#xA;This is not an error; the application will simply ignore the attribute. However, if the attribute is important to the appearance or functionality of your application, you should consider finding an alternative way to achieve the same result with only available attributes, and then you can optionally create a copy of the layout in a layout-vNN folder which will be used on API NN or higher where you can take advantage of the newer attribute.&#xA;&#xA;Note: This check does not only apply to attributes. For example, some tags can be unused too, such as the new `&lt;tag>` element in layouts introduced in API 21."
        errorLine1="        android:tooltipText=&quot;Name&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml"
            line="45"
            column="9"/>
    </issue>

    <issue
        id="UnusedAttribute"
        severity="Warning"
        message="Attribute `tooltipText` is only used in API level 26 and higher (current min is 23)"
        category="Correctness"
        priority="6"
        summary="Attribute unused on older versions"
        explanation="This check finds attributes set in XML files that were introduced in a version newer than the oldest version targeted by your application (with the `minSdkVersion` attribute).&#xA;&#xA;This is not an error; the application will simply ignore the attribute. However, if the attribute is important to the appearance or functionality of your application, you should consider finding an alternative way to achieve the same result with only available attributes, and then you can optionally create a copy of the layout in a layout-vNN folder which will be used on API NN or higher where you can take advantage of the newer attribute.&#xA;&#xA;Note: This check does not only apply to attributes. For example, some tags can be unused too, such as the new `&lt;tag>` element in layouts introduced in API 21."
        errorLine1="        android:tooltipText=&quot;Phone&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml"
            line="57"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;18dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="78"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;12dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="99"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;12dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="110"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;18dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="         android:textSize=&quot;24dp&quot;"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_snooze.xml"
            line="14"
            column="10"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;20dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="28"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;20dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="40"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;20dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="65"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;20dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="78"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="            android:textSize=&quot;24dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\file_row.xml"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;30dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_one.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;30dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_two.xml"
            line="11"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;16dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml"
            line="41"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;12dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml"
            line="52"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;12dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml"
            line="63"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;16dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml"
            line="33"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;12dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml"
            line="44"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="        android:textSize=&quot;12dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_user.xml"
            line="56"
            column="9"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="            android:textSize=&quot;12dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="48"
            column="13"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="            android:textSize=&quot;12dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="SpUsage"
        severity="Warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes"
        category="Correctness"
        priority="3"
        summary="Using `dp` instead of `sp` for text sizes"
        explanation="When setting text sizes, you should normally use `sp`, or &quot;scale-independent pixels&quot;. This is like the `dp` unit, but it is also scaled by the user&apos;s font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user&apos;s preference.&#xA;&#xA;There **are** cases where you might need to use `dp`; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user&apos;s font size settings are not respected, so consider adjusting the layout itself to be more flexible."
        url="https://developer.android.com/training/multiscreen/screendensities.html"
        urls="https://developer.android.com/training/multiscreen/screendensities.html"
        errorLine1="            android:textSize=&quot;12dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="UseCompatTextViewDrawableXml"
        severity="Warning"
        message="Use `app:drawableEndCompat` instead of `android:drawableEnd`"
        category="Correctness"
        priority="1"
        summary="Compat compound drawable attributes should be used on `TextView`"
        explanation="`TextView` uses `android:` compound drawable attributes instead of `app:` ones"
        errorLine1="            android:drawableEnd=&quot;@drawable/ic_arrow&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="197"
            column="13"/>
    </issue>

    <issue
        id="UseCompatTextViewDrawableXml"
        severity="Warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`"
        category="Correctness"
        priority="1"
        summary="Compat compound drawable attributes should be used on `TextView`"
        explanation="`TextView` uses `android:` compound drawable attributes instead of `app:` ones"
        errorLine1="            android:drawableStart=&quot;@drawable/ic_arrow&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="198"
            column="13"/>
    </issue>

    <issue
        id="UnsafeImplicitIntentLaunch"
        severity="Error"
        message="The intent action `com.example.androidtraining.SERVICE_SEND_COUNT (used to send a broadcast)` matches the intent filter of a non-exported receiver, registered via a call to `Context.registerReceiver`, or similar. If you are trying to invoke this specific receiver via the action then you should use `Intent.setPackage(&lt;APPLICATION_ID>)`."
        category="Security"
        priority="9"
        summary="Implicit intent matches an internal non-exported component"
        explanation="This intent matches a non-exported component within the same app. In many cases, the app developer could instead use an explicit Intent to send messages to their internal components, ensuring that the messages are safely delivered without exposure to malicious apps on the device. Using such implicit intents will result in a crash in an upcoming version of Android."
        errorLine1="                    Intent intent1 = new Intent(ACTION_SEND_COUNT);"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\service\ForegroundService.java"
            line="71"
            column="38"/>
    </issue>

    <issue
        id="ExportedReceiver"
        severity="Warning"
        message="Exported receiver does not require permission"
        category="Security"
        priority="5"
        summary="Receiver does not require permission"
        explanation="Exported receivers (receivers which either set `exported=true` or contain an intent-filter and do not specify `exported=false`) should define a permission that an entity must have in order to launch the receiver or bind to it. Without this, any application can use this receiver."
        url="https://goo.gle/ExportedReceiver"
        urls="https://goo.gle/ExportedReceiver"
        errorLine1="        &lt;receiver"
        errorLine2="         ~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml"
            line="118"
            column="10"/>
    </issue>

    <issue
        id="ExportedService"
        severity="Warning"
        message="Exported service does not require permission"
        category="Security"
        priority="5"
        summary="Exported service does not require permission"
        explanation="Exported services (services which either set `exported=true` or contain an intent-filter and do not specify `exported=false`) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service."
        url="https://goo.gle/ExportedService"
        urls="https://goo.gle/ExportedService"
        errorLine1="        &lt;service"
        errorLine2="         ~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml"
            line="133"
            column="10"/>
    </issue>

    <issue
        id="ExportedService"
        severity="Warning"
        message="Exported service does not require permission"
        category="Security"
        priority="5"
        summary="Exported service does not require permission"
        explanation="Exported services (services which either set `exported=true` or contain an intent-filter and do not specify `exported=false`) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service."
        url="https://goo.gle/ExportedService"
        urls="https://goo.gle/ExportedService"
        errorLine1="        &lt;service"
        errorLine2="         ~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml"
            line="136"
            column="10"/>
    </issue>

    <issue
        id="ExportedService"
        severity="Warning"
        message="Exported service does not require permission"
        category="Security"
        priority="5"
        summary="Exported service does not require permission"
        explanation="Exported services (services which either set `exported=true` or contain an intent-filter and do not specify `exported=false`) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service."
        url="https://goo.gle/ExportedService"
        urls="https://goo.gle/ExportedService"
        errorLine1="        &lt;service"
        errorLine2="         ~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml"
            line="140"
            column="10"/>
    </issue>

    <issue
        id="ExportedService"
        severity="Warning"
        message="Exported service does not require permission"
        category="Security"
        priority="5"
        summary="Exported service does not require permission"
        explanation="Exported services (services which either set `exported=true` or contain an intent-filter and do not specify `exported=false`) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service."
        url="https://goo.gle/ExportedService"
        urls="https://goo.gle/ExportedService"
        errorLine1="        &lt;service"
        errorLine2="         ~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\AndroidManifest.xml"
            line="144"
            column="10"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\MainActivity.java"
            line="96"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\MainActivity.java"
            line="135"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="This folder configuration (`v23`) is unnecessary; `minSdkVersion` is 23. Merge all the resources in this folder into `values`."
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder.">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values-v23"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="This `AsyncTask` class should be static or leaks might occur (com.example.androidtraining.course.threadhandler.ThreadActivity.MyAsyncTask)"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    class MyAsyncTask extends AsyncTask&lt;String, Integer, Result> {"
        errorLine2="          ~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java"
            line="246"
            column="11"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/purple_200` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTraining`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/purple_200&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_recycle_view.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/design_default_color_secondary` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTraining`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/design_default_color_secondary&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\view_layout_include.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.hello_blank_fragment` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;hello_blank_fragment&quot;>Hello blank fragment&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\values\strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="IconXmlAndPng"
        severity="Warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\drawable-anydpi\ic_arrow.xml, src\main\res\drawable-hdpi\ic_arrow.png"
        category="Usability:Icons"
        priority="7"
        summary="Icon is specified both as `.xml` file and as a bitmap"
        explanation="If a drawable resource appears as an `.xml` file in the `drawable/` folder, it&apos;s usually not intentional for it to also appear as a bitmap using the same name; generally you expect the drawable XML file to define states and each state has a corresponding drawable bitmap.">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xxhdpi\ic_arrow.png"/>
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xhdpi\ic_arrow.png"/>
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-mdpi\ic_arrow.png"/>
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-hdpi\ic_arrow.png"/>
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-anydpi\ic_arrow.xml"/>
    </issue>

    <issue
        id="IconXmlAndPng"
        severity="Warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\drawable-anydpi\ic_next.xml, src\main\res\drawable-hdpi\ic_next.png"
        category="Usability:Icons"
        priority="7"
        summary="Icon is specified both as `.xml` file and as a bitmap"
        explanation="If a drawable resource appears as an `.xml` file in the `drawable/` folder, it&apos;s usually not intentional for it to also appear as a bitmap using the same name; generally you expect the drawable XML file to define states and each state has a corresponding drawable bitmap.">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xxhdpi\ic_next.png"/>
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-xhdpi\ic_next.png"/>
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-mdpi\ic_next.png"/>
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-hdpi\ic_next.png"/>
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\drawable-anydpi\ic_next.xml"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        severity="Warning"
        message="The application adaptive icon is missing a monochrome tag"
        category="Usability:Icons"
        priority="6"
        summary="Monochrome icon is not defined"
        explanation="If `android:roundIcon` and `android:icon` are both in your manifest, you must either remove the reference to `android:roundIcon` if it is not needed; or, supply the monochrome icon in the drawable defined by the `android:roundIcon` and `android:icon` attribute.&#xA;&#xA;For example, if `android:roundIcon` and `android:icon` are both in the manifest, a launcher might choose to use `android:roundIcon` over `android:icon` to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in `android:roundIcon`."
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        severity="Warning"
        message="The application adaptive roundIcon is missing a monochrome tag"
        category="Usability:Icons"
        priority="6"
        summary="Monochrome icon is not defined"
        explanation="If `android:roundIcon` and `android:icon` are both in your manifest, you must either remove the reference to `android:roundIcon` if it is not needed; or, supply the monochrome icon in the drawable defined by the `android:roundIcon` and `android:icon` attribute.&#xA;&#xA;For example, if `android:roundIcon` and `android:icon` are both in the manifest, a launcher might choose to use `android:roundIcon` over `android:icon` to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in `android:roundIcon`."
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="44"
            column="10"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="55"
            column="10"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="66"
            column="10"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="59"
            column="6"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml"
            line="19"
            column="6"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml"
            line="19"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="59"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml"
            line="19"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml"
            line="19"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml"
            line="28"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\item_list_song.xml"
            line="71"
            column="6"/>
    </issue>

    <issue
        id="LabelFor"
        severity="Warning"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        category="Accessibility"
        priority="2"
        summary="Missing accessibility label"
        explanation="Editable text fields should provide an `android:hint` or, provided your `minSdkVersion` is at least 17, they may be referenced by a view with a `android:labelFor` attribute.&#xA;&#xA;When using `android:labelFor`, be sure to provide an `android:text` or an `android:contentDescription`.&#xA;&#xA;If your view is labeled but by a label in a different layout which includes this one, just suppress this warning from lint."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml"
            line="19"
            column="6"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvValuePrefs.setText(&quot;Value: &quot; + value);"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\shareprefs\SharePrefsActivity.java"
            line="48"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvValuePrefs.setText(&quot;Value: &quot; + value);"
        errorLine2="                                 ~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\shareprefs\SharePrefsActivity.java"
            line="48"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                            tvThreadCount.setText(&quot;&quot; + finalI);"
        errorLine2="                                                  ~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java"
            line="174"
            column="51"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvAsyncCount.setText(&quot;Starting...&quot;);"
        errorLine2="                                 ~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java"
            line="210"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    tvAsyncCount.setText(&quot;Counting: &quot; + progress);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java"
            line="221"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    tvAsyncCount.setText(&quot;Counting: &quot; + progress);"
        errorLine2="                                         ~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java"
            line="221"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                tvAsyncCount.setText(result.msg + &quot;: result = &quot; + result.code);"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java"
            line="239"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                tvAsyncCount.setText(result.msg + &quot;: result = &quot; + result.code);"
        errorLine2="                                                  ~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\threadhandler\ThreadActivity.java"
            line="239"
            column="51"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvId.setText(user.getId() + &quot;&quot;);"
        errorLine2="                         ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\java\com\example\androidtraining\course\database\room\UserAdapter.java"
            line="67"
            column="26"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Show List view&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Show List view&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_adapter.xml"
            line="24"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Show Recycle view&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Show Recycle view&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_adapter.xml"
            line="33"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Alarm after 1 min - not repeating&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Alarm after 1 min - not repeating&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_alarm.xml"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Alarm after 2 min - repeat 3 times for each 1 min&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Alarm after 2 min - repeat 3 times for each 1 min&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_alarm.xml"
            line="29"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Send User action&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Send User action&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_broadcast.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Send New action&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Send New action&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_broadcast.xml"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Send Local action&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Send Local action&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_broadcast.xml"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Activity&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Activity&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="22"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test View Layout&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test View Layout&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Adapter&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Adapter&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Broadcast receiver&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Broadcast receiver&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="52"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Notification&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Notification&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="62"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Service&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="72"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test SharePreference&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test SharePreference&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="82"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Database&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Database&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="92"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Thread - Handler&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Thread - Handler&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="102"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Alarm Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Alarm Service&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_course.xml"
            line="112"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Button default&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Button default&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_custom.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Button cusstom&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Button cusstom&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_custom.xml"
            line="22"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Edit text of Activity&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:hint=&quot;Edit text of Activity&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="65"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Show fragment 1 or fragment 2 below&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Show fragment 1 or fragment 2 below&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="77"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Show Fragment 1&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Show Fragment 1&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="98"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Show Fragment 2&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Show Fragment 2&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_example.xml"
            line="109"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Start Training Course&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Start Training Course&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_main.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Start XXX&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Start XXX&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_main.xml"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Make notification&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Make notification&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_notification.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Make Custom notification&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Make Custom notification&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_notification.xml"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Count value is: &quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Count value is: &quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Start unbounded service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Start unbounded service&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="28"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Stop unbounded service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Stop unbounded service&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="39"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Start bounded service local&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Start bounded service local&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Stop bounded service local&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Stop bounded service local&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="61"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Start bounded service messenger&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Start bounded service messenger&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="72"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Stop bounded service messenger&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Stop bounded service messenger&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="83"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Start bounded service AIDL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Start bounded service AIDL&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="94"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Stop bounded service AIDL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Stop bounded service AIDL&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_service.xml"
            line="105"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Add Prefs&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Add Prefs&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Get Prefs&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Get Prefs&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml"
            line="34"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Value: &quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Value: &quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_share_prefs.xml"
            line="45"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Snooze Activity&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Snooze Activity&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_snooze.xml"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Thread&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Thread&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="16"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Thread count: &quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Thread count: &quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="29"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;0&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="41"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Async&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Async&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="53"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;AsyncTask count: &quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;AsyncTask count: &quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="66"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;0&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="79"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Executor&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Executor&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="102"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Test Future Concurrent&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Test Future Concurrent&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_thread.xml"
            line="113"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Button 11111 1111111 11111&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Button 11111 1111111 11111&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="30"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Button 222222&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Button 222222&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="49"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Button 3&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Button 3&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="70"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;But 1&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;But 1&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="82"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;But 2&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;But 2&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="94"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;But 3&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;But 3&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="105"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;But 7&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;But 7&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="116"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;But 4&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;But 4&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="128"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;But 5&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;But 5&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="139"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;But 6&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;But 6&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="151"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Button frame&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Button frame&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="180"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Frame Layout here, background grey&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Frame Layout here, background grey&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\activity_view_layout.xml"
            line="188"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Name goes here&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:hint=&quot;Name goes here&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\file_row.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Bottom Fragment&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Bottom Fragment&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_bottom.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Content of EditText:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Content of EditText:&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_bottom.xml"
            line="23"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Content is shown here&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:hint=&quot;Content is shown here&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_bottom.xml"
            line="35"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Fragment 1-1-1&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Fragment 1-1-1&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_one.xml"
            line="11"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Top Fragment&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Top Fragment&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Edit text here&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:hint=&quot;Edit text here&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_top.xml"
            line="23"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Fragment 2-2-2&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Fragment 2-2-2&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\fragment_two.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Title&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Title&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Activity&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Activity&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Broadcast&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Broadcast&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Service&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Service&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\notification_layout.xml"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;include layout here&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;include layout here&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\view_layout_include.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Button include&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Button include&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\AndroidStudioProjects\AndroidTraining\app\src\main\res\layout\view_layout_include.xml"
            line="23"
            column="9"/>
    </issue>

</issues>
